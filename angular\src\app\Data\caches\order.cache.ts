import { Injectable } from "@angular/core";
import { OrderDto } from "../../proxy/dtos/direct-data";
import { OrderService } from "../../proxy/services";
import { LocalCache } from "../local-cache";
import { OrderRecord } from "../records/order.record";
import { OrderUpdateService } from "../update-services/order-update.service";


@Injectable({
  providedIn: 'root'
})
export class OrderCache extends LocalCache<OrderRecord, OrderDto> {
  public processDto(dto: OrderDto): OrderRecord {
    return new OrderRecord(dto, this.cacheType);
  }
  public constructor(
    orderService: OrderService,
    orderUpdateService: OrderUpdateService,
  ) {
    super("Order", false, orderService, orderUpdateService);
  }
}

"use strict";(self.webpackChunkUniversalPortal=self.webpackChunkUniversalPortal||[]).push([[738],{7738:(Jf,Va,O)=>{O.r(Va),O.d(Va,{GraphMenuModule:()=>Qf});var $r=O(3887),Gr=O(7945),La=O(4710),c=O(3953),fe=O(7),V=O(9417);function te(i){return i+.5|0}const vt=(i,n,t)=>Math.max(Math.min(i,t),n);function pe(i){return vt(te(2.55*i),0,255)}function Rt(i){return vt(te(255*i),0,255)}function yt(i){return vt(te(i/2.55)/100,0,1)}function Ba(i){return vt(te(100*i),0,100)}const ht={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Ti=[..."0123456789ABCDEF"],Xr=i=>Ti[15&i],Kr=i=>Ti[(240&i)>>4]+Ti[15&i],We=i=>(240&i)>>4==(15&i);const to=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function za(i,n,t){const e=n*Math.min(t,1-t),a=(s,r=(s+i/30)%12)=>t-e*Math.max(Math.min(r-3,9-r,1),-1);return[a(0),a(8),a(4)]}function eo(i,n,t){const e=(a,s=(a+i/60)%6)=>t-t*n*Math.max(Math.min(s,4-s,1),0);return[e(5),e(3),e(1)]}function io(i,n,t){const e=za(i,1,.5);let a;for(n+t>1&&(a=1/(n+t),n*=a,t*=a),a=0;a<3;a++)e[a]*=1-n-t,e[a]+=n;return e}function Ii(i){const t=i.r/255,e=i.g/255,a=i.b/255,s=Math.max(t,e,a),r=Math.min(t,e,a),o=(s+r)/2;let l,d,h;return s!==r&&(h=s-r,d=o>.5?h/(2-s-r):h/(s+r),l=function ao(i,n,t,e,a){return i===a?(n-t)/e+(n<t?6:0):n===a?(t-i)/e+2:(i-n)/e+4}(t,e,a,h,s),l=60*l+.5),[0|l,d||0,o]}function Pi(i,n,t,e){return(Array.isArray(n)?i(n[0],n[1],n[2]):i(n,t,e)).map(Rt)}function Fi(i,n,t){return Pi(za,i,n,t)}function Na(i){return(i%360+360)%360}const ja={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Ha={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let Ue;const uo=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,Oi=i=>i<=.0031308?12.92*i:1.055*Math.pow(i,1/2.4)-.055,ee=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function $e(i,n,t){if(i){let e=Ii(i);e[n]=Math.max(0,Math.min(e[n]+e[n]*t,0===n?360:1)),e=Fi(e),i.r=e[0],i.g=e[1],i.b=e[2]}}function Ya(i,n){return i&&Object.assign(n||{},i)}function Wa(i){var n={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(n={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(n.a=Rt(i[3]))):(n=Ya(i,{r:0,g:0,b:0,a:1})).a=Rt(n.a),n}function mo(i){return"r"===i.charAt(0)?function fo(i){const n=uo.exec(i);let e,a,s,t=255;if(n){if(n[7]!==e){const r=+n[7];t=n[8]?pe(r):vt(255*r,0,255)}return e=+n[1],a=+n[3],s=+n[5],e=255&(n[2]?pe(e):vt(e,0,255)),a=255&(n[4]?pe(a):vt(a,0,255)),s=255&(n[6]?pe(s):vt(s,0,255)),{r:e,g:a,b:s,a:t}}}(i):function ro(i){const n=to.exec(i);let e,t=255;if(!n)return;n[5]!==e&&(t=n[6]?pe(+n[5]):Rt(+n[5]));const a=Na(+n[2]),s=+n[3]/100,r=+n[4]/100;return e="hwb"===n[1]?function no(i,n,t){return Pi(io,i,n,t)}(a,s,r):"hsv"===n[1]?function so(i,n,t){return Pi(eo,i,n,t)}(a,s,r):Fi(a,s,r),{r:e[0],g:e[1],b:e[2],a:t}}(i)}class ie{constructor(n){if(n instanceof ie)return n;const t=typeof n;let e;"object"===t?e=Wa(n):"string"===t&&(e=function Qr(i){var t,n=i.length;return"#"===i[0]&&(4===n||5===n?t={r:255&17*ht[i[1]],g:255&17*ht[i[2]],b:255&17*ht[i[3]],a:5===n?17*ht[i[4]]:255}:(7===n||9===n)&&(t={r:ht[i[1]]<<4|ht[i[2]],g:ht[i[3]]<<4|ht[i[4]],b:ht[i[5]]<<4|ht[i[6]],a:9===n?ht[i[7]]<<4|ht[i[8]]:255})),t}(n)||function ho(i){Ue||(Ue=function co(){const i={},n=Object.keys(Ha),t=Object.keys(ja);let e,a,s,r,o;for(e=0;e<n.length;e++){for(r=o=n[e],a=0;a<t.length;a++)s=t[a],o=o.replace(s,ja[s]);s=parseInt(Ha[r],16),i[o]=[s>>16&255,s>>8&255,255&s]}return i}(),Ue.transparent=[0,0,0,0]);const n=Ue[i.toLowerCase()];return n&&{r:n[0],g:n[1],b:n[2],a:4===n.length?n[3]:255}}(n)||mo(n)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var n=Ya(this._rgb);return n&&(n.a=yt(n.a)),n}set rgb(n){this._rgb=Wa(n)}rgbString(){return this._valid?function po(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${yt(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}(this._rgb):void 0}hexString(){return this._valid?function Zr(i){var n=(i=>We(i.r)&&We(i.g)&&We(i.b)&&We(i.a))(i)?Xr:Kr;return i?"#"+n(i.r)+n(i.g)+n(i.b)+((i,n)=>i<255?n(i):"")(i.a,n):void 0}(this._rgb):void 0}hslString(){return this._valid?function lo(i){if(!i)return;const n=Ii(i),t=n[0],e=Ba(n[1]),a=Ba(n[2]);return i.a<255?`hsla(${t}, ${e}%, ${a}%, ${yt(i.a)})`:`hsl(${t}, ${e}%, ${a}%)`}(this._rgb):void 0}mix(n,t){if(n){const e=this.rgb,a=n.rgb;let s;const r=t===s?.5:t,o=2*r-1,l=e.a-a.a,d=((o*l==-1?o:(o+l)/(1+o*l))+1)/2;s=1-d,e.r=255&d*e.r+s*a.r+.5,e.g=255&d*e.g+s*a.g+.5,e.b=255&d*e.b+s*a.b+.5,e.a=r*e.a+(1-r)*a.a,this.rgb=e}return this}interpolate(n,t){return n&&(this._rgb=function go(i,n,t){const e=ee(yt(i.r)),a=ee(yt(i.g)),s=ee(yt(i.b));return{r:Rt(Oi(e+t*(ee(yt(n.r))-e))),g:Rt(Oi(a+t*(ee(yt(n.g))-a))),b:Rt(Oi(s+t*(ee(yt(n.b))-s))),a:i.a+t*(n.a-i.a)}}(this._rgb,n._rgb,t)),this}clone(){return new ie(this.rgb)}alpha(n){return this._rgb.a=Rt(n),this}clearer(n){return this._rgb.a*=1-n,this}greyscale(){const n=this._rgb,t=te(.3*n.r+.59*n.g+.11*n.b);return n.r=n.g=n.b=t,this}opaquer(n){return this._rgb.a*=1+n,this}negate(){const n=this._rgb;return n.r=255-n.r,n.g=255-n.g,n.b=255-n.b,this}lighten(n){return $e(this._rgb,2,n),this}darken(n){return $e(this._rgb,2,-n),this}saturate(n){return $e(this._rgb,1,n),this}desaturate(n){return $e(this._rgb,1,-n),this}rotate(n){return function oo(i,n){var t=Ii(i);t[0]=Na(t[0]+n),t=Fi(t),i.r=t[0],i.g=t[1],i.b=t[2]}(this._rgb,n),this}}function xt(){}const _o=(()=>{let i=0;return()=>i++})();function R(i){return null==i}function j(i){if(Array.isArray&&Array.isArray(i))return!0;const n=Object.prototype.toString.call(i);return"[object"===n.slice(0,7)&&"Array]"===n.slice(-6)}function P(i){return null!==i&&"[object Object]"===Object.prototype.toString.call(i)}function G(i){return("number"==typeof i||i instanceof Number)&&isFinite(+i)}function ot(i,n){return G(i)?i:n}function A(i,n){return typeof i>"u"?n:i}const Ua=(i,n)=>"string"==typeof i&&i.endsWith("%")?parseFloat(i)/100*n:+i;function B(i,n,t){if(i&&"function"==typeof i.call)return i.apply(t,n)}function L(i,n,t,e){let a,s,r;if(j(i))if(s=i.length,e)for(a=s-1;a>=0;a--)n.call(t,i[a],a);else for(a=0;a<s;a++)n.call(t,i[a],a);else if(P(i))for(r=Object.keys(i),s=r.length,a=0;a<s;a++)n.call(t,i[r[a]],r[a])}function Ge(i,n){let t,e,a,s;if(!i||!n||i.length!==n.length)return!1;for(t=0,e=i.length;t<e;++t)if(a=i[t],s=n[t],a.datasetIndex!==s.datasetIndex||a.index!==s.index)return!1;return!0}function Xe(i){if(j(i))return i.map(Xe);if(P(i)){const n=Object.create(null),t=Object.keys(i),e=t.length;let a=0;for(;a<e;++a)n[t[a]]=Xe(i[t[a]]);return n}return i}function $a(i){return-1===["__proto__","prototype","constructor"].indexOf(i)}function vo(i,n,t,e){if(!$a(i))return;const a=n[i],s=t[i];P(a)&&P(s)?pt(a,s,e):n[i]=Xe(s)}function pt(i,n,t){const e=j(n)?n:[n],a=e.length;if(!P(i))return i;const s=(t=t||{}).merger||vo;let r;for(let o=0;o<a;++o){if(r=e[o],!P(r))continue;const l=Object.keys(r);for(let d=0,h=l.length;d<h;++d)s(l[d],i,r,t)}return i}function ge(i,n){return pt(i,n,{merger:yo})}function yo(i,n,t){if(!$a(i))return;const e=n[i],a=t[i];P(e)&&P(a)?ge(e,a):Object.prototype.hasOwnProperty.call(n,i)||(n[i]=Xe(a))}const Ga={"":i=>i,x:i=>i.x,y:i=>i.y};function Et(i,n){return(Ga[n]||(Ga[n]=function Do(i){const n=function xo(i){const n=i.split("."),t=[];let e="";for(const a of n)e+=a,e.endsWith("\\")?e=e.slice(0,-1)+".":(t.push(e),e="");return t}(i);return t=>{for(const e of n){if(""===e)break;t=t&&t[e]}return t}}(n)))(i)}function Vi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const me=i=>typeof i<"u",Tt=i=>"function"==typeof i,Xa=(i,n)=>{if(i.size!==n.size)return!1;for(const t of i)if(!n.has(t))return!1;return!0},H=Math.PI,Y=2*H,Mo=Y+H,Ke=Number.POSITIVE_INFINITY,Co=H/180,X=H/2,zt=H/4,Ka=2*H/3,It=Math.log10,gt=Math.sign;function _e(i,n,t){return Math.abs(i-n)<t}function qa(i){const n=Math.round(i);i=_e(i,n,i/1e3)?n:i;const t=Math.pow(10,Math.floor(It(i))),e=i/t;return(e<=1?1:e<=2?2:e<=5?5:10)*t}function ae(i){return!function So(i){return"symbol"==typeof i||"object"==typeof i&&null!==i&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function Qa(i,n,t){let e,a,s;for(e=0,a=i.length;e<a;e++)s=i[e][t],isNaN(s)||(n.min=Math.min(n.min,s),n.max=Math.max(n.max,s))}function ut(i){return i*(H/180)}function Li(i){return i*(180/H)}function Ja(i){if(!G(i))return;let n=1,t=0;for(;Math.round(i*n)/n!==i;)n*=10,t++;return t}function Za(i,n){const t=n.x-i.x,e=n.y-i.y,a=Math.sqrt(t*t+e*e);let s=Math.atan2(e,t);return s<-.5*H&&(s+=Y),{angle:s,distance:a}}function Bi(i,n){return Math.sqrt(Math.pow(n.x-i.x,2)+Math.pow(n.y-i.y,2))}function Ro(i,n){return(i-n+Mo)%Y-H}function lt(i){return(i%Y+Y)%Y}function be(i,n,t,e){const a=lt(i),s=lt(n),r=lt(t),o=lt(s-a),l=lt(r-a),d=lt(a-s),h=lt(a-r);return a===s||a===r||e&&s===r||o>l&&d<h}function Q(i,n,t){return Math.max(n,Math.min(t,i))}function Dt(i,n,t,e=1e-6){return i>=Math.min(n,t)-e&&i<=Math.max(n,t)+e}function zi(i,n,t){t=t||(r=>i[r]<n);let s,e=i.length-1,a=0;for(;e-a>1;)s=a+e>>1,t(s)?a=s:e=s;return{lo:a,hi:e}}const kt=(i,n,t,e)=>zi(i,t,e?a=>{const s=i[a][n];return s<t||s===t&&i[a+1][n]===t}:a=>i[a][n]<t),To=(i,n,t)=>zi(i,t,e=>i[e][n]>=t),tn=["push","pop","shift","splice","unshift"];function en(i,n){const t=i._chartjs;if(!t)return;const e=t.listeners,a=e.indexOf(n);-1!==a&&e.splice(a,1),!(e.length>0)&&(tn.forEach(s=>{delete i[s]}),delete i._chartjs)}function an(i){const n=new Set(i);return n.size===i.length?i:Array.from(n)}const nn=typeof window>"u"?function(i){return i()}:window.requestAnimationFrame;function sn(i,n){let t=[],e=!1;return function(...a){t=a,e||(e=!0,nn.call(window,()=>{e=!1,i.apply(n,t)}))}}const Ni=i=>"start"===i?"left":"end"===i?"right":"center",et=(i,n,t)=>"start"===i?n:"end"===i?t:(n+t)/2;function rn(i,n,t){const e=n.length;let a=0,s=e;if(i._sorted){const{iScale:r,vScale:o,_parsed:l}=i,d=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,h=r.axis,{min:u,max:f,minDefined:p,maxDefined:g}=r.getUserBounds();if(p){if(a=Math.min(kt(l,h,u).lo,t?e:kt(n,h,r.getPixelForValue(u)).lo),d){const m=l.slice(0,a+1).reverse().findIndex(_=>!R(_[o.axis]));a-=Math.max(0,m)}a=Q(a,0,e-1)}if(g){let m=Math.max(kt(l,r.axis,f,!0).hi+1,t?0:kt(n,h,r.getPixelForValue(f),!0).hi+1);if(d){const _=l.slice(m-1).findIndex(v=>!R(v[o.axis]));m+=Math.max(0,_)}s=Q(m,a,e)-a}else s=e-a}return{start:a,count:s}}function on(i){const{xScale:n,yScale:t,_scaleRanges:e}=i,a={xmin:n.min,xmax:n.max,ymin:t.min,ymax:t.max};if(!e)return i._scaleRanges=a,!0;const s=e.xmin!==n.min||e.xmax!==n.max||e.ymin!==t.min||e.ymax!==t.max;return Object.assign(e,a),s}const qe=i=>0===i||1===i,ln=(i,n,t)=>-Math.pow(2,10*(i-=1))*Math.sin((i-n)*Y/t),cn=(i,n,t)=>Math.pow(2,-10*i)*Math.sin((i-n)*Y/t)+1,ve={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>1-Math.cos(i*X),easeOutSine:i=>Math.sin(i*X),easeInOutSine:i=>-.5*(Math.cos(H*i)-1),easeInExpo:i=>0===i?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>1===i?1:1-Math.pow(2,-10*i),easeInOutExpo:i=>qe(i)?i:i<.5?.5*Math.pow(2,10*(2*i-1)):.5*(2-Math.pow(2,-10*(2*i-1))),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>qe(i)?i:ln(i,.075,.3),easeOutElastic:i=>qe(i)?i:cn(i,.075,.3),easeInOutElastic:i=>qe(i)?i:i<.5?.5*ln(2*i,.1125,.45):.5+.5*cn(2*i-1,.1125,.45),easeInBack:i=>i*i*(2.70158*i-1.70158),easeOutBack:i=>(i-=1)*i*(2.70158*i********)+1,easeInOutBack(i){let n=1.70158;return(i/=.5)<1?i*i*((1+(n*=1.525))*i-n)*.5:.5*((i-=2)*i*((1+(n*=1.525))*i+n)+2)},easeInBounce:i=>1-ve.easeOutBounce(1-i),easeOutBounce:i=>i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375,easeInOutBounce:i=>i<.5?.5*ve.easeInBounce(2*i):.5*ve.easeOutBounce(2*i-1)+.5};function ji(i){if(i&&"object"==typeof i){const n=i.toString();return"[object CanvasPattern]"===n||"[object CanvasGradient]"===n}return!1}function dn(i){return ji(i)?i:new ie(i)}function Hi(i){return ji(i)?i:new ie(i).saturate(.5).darken(.1).hexString()}const Vo=["x","y","borderWidth","radius","tension"],Lo=["color","borderColor","backgroundColor"],hn=new Map;function ye(i,n,t){return function No(i,n){n=n||{};const t=i+JSON.stringify(n);let e=hn.get(t);return e||(e=new Intl.NumberFormat(i,n),hn.set(t,e)),e}(n,t).format(i)}const un={values:i=>j(i)?i:""+i,numeric(i,n,t){if(0===i)return"0";const e=this.chart.options.locale;let a,s=i;if(t.length>1){const d=Math.max(Math.abs(t[0].value),Math.abs(t[t.length-1].value));(d<1e-4||d>1e15)&&(a="scientific"),s=function jo(i,n){let t=n.length>3?n[2].value-n[1].value:n[1].value-n[0].value;return Math.abs(t)>=1&&i!==Math.floor(i)&&(t=i-Math.floor(i)),t}(i,t)}const r=It(Math.abs(s)),o=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:a,minimumFractionDigits:o,maximumFractionDigits:o};return Object.assign(l,this.options.ticks.format),ye(i,e,l)},logarithmic(i,n,t){if(0===i)return"0";const e=t[n].significand||i/Math.pow(10,Math.floor(It(i)));return[1,2,3,5,10,15].includes(e)||n>.8*t.length?un.numeric.call(this,i,n,t):""}};var Qe={formatters:un};const Nt=Object.create(null),Yi=Object.create(null);function xe(i,n){if(!n)return i;const t=n.split(".");for(let e=0,a=t.length;e<a;++e){const s=t[e];i=i[s]||(i[s]=Object.create(null))}return i}function Wi(i,n,t){return"string"==typeof n?pt(xe(i,n),t):pt(xe(i,""),n)}class Yo{constructor(n,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=e=>e.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(e,a)=>Hi(a.backgroundColor),this.hoverBorderColor=(e,a)=>Hi(a.borderColor),this.hoverColor=(e,a)=>Hi(a.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(n),this.apply(t)}set(n,t){return Wi(this,n,t)}get(n){return xe(this,n)}describe(n,t){return Wi(Yi,n,t)}override(n,t){return Wi(Nt,n,t)}route(n,t,e,a){const s=xe(this,n),r=xe(this,e),o="_"+t;Object.defineProperties(s,{[o]:{value:s[t],writable:!0},[t]:{enumerable:!0,get(){const l=this[o],d=r[a];return P(l)?Object.assign({},d,l):A(l,d)},set(l){this[o]=l}}})}apply(n){n.forEach(t=>t(this))}}var W=new Yo({_scriptable:i=>!i.startsWith("on"),_indexable:i=>"events"!==i,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function Bo(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:n=>"onProgress"!==n&&"onComplete"!==n&&"fn"!==n}),i.set("animations",{colors:{type:"color",properties:Lo},numbers:{type:"number",properties:Vo}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:n=>0|n}}}})},function zo(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function Ho(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(n,t)=>t.lineWidth,tickColor:(n,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Qe.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:n=>!n.startsWith("before")&&!n.startsWith("after")&&"callback"!==n&&"parser"!==n,_indexable:n=>"borderDash"!==n&&"tickBorderDash"!==n&&"dash"!==n}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:n=>"backdropPadding"!==n&&"callback"!==n,_indexable:n=>"backdropPadding"!==n})}]);function Je(i,n,t,e,a){let s=n[a];return s||(s=n[a]=i.measureText(a).width,t.push(a)),s>e&&(e=s),e}function Uo(i,n,t,e){let a=(e=e||{}).data=e.data||{},s=e.garbageCollect=e.garbageCollect||[];e.font!==n&&(a=e.data={},s=e.garbageCollect=[],e.font=n),i.save(),i.font=n;let r=0;const o=t.length;let l,d,h,u,f;for(l=0;l<o;l++)if(u=t[l],null==u||j(u)){if(j(u))for(d=0,h=u.length;d<h;d++)f=u[d],null!=f&&!j(f)&&(r=Je(i,a,s,r,f))}else r=Je(i,a,s,r,u);i.restore();const p=s.length/2;if(p>t.length){for(l=0;l<p;l++)delete a[s[l]];s.splice(0,p)}return r}function jt(i,n,t){const e=i.currentDevicePixelRatio,a=0!==t?Math.max(t/2,.5):0;return Math.round((n-a)*e)/e+a}function fn(i,n){!n&&!i||((n=n||i.getContext("2d")).save(),n.resetTransform(),n.clearRect(0,0,i.width,i.height),n.restore())}function Ui(i,n,t,e){pn(i,n,t,e,null)}function pn(i,n,t,e,a){let s,r,o,l,d,h,u,f;const p=n.pointStyle,g=n.rotation,m=n.radius;let _=(g||0)*Co;if(p&&"object"==typeof p&&(s=p.toString(),"[object HTMLImageElement]"===s||"[object HTMLCanvasElement]"===s))return i.save(),i.translate(t,e),i.rotate(_),i.drawImage(p,-p.width/2,-p.height/2,p.width,p.height),void i.restore();if(!(isNaN(m)||m<=0)){switch(i.beginPath(),p){default:a?i.ellipse(t,e,a/2,m,0,0,Y):i.arc(t,e,m,0,Y),i.closePath();break;case"triangle":h=a?a/2:m,i.moveTo(t+Math.sin(_)*h,e-Math.cos(_)*m),_+=Ka,i.lineTo(t+Math.sin(_)*h,e-Math.cos(_)*m),_+=Ka,i.lineTo(t+Math.sin(_)*h,e-Math.cos(_)*m),i.closePath();break;case"rectRounded":d=.516*m,l=m-d,r=Math.cos(_+zt)*l,u=Math.cos(_+zt)*(a?a/2-d:l),o=Math.sin(_+zt)*l,f=Math.sin(_+zt)*(a?a/2-d:l),i.arc(t-u,e-o,d,_-H,_-X),i.arc(t+f,e-r,d,_-X,_),i.arc(t+u,e+o,d,_,_+X),i.arc(t-f,e+r,d,_+X,_+H),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*m,h=a?a/2:l,i.rect(t-h,e-l,2*h,2*l);break}_+=zt;case"rectRot":u=Math.cos(_)*(a?a/2:m),r=Math.cos(_)*m,o=Math.sin(_)*m,f=Math.sin(_)*(a?a/2:m),i.moveTo(t-u,e-o),i.lineTo(t+f,e-r),i.lineTo(t+u,e+o),i.lineTo(t-f,e+r),i.closePath();break;case"crossRot":_+=zt;case"cross":u=Math.cos(_)*(a?a/2:m),r=Math.cos(_)*m,o=Math.sin(_)*m,f=Math.sin(_)*(a?a/2:m),i.moveTo(t-u,e-o),i.lineTo(t+u,e+o),i.moveTo(t+f,e-r),i.lineTo(t-f,e+r);break;case"star":u=Math.cos(_)*(a?a/2:m),r=Math.cos(_)*m,o=Math.sin(_)*m,f=Math.sin(_)*(a?a/2:m),i.moveTo(t-u,e-o),i.lineTo(t+u,e+o),i.moveTo(t+f,e-r),i.lineTo(t-f,e+r),_+=zt,u=Math.cos(_)*(a?a/2:m),r=Math.cos(_)*m,o=Math.sin(_)*m,f=Math.sin(_)*(a?a/2:m),i.moveTo(t-u,e-o),i.lineTo(t+u,e+o),i.moveTo(t+f,e-r),i.lineTo(t-f,e+r);break;case"line":r=a?a/2:Math.cos(_)*m,o=Math.sin(_)*m,i.moveTo(t-r,e-o),i.lineTo(t+r,e+o);break;case"dash":i.moveTo(t,e),i.lineTo(t+Math.cos(_)*(a?a/2:m),e+Math.sin(_)*m);break;case!1:i.closePath()}i.fill(),n.borderWidth>0&&i.stroke()}}function Mt(i,n,t){return t=t||.5,!n||i&&i.x>n.left-t&&i.x<n.right+t&&i.y>n.top-t&&i.y<n.bottom+t}function Ze(i,n){i.save(),i.beginPath(),i.rect(n.left,n.top,n.right-n.left,n.bottom-n.top),i.clip()}function ti(i){i.restore()}function $o(i,n,t,e,a){if(!n)return i.lineTo(t.x,t.y);if("middle"===a){const s=(n.x+t.x)/2;i.lineTo(s,n.y),i.lineTo(s,t.y)}else"after"===a!=!!e?i.lineTo(n.x,t.y):i.lineTo(t.x,n.y);i.lineTo(t.x,t.y)}function Go(i,n,t,e){if(!n)return i.lineTo(t.x,t.y);i.bezierCurveTo(e?n.cp1x:n.cp2x,e?n.cp1y:n.cp2y,e?t.cp2x:t.cp1x,e?t.cp2y:t.cp1y,t.x,t.y)}function Ko(i,n,t,e,a){if(a.strikethrough||a.underline){const s=i.measureText(e),r=n-s.actualBoundingBoxLeft,o=n+s.actualBoundingBoxRight,d=t+s.actualBoundingBoxDescent,h=a.strikethrough?(t-s.actualBoundingBoxAscent+d)/2:d;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=a.decorationWidth||2,i.moveTo(r,h),i.lineTo(o,h),i.stroke()}}function qo(i,n){const t=i.fillStyle;i.fillStyle=n.color,i.fillRect(n.left,n.top,n.width,n.height),i.fillStyle=t}function Ht(i,n,t,e,a,s={}){const r=j(n)?n:[n],o=s.strokeWidth>0&&""!==s.strokeColor;let l,d;for(i.save(),i.font=a.string,function Xo(i,n){n.translation&&i.translate(n.translation[0],n.translation[1]),R(n.rotation)||i.rotate(n.rotation),n.color&&(i.fillStyle=n.color),n.textAlign&&(i.textAlign=n.textAlign),n.textBaseline&&(i.textBaseline=n.textBaseline)}(i,s),l=0;l<r.length;++l)d=r[l],s.backdrop&&qo(i,s.backdrop),o&&(s.strokeColor&&(i.strokeStyle=s.strokeColor),R(s.strokeWidth)||(i.lineWidth=s.strokeWidth),i.strokeText(d,t,e,s.maxWidth)),i.fillText(d,t,e,s.maxWidth),Ko(i,t,e,d,s),e+=Number(a.lineHeight);i.restore()}function De(i,n){const{x:t,y:e,w:a,h:s,radius:r}=n;i.arc(t+r.topLeft,e+r.topLeft,r.topLeft,1.5*H,H,!0),i.lineTo(t,e+s-r.bottomLeft),i.arc(t+r.bottomLeft,e+s-r.bottomLeft,r.bottomLeft,H,X,!0),i.lineTo(t+a-r.bottomRight,e+s),i.arc(t+a-r.bottomRight,e+s-r.bottomRight,r.bottomRight,X,0,!0),i.lineTo(t+a,e+r.topRight),i.arc(t+a-r.topRight,e+r.topRight,r.topRight,0,-X,!0),i.lineTo(t+r.topLeft,e)}const Qo=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Jo=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Zo(i,n){const t=(""+i).match(Qo);if(!t||"normal"===t[1])return 1.2*n;switch(i=+t[2],t[3]){case"px":return i;case"%":i/=100}return n*i}const tl=i=>+i||0;function $i(i,n){const t={},e=P(n),a=e?Object.keys(n):n,s=P(i)?e?r=>A(i[r],i[n[r]]):r=>i[r]:()=>i;for(const r of a)t[r]=tl(s(r));return t}function gn(i){return $i(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Yt(i){return $i(i,["topLeft","topRight","bottomLeft","bottomRight"])}function J(i){const n=gn(i);return n.width=n.left+n.right,n.height=n.top+n.bottom,n}function q(i,n){let t=A((i=i||{}).size,(n=n||W.font).size);"string"==typeof t&&(t=parseInt(t,10));let e=A(i.style,n.style);e&&!(""+e).match(Jo)&&(console.warn('Invalid font style specified: "'+e+'"'),e=void 0);const a={family:A(i.family,n.family),lineHeight:Zo(A(i.lineHeight,n.lineHeight),t),size:t,style:e,weight:A(i.weight,n.weight),string:""};return a.string=function Wo(i){return!i||R(i.size)||R(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}(a),a}function U(i,n,t,e){let s,r,o,a=!0;for(s=0,r=i.length;s<r;++s)if(o=i[s],void 0!==o&&(void 0!==n&&"function"==typeof o&&(o=o(n),a=!1),void 0!==t&&j(o)&&(o=o[t%o.length],a=!1),void 0!==o))return e&&!a&&(e.cacheable=!1),o}function Pt(i,n){return Object.assign(Object.create(i),n)}function Gi(i,n=[""],t,e,a=(()=>i[0])){const s=t||i;typeof e>"u"&&(e=yn("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:s,_fallback:e,_getTarget:a,override:o=>Gi([o,...i],n,s,e)};return new Proxy(r,{deleteProperty:(o,l)=>(delete o[l],delete o._keys,delete i[0][l],!0),get:(o,l)=>_n(o,l,()=>function cl(i,n,t,e){let a;for(const s of n)if(a=yn(il(s,i),t),typeof a<"u")return Xi(i,a)?Ki(t,e,i,a):a}(l,n,i,o)),getOwnPropertyDescriptor:(o,l)=>Reflect.getOwnPropertyDescriptor(o._scopes[0],l),getPrototypeOf:()=>Reflect.getPrototypeOf(i[0]),has:(o,l)=>xn(o).includes(l),ownKeys:o=>xn(o),set(o,l,d){const h=o._storage||(o._storage=a());return o[l]=h[l]=d,delete o._keys,!0}})}function ne(i,n,t,e){const a={_cacheable:!1,_proxy:i,_context:n,_subProxy:t,_stack:new Set,_descriptors:mn(i,e),setContext:s=>ne(i,s,t,e),override:s=>ne(i.override(s),n,t,e)};return new Proxy(a,{deleteProperty:(s,r)=>(delete s[r],delete i[r],!0),get:(s,r,o)=>_n(s,r,()=>function al(i,n,t){const{_proxy:e,_context:a,_subProxy:s,_descriptors:r}=i;let o=e[n];return Tt(o)&&r.isScriptable(n)&&(o=function nl(i,n,t,e){const{_proxy:a,_context:s,_subProxy:r,_stack:o}=t;if(o.has(i))throw new Error("Recursion detected: "+Array.from(o).join("->")+"->"+i);o.add(i);let l=n(s,r||e);return o.delete(i),Xi(i,l)&&(l=Ki(a._scopes,a,i,l)),l}(n,o,i,t)),j(o)&&o.length&&(o=function sl(i,n,t,e){const{_proxy:a,_context:s,_subProxy:r,_descriptors:o}=t;if(typeof s.index<"u"&&e(i))return n[s.index%n.length];if(P(n[0])){const l=n,d=a._scopes.filter(h=>h!==l);n=[];for(const h of l){const u=Ki(d,a,i,h);n.push(ne(u,s,r&&r[i],o))}}return n}(n,o,i,r.isIndexable)),Xi(n,o)&&(o=ne(o,a,s&&s[n],r)),o}(s,r,o)),getOwnPropertyDescriptor:(s,r)=>s._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r),getPrototypeOf:()=>Reflect.getPrototypeOf(i),has:(s,r)=>Reflect.has(i,r),ownKeys:()=>Reflect.ownKeys(i),set:(s,r,o)=>(i[r]=o,delete s[r],!0)})}function mn(i,n={scriptable:!0,indexable:!0}){const{_scriptable:t=n.scriptable,_indexable:e=n.indexable,_allKeys:a=n.allKeys}=i;return{allKeys:a,scriptable:t,indexable:e,isScriptable:Tt(t)?t:()=>t,isIndexable:Tt(e)?e:()=>e}}const il=(i,n)=>i?i+Vi(n):n,Xi=(i,n)=>P(n)&&"adapters"!==i&&(null===Object.getPrototypeOf(n)||n.constructor===Object);function _n(i,n,t){if(Object.prototype.hasOwnProperty.call(i,n)||"constructor"===n)return i[n];const e=t();return i[n]=e,e}function bn(i,n,t){return Tt(i)?i(n,t):i}const rl=(i,n)=>!0===i?n:"string"==typeof i?Et(n,i):void 0;function ol(i,n,t,e,a){for(const s of n){const r=rl(t,s);if(r){i.add(r);const o=bn(r._fallback,t,a);if(typeof o<"u"&&o!==t&&o!==e)return o}else if(!1===r&&typeof e<"u"&&t!==e)return null}return!1}function Ki(i,n,t,e){const a=n._rootScopes,s=bn(n._fallback,t,e),r=[...i,...a],o=new Set;o.add(e);let l=vn(o,r,t,s||t,e);return!(null===l||typeof s<"u"&&s!==t&&(l=vn(o,r,s,l,e),null===l))&&Gi(Array.from(o),[""],a,s,()=>function ll(i,n,t){const e=i._getTarget();n in e||(e[n]={});const a=e[n];return j(a)&&P(t)?t:a||{}}(n,t,e))}function vn(i,n,t,e,a){for(;t;)t=ol(i,n,t,e,a);return t}function yn(i,n){for(const t of n){if(!t)continue;const e=t[i];if(typeof e<"u")return e}}function xn(i){let n=i._keys;return n||(n=i._keys=function dl(i){const n=new Set;for(const t of i)for(const e of Object.keys(t).filter(a=>!a.startsWith("_")))n.add(e);return Array.from(n)}(i._scopes)),n}function Dn(i,n,t,e){const{iScale:a}=i,{key:s="r"}=this._parsing,r=new Array(e);let o,l,d,h;for(o=0,l=e;o<l;++o)d=o+t,h=n[d],r[o]={r:a.parse(Et(h,s),d)};return r}const hl=Number.EPSILON||1e-14,se=(i,n)=>n<i.length&&!i[n].skip&&i[n],kn=i=>"x"===i?"y":"x";function ul(i,n,t,e){const a=i.skip?n:i,s=n,r=t.skip?n:t,o=Bi(s,a),l=Bi(r,s);let d=o/(o+l),h=l/(o+l);d=isNaN(d)?0:d,h=isNaN(h)?0:h;const u=e*d,f=e*h;return{previous:{x:s.x-u*(r.x-a.x),y:s.y-u*(r.y-a.y)},next:{x:s.x+f*(r.x-a.x),y:s.y+f*(r.y-a.y)}}}function ei(i,n,t){return Math.max(Math.min(i,t),n)}function _l(i,n,t,e,a){let s,r,o,l;if(n.spanGaps&&(i=i.filter(d=>!d.skip)),"monotone"===n.cubicInterpolationMode)!function gl(i,n="x"){const t=kn(n),e=i.length,a=Array(e).fill(0),s=Array(e);let r,o,l,d=se(i,0);for(r=0;r<e;++r)if(o=l,l=d,d=se(i,r+1),l){if(d){const h=d[n]-l[n];a[r]=0!==h?(d[t]-l[t])/h:0}s[r]=o?d?gt(a[r-1])!==gt(a[r])?0:(a[r-1]+a[r])/2:a[r-1]:a[r]}(function fl(i,n,t){const e=i.length;let a,s,r,o,l,d=se(i,0);for(let h=0;h<e-1;++h)if(l=d,d=se(i,h+1),l&&d){if(_e(n[h],0,hl)){t[h]=t[h+1]=0;continue}a=t[h]/n[h],s=t[h+1]/n[h],o=Math.pow(a,2)+Math.pow(s,2),!(o<=9)&&(r=3/Math.sqrt(o),t[h]=a*r*n[h],t[h+1]=s*r*n[h])}})(i,a,s),function pl(i,n,t="x"){const e=kn(t),a=i.length;let s,r,o,l=se(i,0);for(let d=0;d<a;++d){if(r=o,o=l,l=se(i,d+1),!o)continue;const h=o[t],u=o[e];r&&(s=(h-r[t])/3,o[`cp1${t}`]=h-s,o[`cp1${e}`]=u-s*n[d]),l&&(s=(l[t]-h)/3,o[`cp2${t}`]=h+s,o[`cp2${e}`]=u+s*n[d])}}(i,s,n)}(i,a);else{let d=e?i[i.length-1]:i[0];for(s=0,r=i.length;s<r;++s)o=i[s],l=ul(d,o,i[Math.min(s+1,r-(e?0:1))%r],n.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,d=o}n.capBezierPoints&&function ml(i,n){let t,e,a,s,r,o=Mt(i[0],n);for(t=0,e=i.length;t<e;++t)r=s,s=o,o=t<e-1&&Mt(i[t+1],n),s&&(a=i[t],r&&(a.cp1x=ei(a.cp1x,n.left,n.right),a.cp1y=ei(a.cp1y,n.top,n.bottom)),o&&(a.cp2x=ei(a.cp2x,n.left,n.right),a.cp2y=ei(a.cp2y,n.top,n.bottom)))}(i,t)}function qi(){return typeof window<"u"&&typeof document<"u"}function Qi(i){let n=i.parentNode;return n&&"[object ShadowRoot]"===n.toString()&&(n=n.host),n}function ii(i,n,t){let e;return"string"==typeof i?(e=parseInt(i,10),-1!==i.indexOf("%")&&(e=e/100*n.parentNode[t])):e=i,e}const ai=i=>i.ownerDocument.defaultView.getComputedStyle(i,null),vl=["top","right","bottom","left"];function Wt(i,n,t){const e={};t=t?"-"+t:"";for(let a=0;a<4;a++){const s=vl[a];e[s]=parseFloat(i[n+"-"+s+t])||0}return e.width=e.left+e.right,e.height=e.top+e.bottom,e}const yl=(i,n,t)=>(i>0||n>0)&&(!t||!t.shadowRoot);function Ut(i,n){if("native"in i)return i;const{canvas:t,currentDevicePixelRatio:e}=n,a=ai(t),s="border-box"===a.boxSizing,r=Wt(a,"padding"),o=Wt(a,"border","width"),{x:l,y:d,box:h}=function xl(i,n){const t=i.touches,e=t&&t.length?t[0]:i,{offsetX:a,offsetY:s}=e;let o,l,r=!1;if(yl(a,s,i.target))o=a,l=s;else{const d=n.getBoundingClientRect();o=e.clientX-d.left,l=e.clientY-d.top,r=!0}return{x:o,y:l,box:r}}(i,t),u=r.left+(h&&o.left),f=r.top+(h&&o.top);let{width:p,height:g}=n;return s&&(p-=r.width+o.width,g-=r.height+o.height),{x:Math.round((l-u)/p*t.width/e),y:Math.round((d-f)/g*t.height/e)}}const ni=i=>Math.round(10*i)/10;function Mn(i,n,t){const e=n||1,a=Math.floor(i.height*e),s=Math.floor(i.width*e);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(t||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),(i.currentDevicePixelRatio!==e||r.height!==a||r.width!==s)&&(i.currentDevicePixelRatio=e,r.height=a,r.width=s,i.ctx.setTransform(e,0,0,e,0,0),!0)}const Ml=function(){let i=!1;try{const n={get passive(){return i=!0,!1}};qi()&&(window.addEventListener("test",null,n),window.removeEventListener("test",null,n))}catch{}return i}();function Cn(i,n){const t=function bl(i,n){return ai(i).getPropertyValue(n)}(i,n),e=t&&t.match(/^(\d+)(\.\d+)?px$/);return e?+e[1]:void 0}function $t(i,n,t,e){return{x:i.x+t*(n.x-i.x),y:i.y+t*(n.y-i.y)}}function Cl(i,n,t,e){return{x:i.x+t*(n.x-i.x),y:"middle"===e?t<.5?i.y:n.y:"after"===e?t<1?i.y:n.y:t>0?n.y:i.y}}function wl(i,n,t,e){const a={x:i.cp2x,y:i.cp2y},s={x:n.cp1x,y:n.cp1y},r=$t(i,a,t),o=$t(a,s,t),l=$t(s,n,t),d=$t(r,o,t),h=$t(o,l,t);return $t(d,h,t)}function re(i,n,t){return i?function(i,n){return{x:t=>i+i+n-t,setWidth(t){n=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}}(n,t):{x:i=>i,setWidth(i){},textAlign:i=>i,xPlus:(i,n)=>i+n,leftForLtr:(i,n)=>i}}function wn(i,n){let t,e;("ltr"===n||"rtl"===n)&&(t=i.canvas.style,e=[t.getPropertyValue("direction"),t.getPropertyPriority("direction")],t.setProperty("direction",n,"important"),i.prevTextDirection=e)}function Sn(i,n){void 0!==n&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",n[0],n[1]))}function An(i){return"angle"===i?{between:be,compare:Ro,normalize:lt}:{between:Dt,compare:(n,t)=>n-t,normalize:n=>n}}function Rn({start:i,end:n,count:t,loop:e,style:a}){return{start:i%t,end:n%t,loop:e&&(n-i+1)%t==0,style:a}}function En(i,n,t){if(!t)return[i];const{property:e,start:a,end:s}=t,r=n.length,{compare:o,between:l,normalize:d}=An(e),{start:h,end:u,loop:f,style:p}=function Rl(i,n,t){const{property:e,start:a,end:s}=t,{between:r,normalize:o}=An(e),l=n.length;let f,p,{start:d,end:h,loop:u}=i;if(u){for(d+=l,h+=l,f=0,p=l;f<p&&r(o(n[d%l][e]),a,s);++f)d--,h--;d%=l,h%=l}return h<d&&(h+=l),{start:d,end:h,loop:u,style:i.style}}(i,n,t),g=[];let v,b,x,m=!1,_=null;for(let M=h,w=h;M<=u;++M)b=n[M%r],!b.skip&&(v=d(b[e]),v!==x&&(m=l(v,a,s),null===_&&(m||l(a,x,v)&&0!==o(a,x))&&(_=0===o(v,a)?M:w),null!==_&&(!m||0===o(s,v)||l(s,x,v))&&(g.push(Rn({start:_,end:M,loop:f,count:r,style:p})),_=null),w=M,x=v));return null!==_&&g.push(Rn({start:_,end:u,loop:f,count:r,style:p})),g}function Tn(i,n){const t=[],e=i.segments;for(let a=0;a<e.length;a++){const s=En(e[a],i.points,n);s.length&&t.push(...s)}return t}function Pn(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function Fl(i,n){if(!n)return!1;const t=[],e=function(a,s){return ji(s)?(t.includes(s)||t.push(s),t.indexOf(s)):s};return JSON.stringify(i,e)!==JSON.stringify(n,e)}function si(i,n,t){return i.options.clip?i[t]:n[t]}function Fn(i,n){const t=n._clip;if(t.disabled)return!1;const e=function Ol(i,n){const{xScale:t,yScale:e}=i;return t&&e?{left:si(t,n,"left"),right:si(t,n,"right"),top:si(e,n,"top"),bottom:si(e,n,"bottom")}:n}(n,i.chartArea);return{left:!1===t.left?0:e.left-(!0===t.left?0:t.left),right:!1===t.right?i.width:e.right+(!0===t.right?0:t.right),top:!1===t.top?0:e.top-(!0===t.top?0:t.top),bottom:!1===t.bottom?i.height:e.bottom+(!0===t.bottom?0:t.bottom)}}class Vl{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(n,t,e,a){const r=t.duration;t.listeners[a].forEach(o=>o({chart:n,initial:t.initial,numSteps:r,currentStep:Math.min(e-t.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=nn.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(n=Date.now()){let t=0;this._charts.forEach((e,a)=>{if(!e.running||!e.items.length)return;const s=e.items;let l,r=s.length-1,o=!1;for(;r>=0;--r)l=s[r],l._active?(l._total>e.duration&&(e.duration=l._total),l.tick(n),o=!0):(s[r]=s[s.length-1],s.pop());o&&(a.draw(),this._notify(a,e,n,"progress")),s.length||(e.running=!1,this._notify(a,e,n,"complete"),e.initial=!1),t+=s.length}),this._lastDate=n,0===t&&(this._running=!1)}_getAnims(n){const t=this._charts;let e=t.get(n);return e||(e={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(n,e)),e}listen(n,t,e){this._getAnims(n).listeners[t].push(e)}add(n,t){!t||!t.length||this._getAnims(n).items.push(...t)}has(n){return this._getAnims(n).items.length>0}start(n){const t=this._charts.get(n);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((e,a)=>Math.max(e,a._duration),0),this._refresh())}running(n){if(!this._running)return!1;const t=this._charts.get(n);return!(!t||!t.running||!t.items.length)}stop(n){const t=this._charts.get(n);if(!t||!t.items.length)return;const e=t.items;let a=e.length-1;for(;a>=0;--a)e[a].cancel();t.items=[],this._notify(n,t,Date.now(),"complete")}remove(n){return this._charts.delete(n)}}var Ct=new Vl;const On="transparent",Ll={boolean:(i,n,t)=>t>.5?n:i,color(i,n,t){const e=dn(i||On),a=e.valid&&dn(n||On);return a&&a.valid?a.mix(e,t).hexString():n},number:(i,n,t)=>i+(n-i)*t};class Bl{constructor(n,t,e,a){const s=t[e];a=U([n.to,a,s,n.from]);const r=U([n.from,s,a]);this._active=!0,this._fn=n.fn||Ll[n.type||typeof r],this._easing=ve[n.easing]||ve.linear,this._start=Math.floor(Date.now()+(n.delay||0)),this._duration=this._total=Math.floor(n.duration),this._loop=!!n.loop,this._target=t,this._prop=e,this._from=r,this._to=a,this._promises=void 0}active(){return this._active}update(n,t,e){if(this._active){this._notify(!1);const a=this._target[this._prop],s=e-this._start,r=this._duration-s;this._start=e,this._duration=Math.floor(Math.max(r,n.duration)),this._total+=s,this._loop=!!n.loop,this._to=U([n.to,t,a,n.from]),this._from=U([n.from,a,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(n){const t=n-this._start,e=this._duration,a=this._prop,s=this._from,r=this._loop,o=this._to;let l;if(this._active=s!==o&&(r||t<e),!this._active)return this._target[a]=o,void this._notify(!0);t<0?this._target[a]=s:(l=t/e%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[a]=this._fn(s,o,l))}wait(){const n=this._promises||(this._promises=[]);return new Promise((t,e)=>{n.push({res:t,rej:e})})}_notify(n){const t=n?"res":"rej",e=this._promises||[];for(let a=0;a<e.length;a++)e[a][t]()}}class Vn{constructor(n,t){this._chart=n,this._properties=new Map,this.configure(t)}configure(n){if(!P(n))return;const t=Object.keys(W.animation),e=this._properties;Object.getOwnPropertyNames(n).forEach(a=>{const s=n[a];if(!P(s))return;const r={};for(const o of t)r[o]=s[o];(j(s.properties)&&s.properties||[a]).forEach(o=>{(o===a||!e.has(o))&&e.set(o,r)})})}_animateOptions(n,t){const e=t.options,a=function Nl(i,n){if(!n)return;let t=i.options;if(t)return t.$shared&&(i.options=t=Object.assign({},t,{$shared:!1,$animations:{}})),t;i.options=n}(n,e);if(!a)return[];const s=this._createAnimations(a,e);return e.$shared&&function zl(i,n){const t=[],e=Object.keys(n);for(let a=0;a<e.length;a++){const s=i[e[a]];s&&s.active()&&t.push(s.wait())}return Promise.all(t)}(n.options.$animations,e).then(()=>{n.options=e},()=>{}),s}_createAnimations(n,t){const e=this._properties,a=[],s=n.$animations||(n.$animations={}),r=Object.keys(t),o=Date.now();let l;for(l=r.length-1;l>=0;--l){const d=r[l];if("$"===d.charAt(0))continue;if("options"===d){a.push(...this._animateOptions(n,t));continue}const h=t[d];let u=s[d];const f=e.get(d);if(u){if(f&&u.active()){u.update(f,h,o);continue}u.cancel()}f&&f.duration?(s[d]=u=new Bl(f,n,d,h),a.push(u)):n[d]=h}return a}update(n,t){if(0===this._properties.size)return void Object.assign(n,t);const e=this._createAnimations(n,t);return e.length?(Ct.add(this._chart,e),!0):void 0}}function Ln(i,n){const t=i&&i.options||{},e=t.reverse,a=void 0===t.min?n:0,s=void 0===t.max?n:0;return{start:e?s:a,end:e?a:s}}function Bn(i,n){const t=[],e=i._getSortedDatasetMetas(n);let a,s;for(a=0,s=e.length;a<s;++a)t.push(e[a].index);return t}function zn(i,n,t,e={}){const a=i.keys,s="single"===e.mode;let r,o,l,d;if(null===n)return;let h=!1;for(r=0,o=a.length;r<o;++r){if(l=+a[r],l===t){if(h=!0,e.all)continue;break}d=i.values[l],G(d)&&(s||0===n||gt(n)===gt(d))&&(n+=d)}return h||e.all?n:0}function Ji(i,n){const t=i&&i.options.stacked;return t||void 0===t&&void 0!==n.stack}function $l(i,n,t){const e=i[n]||(i[n]={});return e[t]||(e[t]={})}function Nn(i,n,t,e){for(const a of n.getMatchingVisibleMetas(e).reverse()){const s=i[a.index];if(t&&s>0||!t&&s<0)return a.index}return null}function jn(i,n){const{chart:t,_cachedMeta:e}=i,a=t._stacks||(t._stacks={}),{iScale:s,vScale:r,index:o}=e,l=s.axis,d=r.axis,h=function Wl(i,n,t){return`${i.id}.${n.id}.${t.stack||t.type}`}(s,r,e),u=n.length;let f;for(let p=0;p<u;++p){const g=n[p],{[l]:m,[d]:_}=g;f=(g._stacks||(g._stacks={}))[d]=$l(a,h,m),f[o]=_,f._top=Nn(f,r,!0,e.type),f._bottom=Nn(f,r,!1,e.type),(f._visualValues||(f._visualValues={}))[o]=_}}function Zi(i,n){const t=i.scales;return Object.keys(t).filter(e=>t[e].axis===n).shift()}function ke(i,n){const t=i.controller.index,e=i.vScale&&i.vScale.axis;if(e){n=n||i._parsed;for(const a of n){const s=a._stacks;if(!s||void 0===s[e]||void 0===s[e][t])return;delete s[e][t],void 0!==s[e]._visualValues&&void 0!==s[e]._visualValues[t]&&delete s[e]._visualValues[t]}}}const ta=i=>"reset"===i||"none"===i,Hn=(i,n)=>n?i:Object.assign({},i);let Ft=(()=>class i{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ji(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&ke(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,a=this.getDataset(),s=(f,p,g,m)=>"x"===f?p:"r"===f?m:g,r=e.xAxisID=A(a.xAxisID,Zi(t,"x")),o=e.yAxisID=A(a.yAxisID,Zi(t,"y")),l=e.rAxisID=A(a.rAxisID,Zi(t,"r")),d=e.indexAxis,h=e.iAxisID=s(d,r,o,l),u=e.vAxisID=s(d,o,r,l);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(l),e.iScale=this.getScaleForId(h),e.vScale=this.getScaleForId(u)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&en(this._data,this),t._stacked&&ke(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),a=this._data;if(P(e))this._data=function Yl(i,n){const{iScale:t,vScale:e}=n,a="x"===t.axis?"x":"y",s="x"===e.axis?"x":"y",r=Object.keys(i),o=new Array(r.length);let l,d,h;for(l=0,d=r.length;l<d;++l)h=r[l],o[l]={[a]:h,[s]:i[h]};return o}(e,this._cachedMeta);else if(a!==e){if(a){en(a,this);const s=this._cachedMeta;ke(s),s._parsed=[]}e&&Object.isExtensible(e)&&function Po(i,n){i._chartjs?i._chartjs.listeners.push(n):(Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[n]}}),tn.forEach(t=>{const e="_onData"+Vi(t),a=i[t];Object.defineProperty(i,t,{configurable:!0,enumerable:!1,value(...s){const r=a.apply(this,s);return i._chartjs.listeners.forEach(o=>{"function"==typeof o[e]&&o[e](...s)}),r}})}))}(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,a=this.getDataset();let s=!1;this._dataCheck();const r=e._stacked;e._stacked=Ji(e.vScale,e),e.stack!==a.stack&&(s=!0,ke(e),e.stack=a.stack),this._resyncElements(t),(s||r!==e._stacked)&&(jn(this,e._parsed),e._stacked=Ji(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),a=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(a,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:a,_data:s}=this,{iScale:r,_stacked:o}=a,l=r.axis;let u,f,p,d=0===t&&e===s.length||a._sorted,h=t>0&&a._parsed[t-1];if(!1===this._parsing)a._parsed=s,a._sorted=!0,p=s;else{p=j(s[t])?this.parseArrayData(a,s,t,e):P(s[t])?this.parseObjectData(a,s,t,e):this.parsePrimitiveData(a,s,t,e);const g=()=>null===f[l]||h&&f[l]<h[l];for(u=0;u<e;++u)a._parsed[u+t]=f=p[u],d&&(g()&&(d=!1),h=f);a._sorted=d}o&&jn(this,p)}parsePrimitiveData(t,e,a,s){const{iScale:r,vScale:o}=t,l=r.axis,d=o.axis,h=r.getLabels(),u=r===o,f=new Array(s);let p,g,m;for(p=0,g=s;p<g;++p)m=p+a,f[p]={[l]:u||r.parse(h[m],m),[d]:o.parse(e[m],m)};return f}parseArrayData(t,e,a,s){const{xScale:r,yScale:o}=t,l=new Array(s);let d,h,u,f;for(d=0,h=s;d<h;++d)u=d+a,f=e[u],l[d]={x:r.parse(f[0],u),y:o.parse(f[1],u)};return l}parseObjectData(t,e,a,s){const{xScale:r,yScale:o}=t,{xAxisKey:l="x",yAxisKey:d="y"}=this._parsing,h=new Array(s);let u,f,p,g;for(u=0,f=s;u<f;++u)p=u+a,g=e[p],h[u]={x:r.parse(Et(g,l),p),y:o.parse(Et(g,d),p)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,a){const r=this._cachedMeta,o=e[t.axis];return zn({keys:Bn(this.chart,!0),values:e._stacks[t.axis]._visualValues},o,r.index,{mode:a})}updateRangeFromParsed(t,e,a,s){const r=a[e.axis];let o=null===r?NaN:r;const l=s&&a._stacks[e.axis];s&&l&&(s.values=l,o=zn(s,r,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){const a=this._cachedMeta,s=a._parsed,r=a._sorted&&t===a.iScale,o=s.length,l=this._getOtherScale(t),d=((i,n,t)=>i&&!n.hidden&&n._stacked&&{keys:Bn(this.chart,!0),values:null})(e,a),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:f}=function Ul(i){const{min:n,max:t,minDefined:e,maxDefined:a}=i.getUserBounds();return{min:e?n:Number.NEGATIVE_INFINITY,max:a?t:Number.POSITIVE_INFINITY}}(l);let p,g;function m(){g=s[p];const _=g[l.axis];return!G(g[t.axis])||u>_||f<_}for(p=0;p<o&&(m()||(this.updateRangeFromParsed(h,t,g,d),!r));++p);if(r)for(p=o-1;p>=0;--p)if(!m()){this.updateRangeFromParsed(h,t,g,d);break}return h}getAllParsedValues(t){const e=this._cachedMeta._parsed,a=[];let s,r,o;for(s=0,r=e.length;s<r;++s)o=e[s][t.axis],G(o)&&a.push(o);return a}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,a=e.iScale,s=e.vScale,r=this.getParsed(t);return{label:a?""+a.getLabelForValue(r[a.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=function Hl(i){let n,t,e,a;return P(i)?(n=i.top,t=i.right,e=i.bottom,a=i.left):n=t=e=a=i,{top:n,right:t,bottom:e,left:a,disabled:!1===i}}(A(this.options.clip,function jl(i,n,t){if(!1===t)return!1;const e=Ln(i,t),a=Ln(n,t);return{top:a.end,right:e.end,bottom:a.start,left:e.start}}(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,a=this._cachedMeta,s=a.data||[],r=this.chart.chartArea,o=[],l=this._drawStart||0,d=this._drawCount||s.length-l,h=this.options.drawActiveElementsOnTop;let u;for(a.dataset&&a.dataset.draw(t,r,l,d),u=l;u<l+d;++u){const f=s[u];f.hidden||(f.active&&h?o.push(f):f.draw(t,r))}for(u=0;u<o.length;++u)o[u].draw(t,r)}getStyle(t,e){const a=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(a):this.resolveDataElementOptions(t||0,a)}getContext(t,e,a){const s=this.getDataset();let r;if(t>=0&&t<this._cachedMeta.data.length){const o=this._cachedMeta.data[t];r=o.$context||(o.$context=function Xl(i,n,t){return Pt(i,{active:!1,dataIndex:n,parsed:void 0,raw:void 0,element:t,index:n,mode:"default",type:"data"})}(this.getContext(),t,o)),r.parsed=this.getParsed(t),r.raw=s.data[t],r.index=r.dataIndex=t}else r=this.$context||(this.$context=function Gl(i,n){return Pt(i,{active:!1,dataset:void 0,datasetIndex:n,index:n,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),r.dataset=s,r.index=r.datasetIndex=this.index;return r.active=!!e,r.mode=a,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",a){const s="active"===e,r=this._cachedDataOpts,o=t+"-"+e,l=r[o],d=this.enableOptionSharing&&me(a);if(l)return Hn(l,d);const h=this.chart.config,u=h.datasetElementScopeKeys(this._type,t),f=s?[`${t}Hover`,"hover",t,""]:[t,""],p=h.getOptionScopes(this.getDataset(),u),g=Object.keys(W.elements[t]),_=h.resolveNamedOptions(p,g,()=>this.getContext(a,s,e),f);return _.$shared&&(_.$shared=d,r[o]=Object.freeze(Hn(_,d))),_}_resolveAnimations(t,e,a){const s=this.chart,r=this._cachedDataOpts,o=`animation-${e}`,l=r[o];if(l)return l;let d;if(!1!==s.options.animation){const u=this.chart.config,f=u.datasetAnimationScopeKeys(this._type,e),p=u.getOptionScopes(this.getDataset(),f);d=u.createResolver(p,this.getContext(t,a,e))}const h=new Vn(s,d&&d.animations);return d&&d._cacheable&&(r[o]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||ta(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const a=this.resolveDataElementOptions(t,e),s=this._sharedOptions,r=this.getSharedOptions(a),o=this.includeOptions(e,r)||r!==s;return this.updateSharedOptions(r,e,a),{sharedOptions:r,includeOptions:o}}updateElement(t,e,a,s){ta(s)?Object.assign(t,a):this._resolveAnimations(e,s).update(t,a)}updateSharedOptions(t,e,a){t&&!ta(e)&&this._resolveAnimations(void 0,e).update(t,a)}_setStyle(t,e,a,s){t.active=s;const r=this.getStyle(e,s);this._resolveAnimations(e,a,s).update(t,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,a){this._setStyle(t,a,"active",!1)}setHoverStyle(t,e,a){this._setStyle(t,a,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,a=this._cachedMeta.data;for(const[l,d,h]of this._syncList)this[l](d,h);this._syncList=[];const s=a.length,r=e.length,o=Math.min(r,s);o&&this.parse(0,o),r>s?this._insertElements(s,r-s,t):r<s&&this._removeElements(r,s-r)}_insertElements(t,e,a=!0){const s=this._cachedMeta,r=s.data,o=t+e;let l;const d=h=>{for(h.length+=e,l=h.length-1;l>=o;l--)h[l]=h[l-e]};for(d(r),l=t;l<o;++l)r[l]=new this.dataElementType;this._parsing&&d(s._parsed),this.parse(t,e),a&&this.updateElements(r,t,e,"reset")}updateElements(t,e,a,s){}_removeElements(t,e){const a=this._cachedMeta;if(this._parsing){const s=a._parsed.splice(t,e);a._stacked&&ke(a,s)}a.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,a,s]=t;this[e](a,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const a=arguments.length-2;a&&this._sync(["_insertElements",t,a])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}})();function Ql(i){const n=i.iScale,t=function ql(i,n){if(!i._cache.$bar){const t=i.getMatchingVisibleMetas(n);let e=[];for(let a=0,s=t.length;a<s;a++)e=e.concat(t[a].controller.getAllParsedValues(i));i._cache.$bar=an(e.sort((a,s)=>a-s))}return i._cache.$bar}(n,i.type);let a,s,r,o,e=n._length;const l=()=>{32767===r||-32768===r||(me(o)&&(e=Math.min(e,Math.abs(r-o)||e)),o=r)};for(a=0,s=t.length;a<s;++a)r=n.getPixelForValue(t[a]),l();for(o=void 0,a=0,s=n.ticks.length;a<s;++a)r=n.getPixelForTick(a),l();return e}function Yn(i,n,t,e){return j(i)?function tc(i,n,t,e){const a=t.parse(i[0],e),s=t.parse(i[1],e),r=Math.min(a,s),o=Math.max(a,s);let l=r,d=o;Math.abs(r)>Math.abs(o)&&(l=o,d=r),n[t.axis]=d,n._custom={barStart:l,barEnd:d,start:a,end:s,min:r,max:o}}(i,n,t,e):n[t.axis]=t.parse(i,e),n}function Wn(i,n,t,e){const a=i.iScale,s=i.vScale,r=a.getLabels(),o=a===s,l=[];let d,h,u,f;for(d=t,h=t+e;d<h;++d)f=n[d],u={},u[a.axis]=o||a.parse(r[d],d),l.push(Yn(f,u,s,d));return l}function ea(i){return i&&void 0!==i.barStart&&void 0!==i.barEnd}function ac(i,n,t,e){let a=n.borderSkipped;const s={};if(!a)return void(i.borderSkipped=s);if(!0===a)return void(i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0});const{start:r,end:o,reverse:l,top:d,bottom:h}=function ic(i){let n,t,e,a,s;return i.horizontal?(n=i.base>i.x,t="left",e="right"):(n=i.base<i.y,t="bottom",e="top"),n?(a="end",s="start"):(a="start",s="end"),{start:t,end:e,reverse:n,top:a,bottom:s}}(i);"middle"===a&&t&&(i.enableBorderRadius=!0,(t._top||0)===e?a=d:(t._bottom||0)===e?a=h:(s[Un(h,r,o,l)]=!0,a=d)),s[Un(a,r,o,l)]=!0,i.borderSkipped=s}function Un(i,n,t,e){return e?(i=function nc(i,n,t){return i===n?t:i===t?n:i}(i,n,t),i=$n(i,t,n)):i=$n(i,n,t),i}function $n(i,n,t){return"start"===i?n:"end"===i?t:i}function sc(i,{inflateAmount:n},t){i.inflateAmount="auto"===n?1===t?.33:0:n}let rc=(()=>class i extends Ft{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,a,s){return Wn(t,e,a,s)}parseArrayData(t,e,a,s){return Wn(t,e,a,s)}parseObjectData(t,e,a,s){const{iScale:r,vScale:o}=t,{xAxisKey:l="x",yAxisKey:d="y"}=this._parsing,h="x"===r.axis?l:d,u="x"===o.axis?l:d,f=[];let p,g,m,_;for(p=a,g=a+s;p<g;++p)_=e[p],m={},m[r.axis]=r.parse(Et(_,h),p),f.push(Yn(Et(_,u),m,o,p));return f}updateRangeFromParsed(t,e,a,s){super.updateRangeFromParsed(t,e,a,s);const r=a._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:a,vScale:s}=e,r=this.getParsed(t),o=r._custom,l=ea(o)?"["+o.start+", "+o.end+"]":""+s.getLabelForValue(r[s.axis]);return{label:""+a.getLabelForValue(r[a.axis]),value:l}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,a,s){const r="reset"===s,{index:o,_cachedMeta:{vScale:l}}=this,d=l.getBasePixel(),h=l.isHorizontal(),u=this._getRuler(),{sharedOptions:f,includeOptions:p}=this._getSharedOptions(e,s);for(let g=e;g<e+a;g++){const m=this.getParsed(g),_=r||R(m[l.axis])?{base:d,head:d}:this._calculateBarValuePixels(g),v=this._calculateBarIndexPixels(g,u),b=(m._stacks||{})[l.axis],x={horizontal:h,base:_.base,enableBorderRadius:!b||ea(m._custom)||o===b._top||o===b._bottom,x:h?_.head:v.center,y:h?v.center:_.head,height:h?v.size:Math.abs(_.size),width:h?Math.abs(_.size):v.size};p&&(x.options=f||this.resolveDataElementOptions(g,t[g].active?"active":s));const D=x.options||t[g].options;ac(x,D,b,o),sc(x,D,u.ratio),this.updateElement(t[g],g,x,s)}}_getStacks(t,e){const{iScale:a}=this._cachedMeta,s=a.getMatchingVisibleMetas(this._type).filter(u=>u.controller.options.grouped),r=a.options.stacked,o=[],l=this._cachedMeta.controller.getParsed(e),d=l&&l[a.axis],h=u=>{const f=u._parsed.find(g=>g[a.axis]===d),p=f&&f[u.vScale.axis];if(R(p)||isNaN(p))return!0};for(const u of s)if((void 0===e||!h(u))&&((!1===r||-1===o.indexOf(u.stack)||void 0===r&&void 0===u.stack)&&o.push(u.stack),u.index===t))break;return o.length||o.push(void 0),o}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,a){const s=this._getStacks(t,a),r=void 0!==e?s.indexOf(e):-1;return-1===r?s.length-1:r}_getRuler(){const t=this.options,e=this._cachedMeta,a=e.iScale,s=[];let r,o;for(r=0,o=e.data.length;r<o;++r)s.push(a.getPixelForValue(this.getParsed(r)[a.axis],r));const l=t.barThickness;return{min:l||Ql(e),pixels:s,start:a._startPixel,end:a._endPixel,stackCount:this._getStackCount(),scale:a,grouped:t.grouped,ratio:l?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:a,index:s},options:{base:r,minBarLength:o}}=this,l=r||0,d=this.getParsed(t),h=d._custom,u=ea(h);let m,_,f=d[e.axis],p=0,g=a?this.applyStack(e,d,a):f;g!==f&&(p=g-f,g=f),u&&(f=h.barStart,g=h.barEnd-h.barStart,0!==f&&gt(f)!==gt(h.barEnd)&&(p=0),p+=f);const v=R(r)||u?p:r;let b=e.getPixelForValue(v);if(m=this.chart.getDataVisibility(t)?e.getPixelForValue(p+g):b,_=m-b,Math.abs(_)<o){_=function ec(i,n,t){return 0!==i?gt(i):(n.isHorizontal()?1:-1)*(n.min>=t?1:-1)}(_,e,l)*o,f===l&&(b-=_/2);const x=e.getPixelForDecimal(0),D=e.getPixelForDecimal(1),y=Math.min(x,D),k=Math.max(x,D);b=Math.max(Math.min(b,k),y),m=b+_,a&&!u&&(d._stacks[e.axis]._visualValues[s]=e.getValueForPixel(m)-e.getValueForPixel(b))}if(b===e.getPixelForValue(l)){const x=gt(_)*e.getLineWidthForValue(l)/2;b+=x,_-=x}return{size:_,base:b,head:m,center:m+_/2}}_calculateBarIndexPixels(t,e){const a=e.scale,s=this.options,r=s.skipNull,o=A(s.maxBarThickness,1/0);let l,d;if(e.grouped){const h=r?this._getStackCount(t):e.stackCount,u="flex"===s.barThickness?function Zl(i,n,t,e){const a=n.pixels,s=a[i];let r=i>0?a[i-1]:null,o=i<a.length-1?a[i+1]:null;const l=t.categoryPercentage;null===r&&(r=s-(null===o?n.end-n.start:o-s)),null===o&&(o=s+s-r);const d=s-(s-Math.min(r,o))/2*l;return{chunk:Math.abs(o-r)/2*l/e,ratio:t.barPercentage,start:d}}(t,e,s,h):function Jl(i,n,t,e){const a=t.barThickness;let s,r;return R(a)?(s=n.min*t.categoryPercentage,r=t.barPercentage):(s=a*e,r=1),{chunk:s/e,ratio:r,start:n.pixels[i]-s/2}}(t,e,s,h),f=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0);l=u.start+u.chunk*f+u.chunk/2,d=Math.min(o,u.chunk*u.ratio)}else l=a.getPixelForValue(this.getParsed(t)[a.axis],t),d=Math.min(o,e.min*e.ratio);return{base:l-d/2,head:l+d/2,center:l,size:d}}draw(){const t=this._cachedMeta,e=t.vScale,a=t.data,s=a.length;let r=0;for(;r<s;++r)null!==this.getParsed(r)[e.axis]&&!a[r].hidden&&a[r].draw(this._ctx)}})(),oc=(()=>class i extends Ft{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,a,s){const r=super.parsePrimitiveData(t,e,a,s);for(let o=0;o<r.length;o++)r[o]._custom=this.resolveDataElementOptions(o+a).radius;return r}parseArrayData(t,e,a,s){const r=super.parseArrayData(t,e,a,s);for(let o=0;o<r.length;o++)r[o]._custom=A(e[a+o][2],this.resolveDataElementOptions(o+a).radius);return r}parseObjectData(t,e,a,s){const r=super.parseObjectData(t,e,a,s);for(let o=0;o<r.length;o++){const l=e[a+o];r[o]._custom=A(l&&l.r&&+l.r,this.resolveDataElementOptions(o+a).radius)}return r}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let a=t.length-1;a>=0;--a)e=Math.max(e,t[a].size(this.resolveDataElementOptions(a))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,a=this.chart.data.labels||[],{xScale:s,yScale:r}=e,o=this.getParsed(t),l=s.getLabelForValue(o.x),d=r.getLabelForValue(o.y),h=o._custom;return{label:a[t]||"",value:"("+l+", "+d+(h?", "+h:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,a,s){const r="reset"===s,{iScale:o,vScale:l}=this._cachedMeta,{sharedOptions:d,includeOptions:h}=this._getSharedOptions(e,s),u=o.axis,f=l.axis;for(let p=e;p<e+a;p++){const g=t[p],m=!r&&this.getParsed(p),_={},v=_[u]=r?o.getPixelForDecimal(.5):o.getPixelForValue(m[u]),b=_[f]=r?l.getBasePixel():l.getPixelForValue(m[f]);_.skip=isNaN(v)||isNaN(b),h&&(_.options=d||this.resolveDataElementOptions(p,g.active?"active":s),r&&(_.options.radius=0)),this.updateElement(g,p,_,s)}}resolveDataElementOptions(t,e){const a=this.getParsed(t);let s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));const r=s.radius;return"active"!==e&&(s.radius=0),s.radius+=A(a&&a._custom,r),s}})(),ia=(()=>class i extends Ft{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:a,color:s}}=t.legend.options;return e.labels.map((r,o)=>{const d=t.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:d.backgroundColor,strokeStyle:d.borderColor,fontColor:s,lineWidth:d.borderWidth,pointStyle:a,hidden:!t.getDataVisibility(o),index:o}})}return[]}},onClick(t,e,a){a.chart.toggleDataVisibility(e.index),a.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const a=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=a;else{let o,l,r=d=>+a[d];if(P(a[t])){const{key:d="value"}=this._parsing;r=h=>+Et(a[h],d)}for(o=t,l=t+e;o<l;++o)s._parsed[o]=r(o)}}_getRotation(){return ut(this.options.rotation-90)}_getCircumference(){return ut(this.options.circumference)}_getRotationExtents(){let t=Y,e=-Y;for(let a=0;a<this.chart.data.datasets.length;++a)if(this.chart.isDatasetVisible(a)&&this.chart.getDatasetMeta(a).type===this._type){const s=this.chart.getDatasetMeta(a).controller,r=s._getRotation(),o=s._getCircumference();t=Math.min(t,r),e=Math.max(e,r+o)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:a}=e,s=this._cachedMeta,r=s.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,l=Math.max((Math.min(a.width,a.height)-o)/2,0),d=Math.min(((i,n)=>"string"==typeof i&&i.endsWith("%")?parseFloat(i)/100:+i/n)(this.options.cutout,l),1),h=this._getRingWeight(this.index),{circumference:u,rotation:f}=this._getRotationExtents(),{ratioX:p,ratioY:g,offsetX:m,offsetY:_}=function lc(i,n,t){let e=1,a=1,s=0,r=0;if(n<Y){const o=i,l=o+n,d=Math.cos(o),h=Math.sin(o),u=Math.cos(l),f=Math.sin(l),p=(x,D,y)=>be(x,o,l,!0)?1:Math.max(D,D*t,y,y*t),g=(x,D,y)=>be(x,o,l,!0)?-1:Math.min(D,D*t,y,y*t),m=p(0,d,u),_=p(X,h,f),v=g(H,d,u),b=g(H+X,h,f);e=(m-v)/2,a=(_-b)/2,s=-(m+v)/2,r=-(_+b)/2}return{ratioX:e,ratioY:a,offsetX:s,offsetY:r}}(f,u,d),x=Math.max(Math.min((a.width-o)/p,(a.height-o)/g)/2,0),D=Ua(this.options.radius,x),k=(D-Math.max(D*d,0))/this._getVisibleDatasetWeightTotal();this.offsetX=m*D,this.offsetY=_*D,s.total=this.calculateTotal(),this.outerRadius=D-k*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-k*h,0),this.updateElements(r,0,r.length,t)}_circumference(t,e){const a=this.options,s=this._cachedMeta,r=this._getCircumference();return e&&a.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*r/Y)}updateElements(t,e,a,s){const r="reset"===s,o=this.chart,l=o.chartArea,u=(l.left+l.right)/2,f=(l.top+l.bottom)/2,p=r&&o.options.animation.animateScale,g=p?0:this.innerRadius,m=p?0:this.outerRadius,{sharedOptions:_,includeOptions:v}=this._getSharedOptions(e,s);let x,b=this._getRotation();for(x=0;x<e;++x)b+=this._circumference(x,r);for(x=e;x<e+a;++x){const D=this._circumference(x,r),y=t[x],k={x:u+this.offsetX,y:f+this.offsetY,startAngle:b,endAngle:b+D,circumference:D,outerRadius:m,innerRadius:g};v&&(k.options=_||this.resolveDataElementOptions(x,y.active?"active":s)),b+=D,this.updateElement(y,x,k,s)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s,a=0;for(s=0;s<e.length;s++){const r=t._parsed[s];null!==r&&!isNaN(r)&&this.chart.getDataVisibility(s)&&!e[s].hidden&&(a+=Math.abs(r))}return a}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?Y*(Math.abs(t)/e):0}getLabelAndValue(t){const a=this.chart,s=a.data.labels||[],r=ye(this._cachedMeta._parsed[t],a.options.locale);return{label:s[t]||"",value:r}}getMaxBorderWidth(t){let e=0;const a=this.chart;let s,r,o,l,d;if(!t)for(s=0,r=a.data.datasets.length;s<r;++s)if(a.isDatasetVisible(s)){o=a.getDatasetMeta(s),t=o.data,l=o.controller;break}if(!t)return 0;for(s=0,r=t.length;s<r;++s)d=l.resolveDataElementOptions(s),"inner"!==d.borderAlign&&(e=Math.max(e,d.borderWidth||0,d.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let a=0,s=t.length;a<s;++a){const r=this.resolveDataElementOptions(a);e=Math.max(e,r.offset||0,r.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let a=0;a<t;++a)this.chart.isDatasetVisible(a)&&(e+=this._getRingWeight(a));return e}_getRingWeight(t){return Math.max(A(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}})(),cc=(()=>class i extends Ft{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:a,data:s=[],_dataset:r}=e,o=this.chart._animationsDisabled;let{start:l,count:d}=rn(e,s,o);this._drawStart=l,this._drawCount=d,on(e)&&(l=0,d=s.length),a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!r._decimated,a.points=s;const h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(a,void 0,{animated:!o,options:h},t),this.updateElements(s,l,d,t)}updateElements(t,e,a,s){const r="reset"===s,{iScale:o,vScale:l,_stacked:d,_dataset:h}=this._cachedMeta,{sharedOptions:u,includeOptions:f}=this._getSharedOptions(e,s),p=o.axis,g=l.axis,{spanGaps:m,segment:_}=this.options,v=ae(m)?m:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||r||"none"===s,x=e+a,D=t.length;let y=e>0&&this.getParsed(e-1);for(let k=0;k<D;++k){const C=t[k],M=b?C:{};if(k<e||k>=x){M.skip=!0;continue}const w=this.getParsed(k),I=R(w[g]),T=M[p]=o.getPixelForValue(w[p],k),F=M[g]=r||I?l.getBasePixel():l.getPixelForValue(d?this.applyStack(l,w,d):w[g],k);M.skip=isNaN(T)||isNaN(F)||I,M.stop=k>0&&Math.abs(w[p]-y[p])>v,_&&(M.parsed=w,M.raw=h.data[k]),f&&(M.options=u||this.resolveDataElementOptions(k,C.active?"active":s)),b||this.updateElement(C,k,M,s),y=w}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,a=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return a;const r=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(a,r,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}})(),Gn=(()=>class i extends Ft{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:a,color:s}}=t.legend.options;return e.labels.map((r,o)=>{const d=t.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:d.backgroundColor,strokeStyle:d.borderColor,fontColor:s,lineWidth:d.borderWidth,pointStyle:a,hidden:!t.getDataVisibility(o),index:o}})}return[]}},onClick(t,e,a){a.chart.toggleDataVisibility(e.index),a.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const a=this.chart,s=a.data.labels||[],r=ye(this._cachedMeta._parsed[t].r,a.options.locale);return{label:s[t]||"",value:r}}parseObjectData(t,e,a,s){return Dn.bind(this)(t,e,a,s)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return this._cachedMeta.data.forEach((a,s)=>{const r=this.getParsed(s).r;!isNaN(r)&&this.chart.getDataVisibility(s)&&(r<e.min&&(e.min=r),r>e.max&&(e.max=r))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,a=t.options,s=Math.min(e.right-e.left,e.bottom-e.top),r=Math.max(s/2,0),l=(r-Math.max(a.cutoutPercentage?r/100*a.cutoutPercentage:1,0))/t.getVisibleDatasetCount();this.outerRadius=r-l*this.index,this.innerRadius=this.outerRadius-l}updateElements(t,e,a,s){const r="reset"===s,o=this.chart,d=o.options.animation,h=this._cachedMeta.rScale,u=h.xCenter,f=h.yCenter,p=h.getIndexAngle(0)-.5*H;let m,g=p;const _=360/this.countVisibleElements();for(m=0;m<e;++m)g+=this._computeAngle(m,s,_);for(m=e;m<e+a;m++){const v=t[m];let b=g,x=g+this._computeAngle(m,s,_),D=o.getDataVisibility(m)?h.getDistanceFromCenterForValue(this.getParsed(m).r):0;g=x,r&&(d.animateScale&&(D=0),d.animateRotate&&(b=x=p));const y={x:u,y:f,innerRadius:0,outerRadius:D,startAngle:b,endAngle:x,options:this.resolveDataElementOptions(m,v.active?"active":s)};this.updateElement(v,m,y,s)}}countVisibleElements(){let e=0;return this._cachedMeta.data.forEach((a,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&e++}),e}_computeAngle(t,e,a){return this.chart.getDataVisibility(t)?ut(this.resolveDataElementOptions(t,e).angle||a):0}})();var dc=Object.freeze({__proto__:null,BarController:rc,BubbleController:oc,DoughnutController:ia,LineController:cc,PieController:(()=>class i extends ia{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}})(),PolarAreaController:Gn,RadarController:(()=>class i extends Ft{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const e=this._cachedMeta.vScale,a=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(a[e.axis])}}parseObjectData(t,e,a,s){return Dn.bind(this)(t,e,a,s)}update(t){const e=this._cachedMeta,a=e.dataset,s=e.data||[],r=e.iScale.getLabels();if(a.points=s,"resize"!==t){const o=this.resolveDatasetElementOptions(t);this.options.showLine||(o.borderWidth=0),this.updateElement(a,void 0,{_loop:!0,_fullLoop:r.length===s.length,options:o},t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,a,s){const r=this._cachedMeta.rScale,o="reset"===s;for(let l=e;l<e+a;l++){const d=t[l],h=this.resolveDataElementOptions(l,d.active?"active":s),u=r.getPointPositionForValue(l,this.getParsed(l).r),f=o?r.xCenter:u.x,p=o?r.yCenter:u.y,g={x:f,y:p,angle:u.angle,skip:isNaN(f)||isNaN(p),options:h};this.updateElement(d,l,g,s)}}})(),ScatterController:(()=>class i extends Ft{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const e=this._cachedMeta,a=this.chart.data.labels||[],{xScale:s,yScale:r}=e,o=this.getParsed(t),l=s.getLabelForValue(o.x),d=r.getLabelForValue(o.y);return{label:a[t]||"",value:"("+l+", "+d+")"}}update(t){const e=this._cachedMeta,{data:a=[]}=e,s=this.chart._animationsDisabled;let{start:r,count:o}=rn(e,a,s);if(this._drawStart=r,this._drawCount=o,on(e)&&(r=0,o=a.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:l,_dataset:d}=e;l._chart=this.chart,l._datasetIndex=this.index,l._decimated=!!d._decimated,l.points=a;const h=this.resolveDatasetElementOptions(t);h.segment=this.options.segment,this.updateElement(l,void 0,{animated:!s,options:h},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(a,r,o,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,a,s){const r="reset"===s,{iScale:o,vScale:l,_stacked:d,_dataset:h}=this._cachedMeta,u=this.resolveDataElementOptions(e,s),f=this.getSharedOptions(u),p=this.includeOptions(s,f),g=o.axis,m=l.axis,{spanGaps:_,segment:v}=this.options,b=ae(_)?_:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||r||"none"===s;let D=e>0&&this.getParsed(e-1);for(let y=e;y<e+a;++y){const k=t[y],C=this.getParsed(y),M=x?k:{},w=R(C[m]),I=M[g]=o.getPixelForValue(C[g],y),T=M[m]=r||w?l.getBasePixel():l.getPixelForValue(d?this.applyStack(l,C,d):C[m],y);M.skip=isNaN(I)||isNaN(T)||w,M.stop=y>0&&Math.abs(C[g]-D[g])>b,v&&(M.parsed=C,M.raw=h.data[y]),p&&(M.options=f||this.resolveDataElementOptions(y,k.active?"active":s)),x||this.updateElement(k,y,M,s),D=C}this.updateSharedOptions(f,s,u)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let l=0;for(let d=e.length-1;d>=0;--d)l=Math.max(l,e[d].size(this.resolveDataElementOptions(d))/2);return l>0&&l}const a=t.dataset,s=a.options&&a.options.borderWidth||0;if(!e.length)return s;const r=e[0].size(this.resolveDataElementOptions(0)),o=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(s,r,o)/2}})()});function Gt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class aa{static override(n){Object.assign(aa.prototype,n)}options;constructor(n){this.options=n||{}}init(){}formats(){return Gt()}parse(){return Gt()}format(){return Gt()}add(){return Gt()}diff(){return Gt()}startOf(){return Gt()}endOf(){return Gt()}}var hc__date=aa;function uc(i,n,t,e){const{controller:a,data:s,_sorted:r}=i,o=a._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(o&&n===o.axis&&"r"!==n&&r&&s.length){const d=o._reversePixels?To:kt;if(!e){const h=d(s,n,t);if(l){const{vScale:u}=a._cachedMeta,{_parsed:f}=i,p=f.slice(0,h.lo+1).reverse().findIndex(m=>!R(m[u.axis]));h.lo-=Math.max(0,p);const g=f.slice(h.hi).findIndex(m=>!R(m[u.axis]));h.hi+=Math.max(0,g)}return h}if(a._sharedOptions){const h=s[0],u="function"==typeof h.getRange&&h.getRange(n);if(u){const f=d(s,n,t-u),p=d(s,n,t+u);return{lo:f.lo,hi:p.hi}}}}return{lo:0,hi:s.length-1}}function Me(i,n,t,e,a){const s=i.getSortedVisibleDatasetMetas(),r=t[n];for(let o=0,l=s.length;o<l;++o){const{index:d,data:h}=s[o],{lo:u,hi:f}=uc(s[o],n,r,a);for(let p=u;p<=f;++p){const g=h[p];g.skip||e(g,d,p)}}}function na(i,n,t,e,a){const s=[];return!a&&!i.isPointInArea(n)||Me(i,t,n,function(o,l,d){!a&&!Mt(o,i.chartArea,0)||o.inRange(n.x,n.y,e)&&s.push({element:o,datasetIndex:l,index:d})},!0),s}function sa(i,n,t,e,a,s){return s||i.isPointInArea(n)?"r"!==t||e?function gc(i,n,t,e,a,s){let r=[];const o=function fc(i){const n=-1!==i.indexOf("x"),t=-1!==i.indexOf("y");return function(e,a){const s=n?Math.abs(e.x-a.x):0,r=t?Math.abs(e.y-a.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(r,2))}}(t);let l=Number.POSITIVE_INFINITY;return Me(i,t,n,function d(h,u,f){const p=h.inRange(n.x,n.y,a);if(e&&!p)return;const g=h.getCenterPoint(a);if(!s&&!i.isPointInArea(g)&&!p)return;const _=o(n,g);_<l?(r=[{element:h,datasetIndex:u,index:f}],l=_):_===l&&r.push({element:h,datasetIndex:u,index:f})}),r}(i,n,t,e,a,s):function pc(i,n,t,e){let a=[];return Me(i,t,n,function s(r,o,l){const{startAngle:d,endAngle:h}=r.getProps(["startAngle","endAngle"],e),{angle:u}=Za(r,{x:n.x,y:n.y});be(u,d,h)&&a.push({element:r,datasetIndex:o,index:l})}),a}(i,n,t,a):[]}function Xn(i,n,t,e,a){const s=[],r="x"===t?"inXRange":"inYRange";let o=!1;return Me(i,t,n,(l,d,h)=>{l[r]&&l[r](n[t],a)&&(s.push({element:l,datasetIndex:d,index:h}),o=o||l.inRange(n.x,n.y,a))}),e&&!o?[]:s}var mc={evaluateInteractionItems:Me,modes:{index(i,n,t,e){const a=Ut(n,i),s=t.axis||"x",r=t.includeInvisible||!1,o=t.intersect?na(i,a,s,e,r):sa(i,a,s,!1,e,r),l=[];return o.length?(i.getSortedVisibleDatasetMetas().forEach(d=>{const h=o[0].index,u=d.data[h];u&&!u.skip&&l.push({element:u,datasetIndex:d.index,index:h})}),l):[]},dataset(i,n,t,e){const a=Ut(n,i),s=t.axis||"xy",r=t.includeInvisible||!1;let o=t.intersect?na(i,a,s,e,r):sa(i,a,s,!1,e,r);if(o.length>0){const l=o[0].datasetIndex,d=i.getDatasetMeta(l).data;o=[];for(let h=0;h<d.length;++h)o.push({element:d[h],datasetIndex:l,index:h})}return o},point:(i,n,t,e)=>na(i,Ut(n,i),t.axis||"xy",e,t.includeInvisible||!1),nearest:(i,n,t,e)=>sa(i,Ut(n,i),t.axis||"xy",t.intersect,e,t.includeInvisible||!1),x:(i,n,t,e)=>Xn(i,Ut(n,i),"x",t.intersect,e),y:(i,n,t,e)=>Xn(i,Ut(n,i),"y",t.intersect,e)}};const Kn=["left","top","right","bottom"];function Ce(i,n){return i.filter(t=>t.pos===n)}function qn(i,n){return i.filter(t=>-1===Kn.indexOf(t.pos)&&t.box.axis===n)}function we(i,n){return i.sort((t,e)=>{const a=n?e:t,s=n?t:e;return a.weight===s.weight?a.index-s.index:a.weight-s.weight})}function Qn(i,n,t,e){return Math.max(i[t],n[t])+Math.max(i[e],n[e])}function Jn(i,n){i.top=Math.max(i.top,n.top),i.left=Math.max(i.left,n.left),i.bottom=Math.max(i.bottom,n.bottom),i.right=Math.max(i.right,n.right)}function xc(i,n,t,e){const{pos:a,box:s}=t,r=i.maxPadding;if(!P(a)){t.size&&(i[a]-=t.size);const u=e[t.stack]||{size:0,count:1};u.size=Math.max(u.size,t.horizontal?s.height:s.width),t.size=u.size/u.count,i[a]+=t.size}s.getPadding&&Jn(r,s.getPadding());const o=Math.max(0,n.outerWidth-Qn(r,i,"left","right")),l=Math.max(0,n.outerHeight-Qn(r,i,"top","bottom")),d=o!==i.w,h=l!==i.h;return i.w=o,i.h=l,t.horizontal?{same:d,other:h}:{same:h,other:d}}function kc(i,n){const t=n.maxPadding;return function e(a){const s={left:0,top:0,right:0,bottom:0};return a.forEach(r=>{s[r]=Math.max(n[r],t[r])}),s}(i?["left","right"]:["top","bottom"])}function Se(i,n,t,e){const a=[];let s,r,o,l,d,h;for(s=0,r=i.length,d=0;s<r;++s){o=i[s],l=o.box,l.update(o.width||n.w,o.height||n.h,kc(o.horizontal,n));const{same:u,other:f}=xc(n,t,o,e);d|=u&&a.length,h=h||f,l.fullSize||a.push(o)}return d&&Se(a,n,t,e)||h}function ri(i,n,t,e,a){i.top=t,i.left=n,i.right=n+e,i.bottom=t+a,i.width=e,i.height=a}function Zn(i,n,t,e){const a=t.padding;let{x:s,y:r}=n;for(const o of i){const l=o.box,d=e[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/d.weight||1;if(o.horizontal){const u=n.w*h,f=d.size||l.height;me(d.start)&&(r=d.start),l.fullSize?ri(l,a.left,r,t.outerWidth-a.right-a.left,f):ri(l,n.left+d.placed,r,u,f),d.start=r,d.placed+=u,r=l.bottom}else{const u=n.h*h,f=d.size||l.width;me(d.start)&&(s=d.start),l.fullSize?ri(l,s,a.top,f,t.outerHeight-a.bottom-a.top):ri(l,s,n.top+d.placed,f,u),d.start=s,d.placed+=u,s=l.right}}n.x=s,n.y=r}var it={addBox(i,n){i.boxes||(i.boxes=[]),n.fullSize=n.fullSize||!1,n.position=n.position||"top",n.weight=n.weight||0,n._layers=n._layers||function(){return[{z:0,draw(t){n.draw(t)}}]},i.boxes.push(n)},removeBox(i,n){const t=i.boxes?i.boxes.indexOf(n):-1;-1!==t&&i.boxes.splice(t,1)},configure(i,n,t){n.fullSize=t.fullSize,n.position=t.position,n.weight=t.weight},update(i,n,t,e){if(!i)return;const a=J(i.options.layout.padding),s=Math.max(n-a.width,0),r=Math.max(t-a.height,0),o=function yc(i){const n=function _c(i){const n=[];let t,e,a,s,r,o;for(t=0,e=(i||[]).length;t<e;++t)a=i[t],({position:s,options:{stack:r,stackWeight:o=1}}=a),n.push({index:t,box:a,pos:s,horizontal:a.isHorizontal(),weight:a.weight,stack:r&&s+r,stackWeight:o});return n}(i),t=we(n.filter(d=>d.box.fullSize),!0),e=we(Ce(n,"left"),!0),a=we(Ce(n,"right")),s=we(Ce(n,"top"),!0),r=we(Ce(n,"bottom")),o=qn(n,"x"),l=qn(n,"y");return{fullSize:t,leftAndTop:e.concat(s),rightAndBottom:a.concat(l).concat(r).concat(o),chartArea:Ce(n,"chartArea"),vertical:e.concat(a).concat(l),horizontal:s.concat(r).concat(o)}}(i.boxes),l=o.vertical,d=o.horizontal;L(i.boxes,m=>{"function"==typeof m.beforeLayout&&m.beforeLayout()});const h=l.reduce((m,_)=>_.box.options&&!1===_.box.options.display?m:m+1,0)||1,u=Object.freeze({outerWidth:n,outerHeight:t,padding:a,availableWidth:s,availableHeight:r,vBoxMaxWidth:s/2/h,hBoxMaxHeight:r/2}),f=Object.assign({},a);Jn(f,J(e));const p=Object.assign({maxPadding:f,w:s,h:r,x:a.left,y:a.top},a),g=function vc(i,n){const t=function bc(i){const n={};for(const t of i){const{stack:e,pos:a,stackWeight:s}=t;if(!e||!Kn.includes(a))continue;const r=n[e]||(n[e]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=s}return n}(i),{vBoxMaxWidth:e,hBoxMaxHeight:a}=n;let s,r,o;for(s=0,r=i.length;s<r;++s){o=i[s];const{fullSize:l}=o.box,d=t[o.stack],h=d&&o.stackWeight/d.weight;o.horizontal?(o.width=h?h*e:l&&n.availableWidth,o.height=a):(o.width=e,o.height=h?h*a:l&&n.availableHeight)}return t}(l.concat(d),u);Se(o.fullSize,p,u,g),Se(l,p,u,g),Se(d,p,u,g)&&Se(l,p,u,g),function Dc(i){const n=i.maxPadding;function t(e){const a=Math.max(n[e]-i[e],0);return i[e]+=a,a}i.y+=t("top"),i.x+=t("left"),t("right"),t("bottom")}(p),Zn(o.leftAndTop,p,u,g),p.x+=p.w,p.y+=p.h,Zn(o.rightAndBottom,p,u,g),i.chartArea={left:p.left,top:p.top,right:p.left+p.w,bottom:p.top+p.h,height:p.h,width:p.w},L(o.chartArea,m=>{const _=m.box;Object.assign(_,i.chartArea),_.update(p.w,p.h,{left:0,top:0,right:0,bottom:0})})}};class ts{acquireContext(n,t){}releaseContext(n){return!1}addEventListener(n,t,e){}removeEventListener(n,t,e){}getDevicePixelRatio(){return 1}getMaximumSize(n,t,e,a){return t=Math.max(0,t||n.width),e=e||n.height,{width:t,height:Math.max(0,a?Math.floor(t/a):e)}}isAttached(n){return!0}updateConfig(n){}}class Mc extends ts{acquireContext(n){return n&&n.getContext&&n.getContext("2d")||null}updateConfig(n){n.options.animation=!1}}const oi="$chartjs",Cc={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},es=i=>null===i||""===i,is=!!Ml&&{passive:!0};function Ac(i,n,t){i&&i.canvas&&i.canvas.removeEventListener(n,t,is)}function li(i,n){for(const t of i)if(t===n||t.contains(n))return!0}function Ec(i,n,t){const e=i.canvas,a=new MutationObserver(s=>{let r=!1;for(const o of s)r=r||li(o.addedNodes,e),r=r&&!li(o.removedNodes,e);r&&t()});return a.observe(document,{childList:!0,subtree:!0}),a}function Tc(i,n,t){const e=i.canvas,a=new MutationObserver(s=>{let r=!1;for(const o of s)r=r||li(o.removedNodes,e),r=r&&!li(o.addedNodes,e);r&&t()});return a.observe(document,{childList:!0,subtree:!0}),a}const Ae=new Map;let as=0;function ns(){const i=window.devicePixelRatio;i!==as&&(as=i,Ae.forEach((n,t)=>{t.currentDevicePixelRatio!==i&&n()}))}function Fc(i,n,t){const e=i.canvas,a=e&&Qi(e);if(!a)return;const s=sn((o,l)=>{const d=a.clientWidth;t(o,l),d<a.clientWidth&&t()},window),r=new ResizeObserver(o=>{const l=o[0],d=l.contentRect.width,h=l.contentRect.height;0===d&&0===h||s(d,h)});return r.observe(a),function Ic(i,n){Ae.size||window.addEventListener("resize",ns),Ae.set(i,n)}(i,s),r}function ra(i,n,t){t&&t.disconnect(),"resize"===n&&function Pc(i){Ae.delete(i),Ae.size||window.removeEventListener("resize",ns)}(i)}function Oc(i,n,t){const e=i.canvas,a=sn(s=>{null!==i.ctx&&t(function Rc(i,n){const t=Cc[i.type]||i.type,{x:e,y:a}=Ut(i,n);return{type:t,chart:n,native:i,x:void 0!==e?e:null,y:void 0!==a?a:null}}(s,i))},i);return function Sc(i,n,t){i&&i.addEventListener(n,t,is)}(e,n,a),a}class Vc extends ts{acquireContext(n,t){const e=n&&n.getContext&&n.getContext("2d");return e&&e.canvas===n?(function wc(i,n){const t=i.style,e=i.getAttribute("height"),a=i.getAttribute("width");if(i[oi]={initial:{height:e,width:a,style:{display:t.display,height:t.height,width:t.width}}},t.display=t.display||"block",t.boxSizing=t.boxSizing||"border-box",es(a)){const s=Cn(i,"width");void 0!==s&&(i.width=s)}if(es(e))if(""===i.style.height)i.height=i.width/(n||2);else{const s=Cn(i,"height");void 0!==s&&(i.height=s)}}(n,t),e):null}releaseContext(n){const t=n.canvas;if(!t[oi])return!1;const e=t[oi].initial;["height","width"].forEach(s=>{const r=e[s];R(r)?t.removeAttribute(s):t.setAttribute(s,r)});const a=e.style||{};return Object.keys(a).forEach(s=>{t.style[s]=a[s]}),t.width=t.width,delete t[oi],!0}addEventListener(n,t,e){this.removeEventListener(n,t),(n.$proxies||(n.$proxies={}))[t]=({attach:Ec,detach:Tc,resize:Fc}[t]||Oc)(n,t,e)}removeEventListener(n,t){const e=n.$proxies||(n.$proxies={}),a=e[t];a&&(({attach:ra,detach:ra,resize:ra}[t]||Ac)(n,t,a),e[t]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(n,t,e,a){return function kl(i,n,t,e){const a=ai(i),s=Wt(a,"margin"),r=ii(a.maxWidth,i,"clientWidth")||Ke,o=ii(a.maxHeight,i,"clientHeight")||Ke,l=function Dl(i,n,t){let e,a;if(void 0===n||void 0===t){const s=i&&Qi(i);if(s){const r=s.getBoundingClientRect(),o=ai(s),l=Wt(o,"border","width"),d=Wt(o,"padding");n=r.width-d.width-l.width,t=r.height-d.height-l.height,e=ii(o.maxWidth,s,"clientWidth"),a=ii(o.maxHeight,s,"clientHeight")}else n=i.clientWidth,t=i.clientHeight}return{width:n,height:t,maxWidth:e||Ke,maxHeight:a||Ke}}(i,n,t);let{width:d,height:h}=l;if("content-box"===a.boxSizing){const f=Wt(a,"border","width"),p=Wt(a,"padding");d-=p.width+f.width,h-=p.height+f.height}return d=Math.max(0,d-s.width),h=Math.max(0,e?d/e:h-s.height),d=ni(Math.min(d,r,l.maxWidth)),h=ni(Math.min(h,o,l.maxHeight)),d&&!h&&(h=ni(d/2)),(void 0!==n||void 0!==t)&&e&&l.height&&h>l.height&&(h=l.height,d=ni(Math.floor(h*e))),{width:d,height:h}}(n,t,e,a)}isAttached(n){const t=n&&Qi(n);return!(!t||!t.isConnected)}}class wt{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(n){const{x:t,y:e}=this.getProps(["x","y"],n);return{x:t,y:e}}hasValue(){return ae(this.x)&&ae(this.y)}getProps(n,t){const e=this.$animations;if(!t||!e)return this;const a={};return n.forEach(s=>{a[s]=e[s]&&e[s].active()?e[s]._to:this[s]}),a}}function ci(i,n,t,e,a){const s=A(e,0),r=Math.min(A(a,i.length),i.length);let l,d,h,o=0;for(t=Math.ceil(t),a&&(l=a-e,t=l/Math.floor(l/t)),h=s;h<0;)o++,h=Math.round(s+o*t);for(d=Math.max(s,0);d<r;d++)d===h&&(n.push(i[d]),o++,h=Math.round(s+o*t))}const ss=(i,n,t)=>"top"===n||"left"===n?i[n]+t:i[n]-t,rs=(i,n)=>Math.min(n||i,i);function os(i,n){const t=[],e=i.length/n,a=i.length;let s=0;for(;s<a;s+=e)t.push(i[Math.floor(s)]);return t}function Uc(i,n,t){const e=i.ticks.length,a=Math.min(n,e-1),s=i._startPixel,r=i._endPixel,o=1e-6;let d,l=i.getPixelForTick(a);if(!(t&&(d=1===e?Math.max(l-s,r-l):0===n?(i.getPixelForTick(1)-l)/2:(l-i.getPixelForTick(a-1))/2,l+=a<n?d:-d,l<s-o||l>r+o)))return l}function Re(i){return i.drawTicks?i.tickLength:0}function ls(i,n){if(!i.display)return 0;const t=q(i.font,n),e=J(i.padding);return(j(i.text)?i.text.length:1)*t.lineHeight+e.height}function Kc(i,n,t){let e=Ni(i);return(t&&"right"!==n||!t&&"right"===n)&&(e=(i=>"left"===i?"right":"right"===i?"left":i)(e)),e}class Xt extends wt{constructor(n){super(),this.id=n.id,this.type=n.type,this.options=void 0,this.ctx=n.ctx,this.chart=n.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(n){this.options=n.setContext(this.getContext()),this.axis=n.axis,this._userMin=this.parse(n.min),this._userMax=this.parse(n.max),this._suggestedMin=this.parse(n.suggestedMin),this._suggestedMax=this.parse(n.suggestedMax)}parse(n,t){return n}getUserBounds(){let{_userMin:n,_userMax:t,_suggestedMin:e,_suggestedMax:a}=this;return n=ot(n,Number.POSITIVE_INFINITY),t=ot(t,Number.NEGATIVE_INFINITY),e=ot(e,Number.POSITIVE_INFINITY),a=ot(a,Number.NEGATIVE_INFINITY),{min:ot(n,e),max:ot(t,a),minDefined:G(n),maxDefined:G(t)}}getMinMax(n){let r,{min:t,max:e,minDefined:a,maxDefined:s}=this.getUserBounds();if(a&&s)return{min:t,max:e};const o=this.getMatchingVisibleMetas();for(let l=0,d=o.length;l<d;++l)r=o[l].controller.getMinMax(this,n),a||(t=Math.min(t,r.min)),s||(e=Math.max(e,r.max));return t=s&&t>e?e:t,e=a&&t>e?t:e,{min:ot(t,ot(e,t)),max:ot(e,ot(t,e))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const n=this.chart.data;return this.options.labels||(this.isHorizontal()?n.xLabels:n.yLabels)||n.labels||[]}getLabelItems(n=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(n))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){B(this.options.beforeUpdate,[this])}update(n,t,e){const{beginAtZero:a,grace:s,ticks:r}=this.options,o=r.sampleSize;this.beforeUpdate(),this.maxWidth=n,this.maxHeight=t,this._margins=e=Object.assign({left:0,right:0,top:0,bottom:0},e),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+e.left+e.right:this.height+e.top+e.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function el(i,n,t){const{min:e,max:a}=i,s=Ua(n,(a-e)/2),r=(o,l)=>t&&0===o?0:o+l;return{min:r(e,-Math.abs(s)),max:r(a,s)}}(this,s,a),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=o<this.ticks.length;this._convertTicksToLabels(l?os(this.ticks,o):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||"auto"===r.source)&&(this.ticks=function Bc(i,n){const t=i.options.ticks,e=function zc(i){const n=i.options.offset,t=i._tickSize();return Math.floor(Math.min(i._length/t+(n?0:1),i._maxLength/t))}(i),a=Math.min(t.maxTicksLimit||e,e),s=t.major.enabled?function jc(i){const n=[];let t,e;for(t=0,e=i.length;t<e;t++)i[t].major&&n.push(t);return n}(n):[],r=s.length,o=s[0],l=s[r-1],d=[];if(r>a)return function Hc(i,n,t,e){let r,a=0,s=t[0];for(e=Math.ceil(e),r=0;r<i.length;r++)r===s&&(n.push(i[r]),a++,s=t[a*e])}(n,d,s,r/a),d;const h=function Nc(i,n,t){const e=function Yc(i){const n=i.length;let t,e;if(n<2)return!1;for(e=i[0],t=1;t<n;++t)if(i[t]-i[t-1]!==e)return!1;return e}(i),a=n.length/t;if(!e)return Math.max(a,1);const s=function wo(i){const n=[],t=Math.sqrt(i);let e;for(e=1;e<t;e++)i%e==0&&(n.push(e),n.push(i/e));return t===(0|t)&&n.push(t),n.sort((a,s)=>a-s).pop(),n}(e);for(let r=0,o=s.length-1;r<o;r++){const l=s[r];if(l>a)return l}return Math.max(a,1)}(s,n,a);if(r>0){let u,f;const p=r>1?Math.round((l-o)/(r-1)):null;for(ci(n,d,h,R(p)?0:o-p,o),u=0,f=r-1;u<f;u++)ci(n,d,h,s[u],s[u+1]);return ci(n,d,h,l,R(p)?n.length:l+p),d}return ci(n,d,h),d}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,n=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,n=!n),this._startPixel=t,this._endPixel=e,this._reversePixels=n,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){B(this.options.afterUpdate,[this])}beforeSetDimensions(){B(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){B(this.options.afterSetDimensions,[this])}_callHooks(n){this.chart.notifyPlugins(n,this.getContext()),B(this.options[n],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){B(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(n){const t=this.options.ticks;let e,a,s;for(e=0,a=n.length;e<a;e++)s=n[e],s.label=B(t.callback,[s.value,e,n],this)}afterTickToLabelConversion(){B(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){B(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const n=this.options,t=n.ticks,e=rs(this.ticks.length,n.ticks.maxTicksLimit),a=t.minRotation||0,s=t.maxRotation;let o,l,d,r=a;if(!this._isVisible()||!t.display||a>=s||e<=1||!this.isHorizontal())return void(this.labelRotation=a);const h=this._getLabelSizes(),u=h.widest.width,f=h.highest.height,p=Q(this.chart.width-u,0,this.maxWidth);o=n.offset?this.maxWidth/e:p/(e-1),u+6>o&&(o=p/(e-(n.offset?.5:1)),l=this.maxHeight-Re(n.grid)-t.padding-ls(n.title,this.chart.options.font),d=Math.sqrt(u*u+f*f),r=Li(Math.min(Math.asin(Q((h.highest.height+6)/o,-1,1)),Math.asin(Q(l/d,-1,1))-Math.asin(Q(f/d,-1,1)))),r=Math.max(a,Math.min(s,r))),this.labelRotation=r}afterCalculateLabelRotation(){B(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){B(this.options.beforeFit,[this])}fit(){const n={width:0,height:0},{chart:t,options:{ticks:e,title:a,grid:s}}=this,r=this._isVisible(),o=this.isHorizontal();if(r){const l=ls(a,t.options.font);if(o?(n.width=this.maxWidth,n.height=Re(s)+l):(n.height=this.maxHeight,n.width=Re(s)+l),e.display&&this.ticks.length){const{first:d,last:h,widest:u,highest:f}=this._getLabelSizes(),p=2*e.padding,g=ut(this.labelRotation),m=Math.cos(g),_=Math.sin(g);o?n.height=Math.min(this.maxHeight,n.height+(e.mirror?0:_*u.width+m*f.height)+p):n.width=Math.min(this.maxWidth,n.width+(e.mirror?0:m*u.width+_*f.height)+p),this._calculatePadding(d,h,_,m)}}this._handleMargins(),o?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=n.height):(this.width=n.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(n,t,e,a){const{ticks:{align:s,padding:r},position:o}=this.options,l=0!==this.labelRotation,d="top"!==o&&"x"===this.axis;if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,u=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,p=0;l?d?(f=a*n.width,p=e*t.height):(f=e*n.height,p=a*t.width):"start"===s?p=t.width:"end"===s?f=n.width:"inner"!==s&&(f=n.width/2,p=t.width/2),this.paddingLeft=Math.max((f-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((p-u+r)*this.width/(this.width-u),0)}else{let h=t.height/2,u=n.height/2;"start"===s?(h=0,u=n.height):"end"===s&&(h=t.height,u=0),this.paddingTop=h+r,this.paddingBottom=u+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){B(this.options.afterFit,[this])}isHorizontal(){const{axis:n,position:t}=this.options;return"top"===t||"bottom"===t||"x"===n}isFullSize(){return this.options.fullSize}_convertTicksToLabels(n){let t,e;for(this.beforeTickToLabelConversion(),this.generateTickLabels(n),t=0,e=n.length;t<e;t++)R(n[t].label)&&(n.splice(t,1),e--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let n=this._labelSizes;if(!n){const t=this.options.ticks.sampleSize;let e=this.ticks;t<e.length&&(e=os(e,t)),this._labelSizes=n=this._computeLabelSizes(e,e.length,this.options.ticks.maxTicksLimit)}return n}_computeLabelSizes(n,t,e){const{ctx:a,_longestTextCache:s}=this,r=[],o=[],l=Math.floor(t/rs(t,e));let u,f,p,g,m,_,v,b,x,D,y,d=0,h=0;for(u=0;u<t;u+=l){if(g=n[u].label,m=this._resolveTickFontOptions(u),a.font=_=m.string,v=s[_]=s[_]||{data:{},gc:[]},b=m.lineHeight,x=D=0,R(g)||j(g)){if(j(g))for(f=0,p=g.length;f<p;++f)y=g[f],!R(y)&&!j(y)&&(x=Je(a,v.data,v.gc,x,y),D+=b)}else x=Je(a,v.data,v.gc,x,g),D=b;r.push(x),o.push(D),d=Math.max(x,d),h=Math.max(D,h)}!function $c(i,n){L(i,t=>{const e=t.gc,a=e.length/2;let s;if(a>n){for(s=0;s<a;++s)delete t.data[e[s]];e.splice(0,a)}})}(s,t);const k=r.indexOf(d),C=o.indexOf(h),M=w=>({width:r[w]||0,height:o[w]||0});return{first:M(0),last:M(t-1),widest:M(k),highest:M(C),widths:r,heights:o}}getLabelForValue(n){return n}getPixelForValue(n,t){return NaN}getValueForPixel(n){}getPixelForTick(n){const t=this.ticks;return n<0||n>t.length-1?null:this.getPixelForValue(t[n].value)}getPixelForDecimal(n){this._reversePixels&&(n=1-n);const t=this._startPixel+n*this._length;return function Eo(i){return Q(i,-32768,32767)}(this._alignToPixels?jt(this.chart,t,0):t)}getDecimalForPixel(n){const t=(n-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:n,max:t}=this;return n<0&&t<0?t:n>0&&t>0?n:0}getContext(n){const t=this.ticks||[];if(n>=0&&n<t.length){const e=t[n];return e.$context||(e.$context=function Xc(i,n,t){return Pt(i,{tick:t,index:n,type:"tick"})}(this.getContext(),n,e))}return this.$context||(this.$context=function Gc(i,n){return Pt(i,{scale:n,type:"scale"})}(this.chart.getContext(),this))}_tickSize(){const n=this.options.ticks,t=ut(this.labelRotation),e=Math.abs(Math.cos(t)),a=Math.abs(Math.sin(t)),s=this._getLabelSizes(),r=n.autoSkipPadding||0,o=s?s.widest.width+r:0,l=s?s.highest.height+r:0;return this.isHorizontal()?l*e>o*a?o/e:l/a:l*a<o*e?l/e:o/a}_isVisible(){const n=this.options.display;return"auto"!==n?!!n:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(n){const t=this.axis,e=this.chart,a=this.options,{grid:s,position:r,border:o}=a,l=s.offset,d=this.isHorizontal(),u=this.ticks.length+(l?1:0),f=Re(s),p=[],g=o.setContext(this.getContext()),m=g.display?g.width:0,_=m/2,v=function($){return jt(e,$,m)};let b,x,D,y,k,C,M,w,I,T,F,tt;if("top"===r)b=v(this.bottom),C=this.bottom-f,w=b-_,T=v(n.top)+_,tt=n.bottom;else if("bottom"===r)b=v(this.top),T=n.top,tt=v(n.bottom)-_,C=b+_,w=this.top+f;else if("left"===r)b=v(this.right),k=this.right-f,M=b-_,I=v(n.left)+_,F=n.right;else if("right"===r)b=v(this.left),I=n.left,F=v(n.right)-_,k=b+_,M=this.left+f;else if("x"===t){if("center"===r)b=v((n.top+n.bottom)/2+.5);else if(P(r)){const $=Object.keys(r)[0];b=v(this.chart.scales[$].getPixelForValue(r[$]))}T=n.top,tt=n.bottom,C=b+_,w=C+f}else if("y"===t){if("center"===r)b=v((n.left+n.right)/2);else if(P(r)){const $=Object.keys(r)[0];b=v(this.chart.scales[$].getPixelForValue(r[$]))}k=b-_,M=k-f,I=n.left,F=n.right}const dt=A(a.ticks.maxTicksLimit,u),z=Math.max(1,Math.ceil(u/dt));for(x=0;x<u;x+=z){const $=this.getContext(x),K=s.setContext($),ft=o.setContext($),at=K.lineWidth,he=K.color,Ei=ft.dash||[],ue=ft.dashOffset,He=K.tickWidth,Jt=K.tickColor,Ye=K.tickBorderDash||[],Zt=K.tickBorderDashOffset;D=Uc(this,x,l),void 0!==D&&(y=jt(e,D,at),d?k=M=I=F=y:C=w=T=tt=y,p.push({tx1:k,ty1:C,tx2:M,ty2:w,x1:I,y1:T,x2:F,y2:tt,width:at,color:he,borderDash:Ei,borderDashOffset:ue,tickWidth:He,tickColor:Jt,tickBorderDash:Ye,tickBorderDashOffset:Zt}))}return this._ticksLength=u,this._borderValue=b,p}_computeLabelItems(n){const t=this.axis,e=this.options,{position:a,ticks:s}=e,r=this.isHorizontal(),o=this.ticks,{align:l,crossAlign:d,padding:h,mirror:u}=s,f=Re(e.grid),p=f+h,g=u?-h:p,m=-ut(this.labelRotation),_=[];let v,b,x,D,y,k,C,M,w,I,T,F,tt="middle";if("top"===a)k=this.bottom-g,C=this._getXAxisLabelAlignment();else if("bottom"===a)k=this.top+g,C=this._getXAxisLabelAlignment();else if("left"===a){const z=this._getYAxisLabelAlignment(f);C=z.textAlign,y=z.x}else if("right"===a){const z=this._getYAxisLabelAlignment(f);C=z.textAlign,y=z.x}else if("x"===t){if("center"===a)k=(n.top+n.bottom)/2+p;else if(P(a)){const z=Object.keys(a)[0];k=this.chart.scales[z].getPixelForValue(a[z])+p}C=this._getXAxisLabelAlignment()}else if("y"===t){if("center"===a)y=(n.left+n.right)/2-p;else if(P(a)){const z=Object.keys(a)[0];y=this.chart.scales[z].getPixelForValue(a[z])}C=this._getYAxisLabelAlignment(f).textAlign}"y"===t&&("start"===l?tt="top":"end"===l&&(tt="bottom"));const dt=this._getLabelSizes();for(v=0,b=o.length;v<b;++v){x=o[v],D=x.label;const z=s.setContext(this.getContext(v));M=this.getPixelForTick(v)+s.labelOffset,w=this._resolveTickFontOptions(v),I=w.lineHeight,T=j(D)?D.length:1;const $=T/2,K=z.color,ft=z.textStrokeColor,at=z.textStrokeWidth;let Ei,he=C;if(r?(y=M,"inner"===C&&(he=v===b-1?this.options.reverse?"left":"right":0===v?this.options.reverse?"right":"left":"center"),F="top"===a?"near"===d||0!==m?-T*I+I/2:"center"===d?-dt.highest.height/2-$*I+I:I/2-dt.highest.height:"near"===d||0!==m?I/2:"center"===d?dt.highest.height/2-$*I:dt.highest.height-T*I,u&&(F*=-1),0!==m&&!z.showLabelBackdrop&&(y+=I/2*Math.sin(m))):(k=M,F=(1-T)*I/2),z.showLabelBackdrop){const ue=J(z.backdropPadding),He=dt.heights[v],Jt=dt.widths[v];let Ye=F-ue.top,Zt=0-ue.left;switch(tt){case"middle":Ye-=He/2;break;case"bottom":Ye-=He}switch(C){case"center":Zt-=Jt/2;break;case"right":Zt-=Jt;break;case"inner":v===b-1?Zt-=Jt:v>0&&(Zt-=Jt/2)}Ei={left:Zt,top:Ye,width:Jt+ue.width,height:He+ue.height,color:z.backdropColor}}_.push({label:D,font:w,textOffset:F,options:{rotation:m,color:K,strokeColor:ft,strokeWidth:at,textAlign:he,textBaseline:tt,translation:[y,k],backdrop:Ei}})}return _}_getXAxisLabelAlignment(){const{position:n,ticks:t}=this.options;if(-ut(this.labelRotation))return"top"===n?"left":"right";let a="center";return"start"===t.align?a="left":"end"===t.align?a="right":"inner"===t.align&&(a="inner"),a}_getYAxisLabelAlignment(n){const{position:t,ticks:{crossAlign:e,mirror:a,padding:s}}=this.options,o=n+s,l=this._getLabelSizes().widest.width;let d,h;return"left"===t?a?(h=this.right+s,"near"===e?d="left":"center"===e?(d="center",h+=l/2):(d="right",h+=l)):(h=this.right-o,"near"===e?d="right":"center"===e?(d="center",h-=l/2):(d="left",h=this.left)):"right"===t?a?(h=this.left+s,"near"===e?d="right":"center"===e?(d="center",h-=l/2):(d="left",h-=l)):(h=this.left+o,"near"===e?d="left":"center"===e?(d="center",h+=l/2):(d="right",h=this.right)):d="right",{textAlign:d,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const n=this.chart,t=this.options.position;return"left"===t||"right"===t?{top:0,left:this.left,bottom:n.height,right:this.right}:"top"===t||"bottom"===t?{top:this.top,left:0,bottom:this.bottom,right:n.width}:void 0}drawBackground(){const{ctx:n,options:{backgroundColor:t},left:e,top:a,width:s,height:r}=this;t&&(n.save(),n.fillStyle=t,n.fillRect(e,a,s,r),n.restore())}getLineWidthForValue(n){const t=this.options.grid;if(!this._isVisible()||!t.display)return 0;const a=this.ticks.findIndex(s=>s.value===n);return a>=0?t.setContext(this.getContext(a)).lineWidth:0}drawGrid(n){const t=this.options.grid,e=this.ctx,a=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(n));let s,r;const o=(l,d,h)=>{!h.width||!h.color||(e.save(),e.lineWidth=h.width,e.strokeStyle=h.color,e.setLineDash(h.borderDash||[]),e.lineDashOffset=h.borderDashOffset,e.beginPath(),e.moveTo(l.x,l.y),e.lineTo(d.x,d.y),e.stroke(),e.restore())};if(t.display)for(s=0,r=a.length;s<r;++s){const l=a[s];t.drawOnChartArea&&o({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),t.drawTicks&&o({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:n,ctx:t,options:{border:e,grid:a}}=this,s=e.setContext(this.getContext()),r=e.display?s.width:0;if(!r)return;const o=a.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let d,h,u,f;this.isHorizontal()?(d=jt(n,this.left,r)-r/2,h=jt(n,this.right,o)+o/2,u=f=l):(u=jt(n,this.top,r)-r/2,f=jt(n,this.bottom,o)+o/2,d=h=l),t.save(),t.lineWidth=s.width,t.strokeStyle=s.color,t.beginPath(),t.moveTo(d,u),t.lineTo(h,f),t.stroke(),t.restore()}drawLabels(n){if(!this.options.ticks.display)return;const e=this.ctx,a=this._computeLabelArea();a&&Ze(e,a);const s=this.getLabelItems(n);for(const r of s)Ht(e,r.label,0,r.textOffset,r.font,r.options);a&&ti(e)}drawTitle(){const{ctx:n,options:{position:t,title:e,reverse:a}}=this;if(!e.display)return;const s=q(e.font),r=J(e.padding),o=e.align;let l=s.lineHeight/2;"bottom"===t||"center"===t||P(t)?(l+=r.bottom,j(e.text)&&(l+=s.lineHeight*(e.text.length-1))):l+=r.top;const{titleX:d,titleY:h,maxWidth:u,rotation:f}=function qc(i,n,t,e){const{top:a,left:s,bottom:r,right:o,chart:l}=i,{chartArea:d,scales:h}=l;let f,p,g,u=0;const m=r-a,_=o-s;if(i.isHorizontal()){if(p=et(e,s,o),P(t)){const v=Object.keys(t)[0];g=h[v].getPixelForValue(t[v])+m-n}else g="center"===t?(d.bottom+d.top)/2+m-n:ss(i,t,n);f=o-s}else{if(P(t)){const v=Object.keys(t)[0];p=h[v].getPixelForValue(t[v])-_+n}else p="center"===t?(d.left+d.right)/2-_+n:ss(i,t,n);g=et(e,r,a),u="left"===t?-X:X}return{titleX:p,titleY:g,maxWidth:f,rotation:u}}(this,l,t,o);Ht(n,e.text,0,0,s,{color:e.color,maxWidth:u,rotation:f,textAlign:Kc(o,t,a),textBaseline:"middle",translation:[d,h]})}draw(n){this._isVisible()&&(this.drawBackground(),this.drawGrid(n),this.drawBorder(),this.drawTitle(),this.drawLabels(n))}_layers(){const n=this.options,t=n.ticks&&n.ticks.z||0,e=A(n.grid&&n.grid.z,-1),a=A(n.border&&n.border.z,0);return this._isVisible()&&this.draw===Xt.prototype.draw?[{z:e,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:a,draw:()=>{this.drawBorder()}},{z:t,draw:s=>{this.drawLabels(s)}}]:[{z:t,draw:s=>{this.draw(s)}}]}getMatchingVisibleMetas(n){const t=this.chart.getSortedVisibleDatasetMetas(),e=this.axis+"AxisID",a=[];let s,r;for(s=0,r=t.length;s<r;++s){const o=t[s];o[e]===this.id&&(!n||o.type===n)&&a.push(o)}return a}_resolveTickFontOptions(n){return q(this.options.ticks.setContext(this.getContext(n)).font)}_maxDigits(){const n=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/n}}class di{constructor(n,t,e){this.type=n,this.scope=t,this.override=e,this.items=Object.create(null)}isForType(n){return Object.prototype.isPrototypeOf.call(this.type.prototype,n.prototype)}register(n){const t=Object.getPrototypeOf(n);let e;(function Zc(i){return"id"in i&&"defaults"in i})(t)&&(e=this.register(t));const a=this.items,s=n.id,r=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+n);return s in a||(a[s]=n,function Qc(i,n,t){const e=pt(Object.create(null),[t?W.get(t):{},W.get(n),i.defaults]);W.set(n,e),i.defaultRoutes&&function Jc(i,n){Object.keys(n).forEach(t=>{const e=t.split("."),a=e.pop(),s=[i].concat(e).join("."),r=n[t].split("."),o=r.pop(),l=r.join(".");W.route(s,a,l,o)})}(n,i.defaultRoutes),i.descriptors&&W.describe(n,i.descriptors)}(n,r,e),this.override&&W.override(n.id,n.overrides)),r}get(n){return this.items[n]}unregister(n){const t=this.items,e=n.id,a=this.scope;e in t&&delete t[e],a&&e in W[a]&&(delete W[a][e],this.override&&delete Nt[e])}}class td{constructor(){this.controllers=new di(Ft,"datasets",!0),this.elements=new di(wt,"elements"),this.plugins=new di(Object,"plugins"),this.scales=new di(Xt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...n){this._each("register",n)}remove(...n){this._each("unregister",n)}addControllers(...n){this._each("register",n,this.controllers)}addElements(...n){this._each("register",n,this.elements)}addPlugins(...n){this._each("register",n,this.plugins)}addScales(...n){this._each("register",n,this.scales)}getController(n){return this._get(n,this.controllers,"controller")}getElement(n){return this._get(n,this.elements,"element")}getPlugin(n){return this._get(n,this.plugins,"plugin")}getScale(n){return this._get(n,this.scales,"scale")}removeControllers(...n){this._each("unregister",n,this.controllers)}removeElements(...n){this._each("unregister",n,this.elements)}removePlugins(...n){this._each("unregister",n,this.plugins)}removeScales(...n){this._each("unregister",n,this.scales)}_each(n,t,e){[...t].forEach(a=>{const s=e||this._getRegistryForType(a);e||s.isForType(a)||s===this.plugins&&a.id?this._exec(n,s,a):L(a,r=>{const o=e||this._getRegistryForType(r);this._exec(n,o,r)})})}_exec(n,t,e){const a=Vi(n);B(e["before"+a],[],e),t[n](e),B(e["after"+a],[],e)}_getRegistryForType(n){for(let t=0;t<this._typedRegistries.length;t++){const e=this._typedRegistries[t];if(e.isForType(n))return e}return this.plugins}_get(n,t,e){const a=t.get(n);if(void 0===a)throw new Error('"'+n+'" is not a registered '+e+".");return a}}var mt=new td;class ed{constructor(){this._init=[]}notify(n,t,e,a){"beforeInit"===t&&(this._init=this._createDescriptors(n,!0),this._notify(this._init,n,"install"));const s=a?this._descriptors(n).filter(a):this._descriptors(n),r=this._notify(s,n,t,e);return"afterDestroy"===t&&(this._notify(s,n,"stop"),this._notify(this._init,n,"uninstall")),r}_notify(n,t,e,a){a=a||{};for(const s of n){const r=s.plugin;if(!1===B(r[e],[t,a,s.options],r)&&a.cancelable)return!1}return!0}invalidate(){R(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(n){if(this._cache)return this._cache;const t=this._cache=this._createDescriptors(n);return this._notifyStateChanges(n),t}_createDescriptors(n,t){const e=n&&n.config,a=A(e.options&&e.options.plugins,{}),s=function id(i){const n={},t=[],e=Object.keys(mt.plugins.items);for(let s=0;s<e.length;s++)t.push(mt.getPlugin(e[s]));const a=i.plugins||[];for(let s=0;s<a.length;s++){const r=a[s];-1===t.indexOf(r)&&(t.push(r),n[r.id]=!0)}return{plugins:t,localIds:n}}(e);return!1!==a||t?function nd(i,{plugins:n,localIds:t},e,a){const s=[],r=i.getContext();for(const o of n){const l=o.id,d=ad(e[l],a);null!==d&&s.push({plugin:o,options:sd(i.config,{plugin:o,local:t[l]},d,r)})}return s}(n,s,a,t):[]}_notifyStateChanges(n){const t=this._oldCache||[],e=this._cache,a=(s,r)=>s.filter(o=>!r.some(l=>o.plugin.id===l.plugin.id));this._notify(a(t,e),n,"stop"),this._notify(a(e,t),n,"start")}}function ad(i,n){return n||!1!==i?!0===i?{}:i:null}function sd(i,{plugin:n,local:t},e,a){const s=i.pluginScopeKeys(n),r=i.getOptionScopes(e,s);return t&&n.defaults&&r.push(n.defaults),i.createResolver(r,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function oa(i,n){return((n.datasets||{})[i]||{}).indexAxis||n.indexAxis||(W.datasets[i]||{}).indexAxis||"x"}function cs(i){if("x"===i||"y"===i||"r"===i)return i}function ld(i){return"top"===i||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0}function la(i,...n){if(cs(i))return i;for(const t of n){const e=t.axis||ld(t.position)||i.length>1&&cs(i[0].toLowerCase());if(e)return e}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function ds(i,n,t){if(t[n+"AxisID"]===i)return{axis:n}}function hs(i){const n=i.options||(i.options={});n.plugins=A(n.plugins,{}),n.scales=function dd(i,n){const t=Nt[i.type]||{scales:{}},e=n.scales||{},a=oa(i.type,n),s=Object.create(null);return Object.keys(e).forEach(r=>{const o=e[r];if(!P(o))return console.error(`Invalid scale configuration for scale: ${r}`);if(o._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=la(r,o,function cd(i,n){if(n.data&&n.data.datasets){const t=n.data.datasets.filter(e=>e.xAxisID===i||e.yAxisID===i);if(t.length)return ds(i,"x",t[0])||ds(i,"y",t[0])}return{}}(r,i),W.scales[o.type]),d=function od(i,n){return i===n?"_index_":"_value_"}(l,a),h=t.scales||{};s[r]=ge(Object.create(null),[{axis:l},o,h[l],h[d]])}),i.data.datasets.forEach(r=>{const o=r.type||i.type,l=r.indexAxis||oa(o,n),h=(Nt[o]||{}).scales||{};Object.keys(h).forEach(u=>{const f=function rd(i,n){let t=i;return"_index_"===i?t=n:"_value_"===i&&(t="x"===n?"y":"x"),t}(u,l),p=r[f+"AxisID"]||f;s[p]=s[p]||Object.create(null),ge(s[p],[{axis:f},e[p],h[u]])})}),Object.keys(s).forEach(r=>{const o=s[r];ge(o,[W.scales[o.type],W.scale])}),s}(i,n)}function us(i){return(i=i||{}).datasets=i.datasets||[],i.labels=i.labels||[],i}const fs=new Map,ps=new Set;function hi(i,n){let t=fs.get(i);return t||(t=n(),fs.set(i,t),ps.add(t)),t}const Ee=(i,n,t)=>{const e=Et(n,t);void 0!==e&&i.add(e)};class ud{constructor(n){this._config=function hd(i){return(i=i||{}).data=us(i.data),hs(i),i}(n),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(n){this._config.type=n}get data(){return this._config.data}set data(n){this._config.data=us(n)}get options(){return this._config.options}set options(n){this._config.options=n}get plugins(){return this._config.plugins}update(){const n=this._config;this.clearCache(),hs(n)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(n){return hi(n,()=>[[`datasets.${n}`,""]])}datasetAnimationScopeKeys(n,t){return hi(`${n}.transition.${t}`,()=>[[`datasets.${n}.transitions.${t}`,`transitions.${t}`],[`datasets.${n}`,""]])}datasetElementScopeKeys(n,t){return hi(`${n}-${t}`,()=>[[`datasets.${n}.elements.${t}`,`datasets.${n}`,`elements.${t}`,""]])}pluginScopeKeys(n){const t=n.id;return hi(`${this.type}-plugin-${t}`,()=>[[`plugins.${t}`,...n.additionalOptionScopes||[]]])}_cachedScopes(n,t){const e=this._scopeCache;let a=e.get(n);return(!a||t)&&(a=new Map,e.set(n,a)),a}getOptionScopes(n,t,e){const{options:a,type:s}=this,r=this._cachedScopes(n,e),o=r.get(t);if(o)return o;const l=new Set;t.forEach(h=>{n&&(l.add(n),h.forEach(u=>Ee(l,n,u))),h.forEach(u=>Ee(l,a,u)),h.forEach(u=>Ee(l,Nt[s]||{},u)),h.forEach(u=>Ee(l,W,u)),h.forEach(u=>Ee(l,Yi,u))});const d=Array.from(l);return 0===d.length&&d.push(Object.create(null)),ps.has(t)&&r.set(t,d),d}chartOptionScopes(){const{options:n,type:t}=this;return[n,Nt[t]||{},W.datasets[t]||{},{type:t},W,Yi]}resolveNamedOptions(n,t,e,a=[""]){const s={$shared:!0},{resolver:r,subPrefixes:o}=gs(this._resolverCache,n,a);let l=r;(function pd(i,n){const{isScriptable:t,isIndexable:e}=mn(i);for(const a of n){const s=t(a),r=e(a),o=(r||s)&&i[a];if(s&&(Tt(o)||fd(o))||r&&j(o))return!0}return!1})(r,t)&&(s.$shared=!1,l=ne(r,e=Tt(e)?e():e,this.createResolver(n,e,o)));for(const d of t)s[d]=l[d];return s}createResolver(n,t,e=[""],a){const{resolver:s}=gs(this._resolverCache,n,e);return P(t)?ne(s,t,void 0,a):s}}function gs(i,n,t){let e=i.get(n);e||(e=new Map,i.set(n,e));const a=t.join();let s=e.get(a);return s||(s={resolver:Gi(n,t),subPrefixes:t.filter(o=>!o.toLowerCase().includes("hover"))},e.set(a,s)),s}const fd=i=>P(i)&&Object.getOwnPropertyNames(i).some(n=>Tt(i[n])),md=["top","bottom","left","right","chartArea"];function ms(i,n){return"top"===i||"bottom"===i||-1===md.indexOf(i)&&"x"===n}function _s(i,n){return function(t,e){return t[i]===e[i]?t[n]-e[n]:t[i]-e[i]}}function bs(i){const n=i.chart,t=n.options.animation;n.notifyPlugins("afterRender"),B(t&&t.onComplete,[i],n)}function _d(i){const n=i.chart,t=n.options.animation;B(t&&t.onProgress,[i],n)}function vs(i){return qi()&&"string"==typeof i?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ui={},ys=i=>{const n=vs(i);return Object.values(ui).filter(t=>t.canvas===n).pop()};function bd(i,n,t){const e=Object.keys(i);for(const a of e){const s=+a;if(s>=n){const r=i[a];delete i[a],(t>0||s>n)&&(i[s+t]=r)}}}let ca=(()=>class i{static defaults=W;static instances=ui;static overrides=Nt;static registry=mt;static version="4.4.9";static getChart=ys;static register(...t){mt.add(...t),xs()}static unregister(...t){mt.remove(...t),xs()}constructor(t,e){const a=this.config=new ud(e),s=vs(t),r=ys(s);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const o=a.createResolver(a.chartOptionScopes(),this.getContext());this.platform=new(a.platform||function Lc(i){return!qi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Mc:Vc}(s)),this.platform.updateConfig(a);const l=this.platform.acquireContext(s,o.aspectRatio),d=l&&l.canvas,h=d&&d.height,u=d&&d.width;this.id=_o(),this.ctx=l,this.canvas=d,this.width=u,this.height=h,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ed,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function Fo(i,n){let t;return function(...e){return n?(clearTimeout(t),t=setTimeout(i,n,e)):i.apply(this,e),n}}(f=>this.update(f),o.resizeDelay||0),this._dataChanges=[],ui[this.id]=this,l&&d?(Ct.listen(this,"complete",bs),Ct.listen(this,"progress",_d),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:a,height:s,_aspectRatio:r}=this;return R(t)?e&&r?r:s?a/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return mt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Mn(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return fn(this.canvas,this.ctx),this}stop(){return Ct.stop(this),this}resize(t,e){Ct.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const a=this.options,o=this.platform.getMaximumSize(this.canvas,t,e,a.maintainAspectRatio&&this.aspectRatio),l=a.devicePixelRatio||this.platform.getDevicePixelRatio(),d=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,Mn(this,l,!0)&&(this.notifyPlugins("resize",{size:o}),B(a.onResize,[this,o],this),this.attached&&this._doResize(d)&&this.render())}ensureScalesHaveIDs(){L(this.options.scales||{},(a,s)=>{a.id=s})}buildOrUpdateScales(){const t=this.options,e=t.scales,a=this.scales,s=Object.keys(a).reduce((o,l)=>(o[l]=!1,o),{});let r=[];e&&(r=r.concat(Object.keys(e).map(o=>{const l=e[o],d=la(o,l),h="r"===d,u="x"===d;return{options:l,dposition:h?"chartArea":u?"bottom":"left",dtype:h?"radialLinear":u?"category":"linear"}}))),L(r,o=>{const l=o.options,d=l.id,h=la(d,l),u=A(l.type,o.dtype);(void 0===l.position||ms(l.position,h)!==ms(o.dposition))&&(l.position=o.dposition),s[d]=!0;let f=null;d in a&&a[d].type===u?f=a[d]:(f=new(mt.getScale(u))({id:d,type:u,ctx:this.ctx,chart:this}),a[f.id]=f),f.init(l,t)}),L(s,(o,l)=>{o||delete a[l]}),L(a,o=>{it.configure(this,o,o.options),it.addBox(this,o)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,a=t.length;if(t.sort((s,r)=>s.index-r.index),a>e){for(let s=e;s<a;++s)this._destroyDatasetMeta(s);t.splice(e,a-e)}this._sortedMetasets=t.slice(0).sort(_s("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((a,s)=>{0===e.filter(r=>r===a._dataset).length&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let a,s;for(this._removeUnreferencedMetasets(),a=0,s=e.length;a<s;a++){const r=e[a];let o=this.getDatasetMeta(a);const l=r.type||this.config.type;if(o.type&&o.type!==l&&(this._destroyDatasetMeta(a),o=this.getDatasetMeta(a)),o.type=l,o.indexAxis=r.indexAxis||oa(l,this.options),o.order=r.order||0,o.index=a,o.label=""+r.label,o.visible=this.isDatasetVisible(a),o.controller)o.controller.updateIndex(a),o.controller.linkScales();else{const d=mt.getController(l),{datasetElementType:h,dataElementType:u}=W.datasets[l];Object.assign(d,{dataElementType:mt.getElement(u),datasetElementType:h&&mt.getElement(h)}),o.controller=new d(this,a),t.push(o.controller)}}return this._updateMetasets(),t}_resetElements(){L(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const a=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!a.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let h=0,u=this.data.datasets.length;h<u;h++){const{controller:f}=this.getDatasetMeta(h),p=!s&&-1===r.indexOf(f);f.buildOrUpdateElements(p),o=Math.max(+f.getMaxOverflow(),o)}o=this._minPadding=a.layout.autoPadding?o:0,this._updateLayout(o),s||L(r,h=>{h.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(_s("z","_idx"));const{_active:l,_lastEvent:d}=this;d?this._eventHandler(d,!0):l.length&&this._updateHoverStyles(l,l,!0),this.render()}_updateScales(){L(this.scales,t=>{it.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),a=new Set(t.events);(!Xa(e,a)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:a,start:s,count:r}of e)bd(t,s,"_removeElements"===a?-r:r)}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,a=r=>new Set(t.filter(o=>o[0]===r).map((o,l)=>l+","+o.splice(1).join(","))),s=a(0);for(let r=1;r<e;r++)if(!Xa(s,a(r)))return;return Array.from(s).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;it.update(this,this.width,this.height,t);const e=this.chartArea,a=e.width<=0||e.height<=0;this._layers=[],L(this.boxes,s=>{a&&"chartArea"===s.position||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,r)=>{s._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let e=0,a=this.data.datasets.length;e<a;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,a=this.data.datasets.length;e<a;++e)this._updateDataset(e,Tt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const a=this.getDatasetMeta(t),s={meta:a,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(a.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(Ct.has(this)?this.attached&&!Ct.running(this)&&Ct.start(this):(this.draw(),bs({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:a,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(a,s)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,a=[];let s,r;for(s=0,r=e.length;s<r;++s){const o=e[s];(!t||o.visible)&&a.push(o)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,a={meta:t,index:t.index,cancelable:!0},s=Fn(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",a)&&(s&&Ze(e,s),t.controller.draw(),s&&ti(e),a.cancelable=!1,this.notifyPlugins("afterDatasetDraw",a))}isPointInArea(t){return Mt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,a,s){const r=mc.modes[e];return"function"==typeof r?r(this,t,a,s):[]}getDatasetMeta(t){const e=this.data.datasets[t],a=this._metasets;let s=a.filter(r=>r&&r._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},a.push(s)),s}getContext(){return this.$context||(this.$context=Pt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const a=this.getDatasetMeta(t);return"boolean"==typeof a.hidden?!a.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,a){const s=a?"show":"hide",r=this.getDatasetMeta(t),o=r.controller._resolveAnimations(void 0,s);me(e)?(r.data[e].hidden=!a,this.update()):(this.setDatasetVisibility(t,a),o.update(r,{visible:a}),this.update(l=>l.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),Ct.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),fn(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ui[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,a=(r,o)=>{e.addEventListener(this,r,o),t[r]=o},s=(r,o,l)=>{r.offsetX=o,r.offsetY=l,this._eventHandler(r)};L(this.options.events,r=>a(r,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,a=(d,h)=>{e.addEventListener(this,d,h),t[d]=h},s=(d,h)=>{t[d]&&(e.removeEventListener(this,d,h),delete t[d])},r=(d,h)=>{this.canvas&&this.resize(d,h)};let o;const l=()=>{s("attach",l),this.attached=!0,this.resize(),a("resize",r),a("detach",o)};o=()=>{this.attached=!1,s("resize",r),this._stop(),this._resize(0,0),a("attach",l)},e.isAttached(this.canvas)?l():o()}unbindEvents(){L(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},L(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,a){const s=a?"set":"remove";let r,o,l,d;for("dataset"===e&&(r=this.getDatasetMeta(t[0].datasetIndex),r.controller["_"+s+"DatasetHoverStyle"]()),l=0,d=t.length;l<d;++l){o=t[l];const h=o&&this.getDatasetMeta(o.datasetIndex).controller;h&&h[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],a=t.map(({datasetIndex:r,index:o})=>{const l=this.getDatasetMeta(r);if(!l)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:l.data[o],index:o}});!Ge(a,e)&&(this._active=a,this._lastEvent=null,this._updateHoverStyles(a,e))}notifyPlugins(t,e,a){return this._plugins.notify(this,t,e,a)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,a){const s=this.options.hover,r=(d,h)=>d.filter(u=>!h.some(f=>u.datasetIndex===f.datasetIndex&&u.index===f.index)),o=r(e,t),l=a?t:r(t,e);o.length&&this.updateHoverStyle(o,s.mode,!1),l.length&&s.mode&&this.updateHoverStyle(l,s.mode,!0)}_eventHandler(t,e){const a={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=o=>(o.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",a,s))return;const r=this._handleEvent(t,e,a.inChartArea);return a.cancelable=!1,this.notifyPlugins("afterEvent",a,s),(r||a.changed)&&this.render(),this}_handleEvent(t,e,a){const{_active:s=[],options:r}=this,l=this._getActiveElements(t,s,a,e),d=function ko(i){return"mouseup"===i.type||"click"===i.type||"contextmenu"===i.type}(t),h=function vd(i,n,t,e){return t&&"mouseout"!==i.type?e?n:i:null}(t,this._lastEvent,a,d);a&&(this._lastEvent=null,B(r.onHover,[t,l,this],this),d&&B(r.onClick,[t,l,this],this));const u=!Ge(l,s);return(u||e)&&(this._active=l,this._updateHoverStyles(l,s,e)),this._lastEvent=h,u}_getActiveElements(t,e,a,s){if("mouseout"===t.type)return[];if(!a)return e;const r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,s)}})();function xs(){return L(ca.instances,i=>i._plugins.invalidate())}function oe(i,n,t,e){return{x:t+i*Math.cos(n),y:e+i*Math.sin(n)}}function fi(i,n,t,e,a,s){const{x:r,y:o,startAngle:l,pixelMargin:d,innerRadius:h}=n,u=Math.max(n.outerRadius+e+t-d,0),f=h>0?h+e+t+d:0;let p=0;const g=a-l;if(e){const K=((h>0?h-e:0)+(u>0?u-e:0))/2;p=(g-(0!==K?g*K/(K+e):g))/2}const _=(g-Math.max(.001,g*u-t/H)/u)/2,v=l+_+p,b=a-_-p,{outerStart:x,outerEnd:D,innerStart:y,innerEnd:k}=function Dd(i,n,t,e){const a=function xd(i){return $i(i,["outerStart","outerEnd","innerStart","innerEnd"])}(i.options.borderRadius),s=(t-n)/2,r=Math.min(s,e*n/2),o=l=>{const d=(t-Math.min(s,l))*e/2;return Q(l,0,Math.min(s,d))};return{outerStart:o(a.outerStart),outerEnd:o(a.outerEnd),innerStart:Q(a.innerStart,0,r),innerEnd:Q(a.innerEnd,0,r)}}(n,f,u,b-v),C=u-x,M=u-D,w=v+x/C,I=b-D/M,T=f+y,F=f+k,tt=v+y/T,dt=b-k/F;if(i.beginPath(),s){const z=(w+I)/2;if(i.arc(r,o,u,w,z),i.arc(r,o,u,z,I),D>0){const at=oe(M,I,r,o);i.arc(at.x,at.y,D,I,b+X)}const $=oe(F,b,r,o);if(i.lineTo($.x,$.y),k>0){const at=oe(F,dt,r,o);i.arc(at.x,at.y,k,b+X,dt+Math.PI)}const K=(b-k/f+(v+y/f))/2;if(i.arc(r,o,f,b-k/f,K,!0),i.arc(r,o,f,K,v+y/f,!0),y>0){const at=oe(T,tt,r,o);i.arc(at.x,at.y,y,tt+Math.PI,v-X)}const ft=oe(C,v,r,o);if(i.lineTo(ft.x,ft.y),x>0){const at=oe(C,w,r,o);i.arc(at.x,at.y,x,v-X,w)}}else{i.moveTo(r,o);const z=Math.cos(w)*u+r,$=Math.sin(w)*u+o;i.lineTo(z,$);const K=Math.cos(I)*u+r,ft=Math.sin(I)*u+o;i.lineTo(K,ft)}i.closePath()}class Ds extends wt{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:n=>"borderDash"!==n};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(n){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,t,e){const a=this.getProps(["x","y"],e),{angle:s,distance:r}=Za(a,{x:n,y:t}),{startAngle:o,endAngle:l,innerRadius:d,outerRadius:h,circumference:u}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],e),f=(this.options.spacing+this.options.borderWidth)/2,p=A(u,l-o),g=be(s,o,l)&&o!==l,m=p>=Y||g,_=Dt(r,d+f,h+f);return m&&_}getCenterPoint(n){const{x:t,y:e,startAngle:a,endAngle:s,innerRadius:r,outerRadius:o}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:l,spacing:d}=this.options,h=(a+s)/2,u=(r+o+d+l)/2;return{x:t+Math.cos(h)*u,y:e+Math.sin(h)*u}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:t,circumference:e}=this,a=(t.offset||0)/4,s=(t.spacing||0)/2,r=t.circular;if(this.pixelMargin="inner"===t.borderAlign?.33:0,this.fullCircles=e>Y?Math.floor(e/Y):0,0===e||this.innerRadius<0||this.outerRadius<0)return;n.save();const o=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(o)*a,Math.sin(o)*a);const d=a*(1-Math.sin(Math.min(H,e||0)));n.fillStyle=t.backgroundColor,n.strokeStyle=t.borderColor,function kd(i,n,t,e,a){const{fullCircles:s,startAngle:r,circumference:o}=n;let l=n.endAngle;if(s){fi(i,n,t,e,l,a);for(let d=0;d<s;++d)i.fill();isNaN(o)||(l=r+(o%Y||Y))}fi(i,n,t,e,l,a),i.fill()}(n,this,d,s,r),function Md(i,n,t,e,a){const{fullCircles:s,startAngle:r,circumference:o,options:l}=n,{borderWidth:d,borderJoinStyle:h,borderDash:u,borderDashOffset:f}=l,p="inner"===l.borderAlign;if(!d)return;i.setLineDash(u||[]),i.lineDashOffset=f,p?(i.lineWidth=2*d,i.lineJoin=h||"round"):(i.lineWidth=d,i.lineJoin=h||"bevel");let g=n.endAngle;if(s){fi(i,n,t,e,g,a);for(let m=0;m<s;++m)i.stroke();isNaN(o)||(g=r+(o%Y||Y))}p&&function yd(i,n,t){const{startAngle:e,pixelMargin:a,x:s,y:r,outerRadius:o,innerRadius:l}=n;let d=a/o;i.beginPath(),i.arc(s,r,o,e-d,t+d),l>a?(d=a/l,i.arc(s,r,l,t+d,e-d,!0)):i.arc(s,r,a,t+X,e-X),i.closePath(),i.clip()}(i,n,g),s||(fi(i,n,t,e,g,a),i.stroke())}(n,this,d,s,r),n.restore()}}function ks(i,n,t=n){i.lineCap=A(t.borderCapStyle,n.borderCapStyle),i.setLineDash(A(t.borderDash,n.borderDash)),i.lineDashOffset=A(t.borderDashOffset,n.borderDashOffset),i.lineJoin=A(t.borderJoinStyle,n.borderJoinStyle),i.lineWidth=A(t.borderWidth,n.borderWidth),i.strokeStyle=A(t.borderColor,n.borderColor)}function Cd(i,n,t){i.lineTo(t.x,t.y)}function Ms(i,n,t={}){const e=i.length,{start:a=0,end:s=e-1}=t,{start:r,end:o}=n,l=Math.max(a,r),d=Math.min(s,o);return{count:e,start:l,loop:n.loop,ilen:d<l&&!(a<r&&s<r||a>o&&s>o)?e+d-l:d-l}}function Sd(i,n,t,e){const{points:a,options:s}=n,{count:r,start:o,loop:l,ilen:d}=Ms(a,t,e),h=function wd(i){return i.stepped?$o:i.tension||"monotone"===i.cubicInterpolationMode?Go:Cd}(s);let p,g,m,{move:u=!0,reverse:f}=e||{};for(p=0;p<=d;++p)g=a[(o+(f?d-p:p))%r],!g.skip&&(u?(i.moveTo(g.x,g.y),u=!1):h(i,m,g,f,s.stepped),m=g);return l&&(g=a[(o+(f?d:0))%r],h(i,m,g,f,s.stepped)),!!l}function Ad(i,n,t,e){const a=n.points,{count:s,start:r,ilen:o}=Ms(a,t,e),{move:l=!0,reverse:d}=e||{};let f,p,g,m,_,v,h=0,u=0;const b=D=>(r+(d?o-D:D))%s,x=()=>{m!==_&&(i.lineTo(h,_),i.lineTo(h,m),i.lineTo(h,v))};for(l&&(p=a[b(0)],i.moveTo(p.x,p.y)),f=0;f<=o;++f){if(p=a[b(f)],p.skip)continue;const D=p.x,y=p.y,k=0|D;k===g?(y<m?m=y:y>_&&(_=y),h=(u*h+D)/++u):(x(),i.lineTo(D,y),g=k,u=0,m=_=y),v=y}x()}function da(i){const n=i.options;return i._decimated||i._loop||n.tension||"monotone"===n.cubicInterpolationMode||n.stepped||n.borderDash&&n.borderDash.length?Sd:Ad}const Id="function"==typeof Path2D;let pi=(()=>class i extends wt{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const a=this.options;!a.tension&&"monotone"!==a.cubicInterpolationMode||a.stepped||this._pointsUpdated||(_l(this._points,a,t,a.spanGaps?this._loop:this._fullLoop,e),this._pointsUpdated=!0)}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function Il(i,n){const t=i.points,e=i.options.spanGaps,a=t.length;if(!a)return[];const s=!!i._loop,{start:r,end:o}=function El(i,n,t,e){let a=0,s=n-1;if(t&&!e)for(;a<n&&!i[a].skip;)a++;for(;a<n&&i[a].skip;)a++;for(a%=n,t&&(s+=a);s>a&&i[s%n].skip;)s--;return s%=n,{start:a,end:s}}(t,a,s,e);return function In(i,n,t,e){return e&&e.setContext&&t?function Pl(i,n,t,e){const a=i._chart.getContext(),s=Pn(i.options),{_datasetIndex:r,options:{spanGaps:o}}=i,l=t.length,d=[];let h=s,u=n[0].start,f=u;function p(g,m,_,v){const b=o?-1:1;if(g!==m){for(g+=l;t[g%l].skip;)g-=b;for(;t[m%l].skip;)m+=b;g%l!=m%l&&(d.push({start:g%l,end:m%l,loop:_,style:v}),h=v,u=m%l)}}for(const g of n){u=o?u:g.start;let _,m=t[u%l];for(f=u+1;f<=g.end;f++){const v=t[f%l];_=Pn(e.setContext(Pt(a,{type:"segment",p0:m,p1:v,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:r}))),Fl(_,h)&&p(u,f-1,g.loop,h),m=v,h=_}u<f-1&&p(u,f-1,g.loop,h)}return d}(i,n,t,e):n}(i,!0===e?[{start:r,end:o,loop:s}]:function Tl(i,n,t,e){const a=i.length,s=[];let l,r=n,o=i[n];for(l=n+1;l<=t;++l){const d=i[l%a];d.skip||d.stop?o.skip||(s.push({start:n%a,end:(l-1)%a,loop:e=!1}),n=r=d.stop?l:null):(r=l,o.skip&&(n=l)),o=d}return null!==r&&s.push({start:n%a,end:r%a,loop:e}),s}(t,r,o<r?o+a:o,!!i._fullLoop&&0===r&&o===a-1),t,n)}(this,this.options.segment))}first(){const t=this.segments;return t.length&&this.points[t[0].start]}last(){const t=this.segments,a=t.length;return a&&this.points[t[a-1].end]}interpolate(t,e){const a=this.options,s=t[e],r=this.points,o=Tn(this,{property:e,start:s,end:s});if(!o.length)return;const l=[],d=function Rd(i){return i.stepped?Cl:i.tension||"monotone"===i.cubicInterpolationMode?wl:$t}(a);let h,u;for(h=0,u=o.length;h<u;++h){const{start:f,end:p}=o[h],g=r[f],m=r[p];if(g===m){l.push(g);continue}const v=d(g,m,Math.abs((s-g[e])/(m[e]-g[e])),a.stepped);v[e]=t[e],l.push(v)}return 1===l.length?l[0]:l}pathSegment(t,e,a){return da(this)(t,this,e,a)}path(t,e,a){const s=this.segments,r=da(this);let o=this._loop;e=e||0,a=a||this.points.length-e;for(const l of s)o&=r(t,this,l,{start:e,end:e+a-1});return!!o}draw(t,e,a,s){(this.points||[]).length&&(this.options||{}).borderWidth&&(t.save(),function Pd(i,n,t,e){Id&&!n.options.segment?function Ed(i,n,t,e){let a=n._path;a||(a=n._path=new Path2D,n.path(a,t,e)&&a.closePath()),ks(i,n.options),i.stroke(a)}(i,n,t,e):function Td(i,n,t,e){const{segments:a,options:s}=n,r=da(n);for(const o of a)ks(i,s,o.style),i.beginPath(),r(i,n,o,{start:t,end:t+e-1})&&i.closePath(),i.stroke()}(i,n,t,e)}(t,this,a,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}})();function Cs(i,n,t,e){const a=i.options,{[t]:s}=i.getProps([t],e);return Math.abs(n-s)<a.radius+a.hitRadius}let ws=(()=>class i extends wt{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,a){const s=this.options,{x:r,y:o}=this.getProps(["x","y"],a);return Math.pow(t-r,2)+Math.pow(e-o,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return Cs(this,t,"x",e)}inYRange(t,e){return Cs(this,t,"y",e)}getCenterPoint(t){const{x:e,y:a}=this.getProps(["x","y"],t);return{x:e,y:a}}size(t){let e=(t=t||this.options||{}).radius||0;return e=Math.max(e,e&&t.hoverRadius||0),2*(e+(e&&t.borderWidth||0))}draw(t,e){const a=this.options;this.skip||a.radius<.1||!Mt(this,e,this.size(a)/2)||(t.strokeStyle=a.borderColor,t.lineWidth=a.borderWidth,t.fillStyle=a.backgroundColor,Ui(t,a,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}})();function Ss(i,n){const{x:t,y:e,base:a,width:s,height:r}=i.getProps(["x","y","base","width","height"],n);let o,l,d,h,u;return i.horizontal?(u=r/2,o=Math.min(t,a),l=Math.max(t,a),d=e-u,h=e+u):(u=s/2,o=t-u,l=t+u,d=Math.min(e,a),h=Math.max(e,a)),{left:o,top:d,right:l,bottom:h}}function Ot(i,n,t,e){return i?0:Q(n,t,e)}function ha(i,n,t,e){const a=null===n,s=null===t,o=i&&!(a&&s)&&Ss(i,e);return o&&(a||Dt(n,o.left,o.right))&&(s||Dt(t,o.top,o.bottom))}function Bd(i,n){i.rect(n.x,n.y,n.w,n.h)}function ua(i,n,t={}){const e=i.x!==t.x?-n:0,a=i.y!==t.y?-n:0;return{x:i.x+e,y:i.y+a,w:i.w+((i.x+i.w!==t.x+t.w?n:0)-e),h:i.h+((i.y+i.h!==t.y+t.h?n:0)-a),radius:i.radius}}class As extends wt{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(n){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,n&&Object.assign(this,n)}draw(n){const{inflateAmount:t,options:{borderColor:e,backgroundColor:a}}=this,{inner:s,outer:r}=function Vd(i){const n=Ss(i),t=n.right-n.left,e=n.bottom-n.top,a=function Fd(i,n,t){const a=i.borderSkipped,s=gn(i.options.borderWidth);return{t:Ot(a.top,s.top,0,t),r:Ot(a.right,s.right,0,n),b:Ot(a.bottom,s.bottom,0,t),l:Ot(a.left,s.left,0,n)}}(i,t/2,e/2),s=function Od(i,n,t){const{enableBorderRadius:e}=i.getProps(["enableBorderRadius"]),a=i.options.borderRadius,s=Yt(a),r=Math.min(n,t),o=i.borderSkipped,l=e||P(a);return{topLeft:Ot(!l||o.top||o.left,s.topLeft,0,r),topRight:Ot(!l||o.top||o.right,s.topRight,0,r),bottomLeft:Ot(!l||o.bottom||o.left,s.bottomLeft,0,r),bottomRight:Ot(!l||o.bottom||o.right,s.bottomRight,0,r)}}(i,t/2,e/2);return{outer:{x:n.left,y:n.top,w:t,h:e,radius:s},inner:{x:n.left+a.l,y:n.top+a.t,w:t-a.l-a.r,h:e-a.t-a.b,radius:{topLeft:Math.max(0,s.topLeft-Math.max(a.t,a.l)),topRight:Math.max(0,s.topRight-Math.max(a.t,a.r)),bottomLeft:Math.max(0,s.bottomLeft-Math.max(a.b,a.l)),bottomRight:Math.max(0,s.bottomRight-Math.max(a.b,a.r))}}}}(this),o=function Ld(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}(r.radius)?De:Bd;n.save(),(r.w!==s.w||r.h!==s.h)&&(n.beginPath(),o(n,ua(r,t,s)),n.clip(),o(n,ua(s,-t,r)),n.fillStyle=e,n.fill("evenodd")),n.beginPath(),o(n,ua(s,t)),n.fillStyle=a,n.fill(),n.restore()}inRange(n,t,e){return ha(this,n,t,e)}inXRange(n,t){return ha(this,n,null,t)}inYRange(n,t){return ha(this,null,n,t)}getCenterPoint(n){const{x:t,y:e,base:a,horizontal:s}=this.getProps(["x","y","base","horizontal"],n);return{x:s?(t+a)/2:t,y:s?e:(e+a)/2}}getRange(n){return"x"===n?this.width/2:this.height/2}}var zd=Object.freeze({__proto__:null,ArcElement:Ds,BarElement:As,LineElement:pi,PointElement:ws});const fa=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Rs=fa.map(i=>i.replace("rgb(","rgba(").replace(")",", 0.5)"));function Es(i){return fa[i%fa.length]}function Ts(i){return Rs[i%Rs.length]}function Is(i){let n;for(n in i)if(i[n].borderColor||i[n].backgroundColor)return!0;return!1}var $d={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(i,n,t){if(!t.enabled)return;const{data:{datasets:e},options:a}=i.config,{elements:s}=a,r=Is(e)||function Wd(i){return i&&(i.borderColor||i.backgroundColor)}(a)||s&&Is(s)||function Ud(){return"rgba(0,0,0,0.1)"!==W.borderColor||"rgba(0,0,0,0.1)"!==W.backgroundColor}();if(!t.forceOverride&&r)return;const o=function Yd(i){let n=0;return(t,e)=>{const a=i.getDatasetMeta(e).controller;a instanceof ia?n=function jd(i,n){return i.backgroundColor=i.data.map(()=>Es(n++)),n}(t,n):a instanceof Gn?n=function Hd(i,n){return i.backgroundColor=i.data.map(()=>Ts(n++)),n}(t,n):a&&(n=function Nd(i,n){return i.borderColor=Es(n),i.backgroundColor=Ts(n),++n}(t,n))}}(i);e.forEach(o)}};function Ps(i){if(i._decimated){const n=i._data;delete i._decimated,delete i._data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,writable:!0,value:n})}}function Fs(i){i.data.datasets.forEach(n=>{Ps(n)})}var qd={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(i,n,t)=>{if(!t.enabled)return void Fs(i);const e=i.width;i.data.datasets.forEach((a,s)=>{const{_data:r,indexAxis:o}=a,l=i.getDatasetMeta(s),d=r||a.data;if("y"===U([o,i.options.indexAxis])||!l.controller.supportsDecimation)return;const h=i.scales[l.xAxisID];if("linear"!==h.type&&"time"!==h.type||i.options.parsing)return;let g,{start:u,count:f}=function Kd(i,n){const t=n.length;let a,e=0;const{iScale:s}=i,{min:r,max:o,minDefined:l,maxDefined:d}=s.getUserBounds();return l&&(e=Q(kt(n,s.axis,r).lo,0,t-1)),a=d?Q(kt(n,s.axis,o).hi+1,e,t)-e:t-e,{start:e,count:a}}(l,d);if(f<=(t.threshold||4*e))Ps(a);else{switch(R(r)&&(a._data=d,delete a.data,Object.defineProperty(a,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(m){this._data=m}})),t.algorithm){case"lttb":g=function Gd(i,n,t,e,a){const s=a.samples||e;if(s>=t)return i.slice(n,n+t);const r=[],o=(t-2)/(s-2);let l=0;const d=n+t-1;let u,f,p,g,m,h=n;for(r[l++]=i[h],u=0;u<s-2;u++){let b,_=0,v=0;const x=Math.floor((u+1)*o)+1+n,D=Math.min(Math.floor((u+2)*o)+1,t)+n,y=D-x;for(b=x;b<D;b++)_+=i[b].x,v+=i[b].y;_/=y,v/=y;const k=Math.floor(u*o)+1+n,C=Math.min(Math.floor((u+1)*o)+1,t)+n,{x:M,y:w}=i[h];for(p=g=-1,b=k;b<C;b++)g=.5*Math.abs((M-_)*(i[b].y-w)-(M-i[b].x)*(v-w)),g>p&&(p=g,f=i[b],m=b);r[l++]=f,h=m}return r[l++]=i[d],r}(d,u,f,e,t);break;case"min-max":g=function Xd(i,n,t,e){let r,o,l,d,h,u,f,p,g,m,a=0,s=0;const _=[],b=i[n].x,D=i[n+t-1].x-b;for(r=n;r<n+t;++r){o=i[r],l=(o.x-b)/D*e,d=o.y;const y=0|l;if(y===h)d<g?(g=d,u=r):d>m&&(m=d,f=r),a=(s*a+o.x)/++s;else{const k=r-1;if(!R(u)&&!R(f)){const C=Math.min(u,f),M=Math.max(u,f);C!==p&&C!==k&&_.push({...i[C],x:a}),M!==p&&M!==k&&_.push({...i[M],x:a})}r>0&&k!==p&&_.push(i[k]),_.push(o),h=y,s=0,g=m=d,u=f=p=r}}return _}(d,u,f,e);break;default:throw new Error(`Unsupported decimation algorithm '${t.algorithm}'`)}a._decimated=g}})},destroy(i){Fs(i)}};function pa(i,n,t,e){if(e)return;let a=n[i],s=t[i];return"angle"===i&&(a=lt(a),s=lt(s)),{property:i,start:a,end:s}}function ga(i,n,t){for(;n>i;n--){const e=t[n];if(!isNaN(e.x)&&!isNaN(e.y))break}return n}function Os(i,n,t,e){return i&&n?e(i[t],n[t]):i?i[t]:n?n[t]:0}function Vs(i,n){let t=[],e=!1;return j(i)?(e=!0,t=i):t=function Jd(i,n){const{x:t=null,y:e=null}=i||{},a=n.points,s=[];return n.segments.forEach(({start:r,end:o})=>{o=ga(r,o,a);const l=a[r],d=a[o];null!==e?(s.push({x:l.x,y:e}),s.push({x:d.x,y:e})):null!==t&&(s.push({x:t,y:l.y}),s.push({x:t,y:d.y}))}),s}(i,n),t.length?new pi({points:t,options:{tension:0},_loop:e,_fullLoop:e}):null}function Ls(i){return i&&!1!==i.fill}function Zd(i,n,t){let a=i[n].fill;const s=[n];let r;if(!t)return a;for(;!1!==a&&-1===s.indexOf(a);){if(!G(a))return a;if(r=i[a],!r)return!1;if(r.visible)return a;s.push(a),a=r.fill}return!1}function th(i,n,t){const e=function nh(i){const n=i.options,t=n.fill;let e=A(t&&t.target,t);return void 0===e&&(e=!!n.backgroundColor),!1!==e&&null!==e&&(!0===e?"origin":e)}(i);if(P(e))return!isNaN(e.value)&&e;let a=parseFloat(e);return G(a)&&Math.floor(a)===a?function eh(i,n,t,e){return("-"===i||"+"===i)&&(t=n+t),!(t===n||t<0||t>=e)&&t}(e[0],n,a,t):["origin","start","end","stack","shape"].indexOf(e)>=0&&e}function oh(i,n,t){const e=[];for(let a=0;a<t.length;a++){const s=t[a],{first:r,last:o,point:l}=lh(s,n,"x");if(!(!l||r&&o))if(r)e.unshift(l);else if(i.push(l),!o)break}i.push(...e)}function lh(i,n,t){const e=i.interpolate(n,t);if(!e)return{};const a=e[t],s=i.segments,r=i.points;let o=!1,l=!1;for(let d=0;d<s.length;d++){const h=s[d],u=r[h.start][t],f=r[h.end][t];if(Dt(a,u,f)){o=a===u,l=a===f;break}}return{first:o,last:l,point:e}}class Bs{constructor(n){this.x=n.x,this.y=n.y,this.radius=n.radius}pathSegment(n,t,e){const{x:a,y:s,radius:r}=this;return n.arc(a,s,r,(t=t||{start:0,end:Y}).end,t.start,!0),!e.bounds}interpolate(n){const{x:t,y:e,radius:a}=this,s=n.angle;return{x:t+Math.cos(s)*a,y:e+Math.sin(s)*a,angle:s}}}function ma(i,n,t){const e=function ch(i){const{chart:n,fill:t,line:e}=i;if(G(t))return function dh(i,n){const t=i.getDatasetMeta(n);return t&&i.isDatasetVisible(n)?t.dataset:null}(n,t);if("stack"===t)return function sh(i){const{scale:n,index:t,line:e}=i,a=[],s=e.segments,r=e.points,o=function rh(i,n){const t=[],e=i.getMatchingVisibleMetas("line");for(let a=0;a<e.length;a++){const s=e[a];if(s.index===n)break;s.hidden||t.unshift(s.dataset)}return t}(n,t);o.push(Vs({x:null,y:n.bottom},e));for(let l=0;l<s.length;l++){const d=s[l];for(let h=d.start;h<=d.end;h++)oh(a,r[h],o)}return new pi({points:a,options:{}})}(i);if("shape"===t)return!0;const a=function hh(i){return(i.scale||{}).getPointPositionForValue?function fh(i){const{scale:n,fill:t}=i,e=n.options,a=n.getLabels().length,s=e.reverse?n.max:n.min,r=function ah(i,n,t){let e;return e="start"===i?t:"end"===i?n.options.reverse?n.min:n.max:P(i)?i.value:n.getBaseValue(),e}(t,n,s),o=[];if(e.grid.circular){const l=n.getPointPositionForValue(0,s);return new Bs({x:l.x,y:l.y,radius:n.getDistanceFromCenterForValue(r)})}for(let l=0;l<a;++l)o.push(n.getPointPositionForValue(l,r));return o}(i):function uh(i){const{scale:n={},fill:t}=i,e=function ih(i,n){let t=null;return"start"===i?t=n.bottom:"end"===i?t=n.top:P(i)?t=n.getPixelForValue(i.value):n.getBasePixel&&(t=n.getBasePixel()),t}(t,n);if(G(e)){const a=n.isHorizontal();return{x:a?e:null,y:a?null:e}}return null}(i)}(i);return a instanceof Bs?a:Vs(a,e)}(n),{chart:a,index:s,line:r,scale:o,axis:l}=n,d=r.options,h=d.fill,u=d.backgroundColor,{above:f=u,below:p=u}=h||{},g=a.getDatasetMeta(s),m=Fn(a,g);e&&r.points.length&&(Ze(i,t),function ph(i,n){const{line:t,target:e,above:a,below:s,area:r,scale:o,clip:l}=n,d=t._loop?"angle":n.axis;i.save(),"x"===d&&s!==a&&(zs(i,e,r.top),Ns(i,{line:t,target:e,color:a,scale:o,property:d,clip:l}),i.restore(),i.save(),zs(i,e,r.bottom)),Ns(i,{line:t,target:e,color:s,scale:o,property:d,clip:l}),i.restore()}(i,{line:r,target:e,above:f,below:p,area:t,scale:o,axis:l,clip:m}),ti(i))}function zs(i,n,t){const{segments:e,points:a}=n;let s=!0,r=!1;i.beginPath();for(const o of e){const{start:l,end:d}=o,h=a[l],u=a[ga(l,d,a)];s?(i.moveTo(h.x,h.y),s=!1):(i.lineTo(h.x,t),i.lineTo(h.x,h.y)),r=!!n.pathSegment(i,o,{move:r}),r?i.closePath():i.lineTo(u.x,t)}i.lineTo(n.first().x,t),i.closePath(),i.clip()}function Ns(i,n){const{line:t,target:e,property:a,color:s,scale:r,clip:o}=n,l=function Qd(i,n,t){const e=i.segments,a=i.points,s=n.points,r=[];for(const o of e){let{start:l,end:d}=o;d=ga(l,d,a);const h=pa(t,a[l],a[d],o.loop);if(!n.segments){r.push({source:o,target:h,start:a[l],end:a[d]});continue}const u=Tn(n,h);for(const f of u){const p=pa(t,s[f.start],s[f.end],f.loop),g=En(o,a,p);for(const m of g)r.push({source:m,target:f,start:{[t]:Os(h,p,"start",Math.max)},end:{[t]:Os(h,p,"end",Math.min)}})}}return r}(t,e,a);for(const{source:d,target:h,start:u,end:f}of l){const{style:{backgroundColor:p=s}={}}=d,g=!0!==e;i.save(),i.fillStyle=p,gh(i,r,o,g&&pa(a,u,f)),i.beginPath();const m=!!t.pathSegment(i,d);let _;if(g){m?i.closePath():js(i,e,f,a);const v=!!e.pathSegment(i,h,{move:m,reverse:!0});_=m&&v,_||js(i,e,u,a)}i.closePath(),i.fill(_?"evenodd":"nonzero"),i.restore()}}function gh(i,n,t,e){const a=n.chart.chartArea,{property:s,start:r,end:o}=e||{};if("x"===s||"y"===s){let l,d,h,u;"x"===s?(l=r,d=a.top,h=o,u=a.bottom):(l=a.left,d=r,h=a.right,u=o),i.beginPath(),t&&(l=Math.max(l,t.left),h=Math.min(h,t.right),d=Math.max(d,t.top),u=Math.min(u,t.bottom)),i.rect(l,d,h-l,u-d),i.clip()}}function js(i,n,t,e){const a=n.interpolate(t,e);a&&i.lineTo(a.x,a.y)}var mh={id:"filler",afterDatasetsUpdate(i,n,t){const e=(i.data.datasets||[]).length,a=[];let s,r,o,l;for(r=0;r<e;++r)s=i.getDatasetMeta(r),o=s.dataset,l=null,o&&o.options&&o instanceof pi&&(l={visible:i.isDatasetVisible(r),index:r,fill:th(o,r,e),chart:i,axis:s.controller.options.indexAxis,scale:s.vScale,line:o}),s.$filler=l,a.push(l);for(r=0;r<e;++r)l=a[r],l&&!1!==l.fill&&(l.fill=Zd(a,r,t.propagate))},beforeDraw(i,n,t){const e="beforeDraw"===t.drawTime,a=i.getSortedVisibleDatasetMetas(),s=i.chartArea;for(let r=a.length-1;r>=0;--r){const o=a[r].$filler;o&&(o.line.updateControlPoints(s,o.axis),e&&o.fill&&ma(i.ctx,o,s))}},beforeDatasetsDraw(i,n,t){if("beforeDatasetsDraw"!==t.drawTime)return;const e=i.getSortedVisibleDatasetMetas();for(let a=e.length-1;a>=0;--a){const s=e[a].$filler;Ls(s)&&ma(i.ctx,s,i.chartArea)}},beforeDatasetDraw(i,n,t){const e=n.meta.$filler;!Ls(e)||"beforeDatasetDraw"!==t.drawTime||ma(i.ctx,e,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const Hs=(i,n)=>{let{boxHeight:t=n,boxWidth:e=n}=i;return i.usePointStyle&&(t=Math.min(t,n),e=i.pointStyleWidth||Math.min(e,n)),{boxWidth:e,boxHeight:t,itemHeight:Math.max(n,t)}};class Ys extends wt{constructor(n){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=n.chart,this.options=n.options,this.ctx=n.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(n,t,e){this.maxWidth=n,this.maxHeight=t,this._margins=e,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const n=this.options.labels||{};let t=B(n.generateLabels,[this.chart],this)||[];n.filter&&(t=t.filter(e=>n.filter(e,this.chart.data))),n.sort&&(t=t.sort((e,a)=>n.sort(e,a,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){const{options:n,ctx:t}=this;if(!n.display)return void(this.width=this.height=0);const e=n.labels,a=q(e.font),s=a.size,r=this._computeTitleHeight(),{boxWidth:o,itemHeight:l}=Hs(e,s);let d,h;t.font=a.string,this.isHorizontal()?(d=this.maxWidth,h=this._fitRows(r,s,o,l)+10):(h=this.maxHeight,d=this._fitCols(r,a,o,l)+10),this.width=Math.min(d,n.maxWidth||this.maxWidth),this.height=Math.min(h,n.maxHeight||this.maxHeight)}_fitRows(n,t,e,a){const{ctx:s,maxWidth:r,options:{labels:{padding:o}}}=this,l=this.legendHitBoxes=[],d=this.lineWidths=[0],h=a+o;let u=n;s.textAlign="left",s.textBaseline="middle";let f=-1,p=-h;return this.legendItems.forEach((g,m)=>{const _=e+t/2+s.measureText(g.text).width;(0===m||d[d.length-1]+_+2*o>r)&&(u+=h,d[d.length-(m>0?0:1)]=0,p+=h,f++),l[m]={left:0,top:p,row:f,width:_,height:a},d[d.length-1]+=_+o}),u}_fitCols(n,t,e,a){const{ctx:s,maxHeight:r,options:{labels:{padding:o}}}=this,l=this.legendHitBoxes=[],d=this.columnSizes=[],h=r-n;let u=o,f=0,p=0,g=0,m=0;return this.legendItems.forEach((_,v)=>{const{itemWidth:b,itemHeight:x}=function bh(i,n,t,e,a){const s=function vh(i,n,t,e){let a=i.text;return a&&"string"!=typeof a&&(a=a.reduce((s,r)=>s.length>r.length?s:r)),n+t.size/2+e.measureText(a).width}(e,i,n,t),r=function yh(i,n,t){let e=i;return"string"!=typeof n.text&&(e=Ws(n,t)),e}(a,e,n.lineHeight);return{itemWidth:s,itemHeight:r}}(e,t,s,_,a);v>0&&p+x+2*o>h&&(u+=f+o,d.push({width:f,height:p}),g+=f+o,m++,f=p=0),l[v]={left:g,top:p,col:m,width:b,height:x},f=Math.max(f,b),p+=x+o}),u+=f,d.push({width:f,height:p}),u}adjustHitBoxes(){if(!this.options.display)return;const n=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:e,labels:{padding:a},rtl:s}}=this,r=re(s,this.left,this.width);if(this.isHorizontal()){let o=0,l=et(e,this.left+a,this.right-this.lineWidths[o]);for(const d of t)o!==d.row&&(o=d.row,l=et(e,this.left+a,this.right-this.lineWidths[o])),d.top+=this.top+n+a,d.left=r.leftForLtr(r.x(l),d.width),l+=d.width+a}else{let o=0,l=et(e,this.top+n+a,this.bottom-this.columnSizes[o].height);for(const d of t)d.col!==o&&(o=d.col,l=et(e,this.top+n+a,this.bottom-this.columnSizes[o].height)),d.top=l,d.left+=this.left+a,d.left=r.leftForLtr(r.x(d.left),d.width),l+=d.height+a}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const n=this.ctx;Ze(n,this),this._draw(),ti(n)}}_draw(){const{options:n,columnSizes:t,lineWidths:e,ctx:a}=this,{align:s,labels:r}=n,o=W.color,l=re(n.rtl,this.left,this.width),d=q(r.font),{padding:h}=r,u=d.size,f=u/2;let p;this.drawTitle(),a.textAlign=l.textAlign("left"),a.textBaseline="middle",a.lineWidth=.5,a.font=d.string;const{boxWidth:g,boxHeight:m,itemHeight:_}=Hs(r,u),x=this.isHorizontal(),D=this._computeTitleHeight();p=x?{x:et(s,this.left+h,this.right-e[0]),y:this.top+h+D,line:0}:{x:this.left+h,y:et(s,this.top+D+h,this.bottom-t[0].height),line:0},wn(this.ctx,n.textDirection);const y=_+h;this.legendItems.forEach((k,C)=>{a.strokeStyle=k.fontColor,a.fillStyle=k.fontColor;const M=a.measureText(k.text).width,w=l.textAlign(k.textAlign||(k.textAlign=r.textAlign)),I=g+f+M;let T=p.x,F=p.y;l.setWidth(this.width),x?C>0&&T+I+h>this.right&&(F=p.y+=y,p.line++,T=p.x=et(s,this.left+h,this.right-e[p.line])):C>0&&F+y>this.bottom&&(T=p.x=T+t[p.line].width+h,p.line++,F=p.y=et(s,this.top+D+h,this.bottom-t[p.line].height)),function(k,C,M){if(isNaN(g)||g<=0||isNaN(m)||m<0)return;a.save();const w=A(M.lineWidth,1);if(a.fillStyle=A(M.fillStyle,o),a.lineCap=A(M.lineCap,"butt"),a.lineDashOffset=A(M.lineDashOffset,0),a.lineJoin=A(M.lineJoin,"miter"),a.lineWidth=w,a.strokeStyle=A(M.strokeStyle,o),a.setLineDash(A(M.lineDash,[])),r.usePointStyle){const I={radius:m*Math.SQRT2/2,pointStyle:M.pointStyle,rotation:M.rotation,borderWidth:w},T=l.xPlus(k,g/2);pn(a,I,T,C+f,r.pointStyleWidth&&g)}else{const I=C+Math.max((u-m)/2,0),T=l.leftForLtr(k,g),F=Yt(M.borderRadius);a.beginPath(),Object.values(F).some(tt=>0!==tt)?De(a,{x:T,y:I,w:g,h:m,radius:F}):a.rect(T,I,g,m),a.fill(),0!==w&&a.stroke()}a.restore()}(l.x(T),F,k),T=((i,n,t,e)=>i===(e?"left":"right")?t:"center"===i?(n+t)/2:n)(w,T+g+f,x?T+I:this.right,n.rtl),function(k,C,M){Ht(a,M.text,k,C+_/2,d,{strikethrough:M.hidden,textAlign:l.textAlign(M.textAlign)})}(l.x(T),F,k),x?p.x+=I+h:p.y+="string"!=typeof k.text?Ws(k,d.lineHeight)+h:y}),Sn(this.ctx,n.textDirection)}drawTitle(){const n=this.options,t=n.title,e=q(t.font),a=J(t.padding);if(!t.display)return;const s=re(n.rtl,this.left,this.width),r=this.ctx,o=t.position,d=a.top+e.size/2;let h,u=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),h=this.top+d,u=et(n.align,u,this.right-f);else{const g=this.columnSizes.reduce((m,_)=>Math.max(m,_.height),0);h=d+et(n.align,this.top,this.bottom-g-n.labels.padding-this._computeTitleHeight())}const p=et(o,u,u+f);r.textAlign=s.textAlign(Ni(o)),r.textBaseline="middle",r.strokeStyle=t.color,r.fillStyle=t.color,r.font=e.string,Ht(r,t.text,p,h,e)}_computeTitleHeight(){const n=this.options.title,t=q(n.font),e=J(n.padding);return n.display?t.lineHeight+e.height:0}_getLegendItemAt(n,t){let e,a,s;if(Dt(n,this.left,this.right)&&Dt(t,this.top,this.bottom))for(s=this.legendHitBoxes,e=0;e<s.length;++e)if(a=s[e],Dt(n,a.left,a.left+a.width)&&Dt(t,a.top,a.top+a.height))return this.legendItems[e];return null}handleEvent(n){const t=this.options;if(!function xh(i,n){return!(("mousemove"!==i&&"mouseout"!==i||!n.onHover&&!n.onLeave)&&(!n.onClick||"click"!==i&&"mouseup"!==i))}(n.type,t))return;const e=this._getLegendItemAt(n.x,n.y);if("mousemove"===n.type||"mouseout"===n.type){const a=this._hoveredItem,s=((i,n)=>null!==i&&null!==n&&i.datasetIndex===n.datasetIndex&&i.index===n.index)(a,e);a&&!s&&B(t.onLeave,[n,a,this],this),this._hoveredItem=e,e&&!s&&B(t.onHover,[n,e,this],this)}else e&&B(t.onClick,[n,e,this],this)}}function Ws(i,n){return n*(i.text?i.text.length:0)}var Dh={id:"legend",_element:Ys,start(i,n,t){const e=i.legend=new Ys({ctx:i.ctx,options:t,chart:i});it.configure(i,e,t),it.addBox(i,e)},stop(i){it.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,n,t){const e=i.legend;it.configure(i,e,t),e.options=t},afterUpdate(i){const n=i.legend;n.buildLabels(),n.adjustHitBoxes()},afterEvent(i,n){n.replay||i.legend.handleEvent(n.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,n,t){const e=n.datasetIndex,a=t.chart;a.isDatasetVisible(e)?(a.hide(e),n.hidden=!0):(a.show(e),n.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const n=i.data.datasets,{labels:{usePointStyle:t,pointStyle:e,textAlign:a,color:s,useBorderRadius:r,borderRadius:o}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const d=l.controller.getStyle(t?0:void 0),h=J(d.borderWidth);return{text:n[l.index].label,fillStyle:d.backgroundColor,fontColor:s,hidden:!l.visible,lineCap:d.borderCapStyle,lineDash:d.borderDash,lineDashOffset:d.borderDashOffset,lineJoin:d.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:d.borderColor,pointStyle:e||d.pointStyle,rotation:d.rotation,textAlign:a||d.textAlign,borderRadius:r&&(o||d.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class _a extends wt{constructor(n){super(),this.chart=n.chart,this.options=n.options,this.ctx=n.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(n,t){const e=this.options;if(this.left=0,this.top=0,!e.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=n,this.height=this.bottom=t;const a=j(e.text)?e.text.length:1;this._padding=J(e.padding);const s=a*q(e.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const n=this.options.position;return"top"===n||"bottom"===n}_drawArgs(n){const{top:t,left:e,bottom:a,right:s,options:r}=this,o=r.align;let d,h,u,l=0;return this.isHorizontal()?(h=et(o,e,s),u=t+n,d=s-e):("left"===r.position?(h=e+n,u=et(o,a,t),l=-.5*H):(h=s-n,u=et(o,t,a),l=.5*H),d=a-t),{titleX:h,titleY:u,maxWidth:d,rotation:l}}draw(){const n=this.ctx,t=this.options;if(!t.display)return;const e=q(t.font),s=e.lineHeight/2+this._padding.top,{titleX:r,titleY:o,maxWidth:l,rotation:d}=this._drawArgs(s);Ht(n,t.text,0,0,e,{color:t.color,maxWidth:l,rotation:d,textAlign:Ni(t.align),textBaseline:"middle",translation:[r,o]})}}var Mh={id:"title",_element:_a,start(i,n,t){!function kh(i,n){const t=new _a({ctx:i.ctx,options:n,chart:i});it.configure(i,t,n),it.addBox(i,t),i.titleBlock=t}(i,t)},stop(i){it.removeBox(i,i.titleBlock),delete i.titleBlock},beforeUpdate(i,n,t){const e=i.titleBlock;it.configure(i,e,t),e.options=t},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const gi=new WeakMap;var Ch={id:"subtitle",start(i,n,t){const e=new _a({ctx:i.ctx,options:t,chart:i});it.configure(i,e,t),it.addBox(i,e),gi.set(i,e)},stop(i){it.removeBox(i,gi.get(i)),gi.delete(i)},beforeUpdate(i,n,t){const e=gi.get(i);it.configure(i,e,t),e.options=t},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Te={average(i){if(!i.length)return!1;let n,t,e=new Set,a=0,s=0;for(n=0,t=i.length;n<t;++n){const o=i[n].element;if(o&&o.hasValue()){const l=o.tooltipPosition();e.add(l.x),a+=l.y,++s}}return 0!==s&&0!==e.size&&{x:[...e].reduce((o,l)=>o+l)/e.size,y:a/s}},nearest(i,n){if(!i.length)return!1;let s,r,o,t=n.x,e=n.y,a=Number.POSITIVE_INFINITY;for(s=0,r=i.length;s<r;++s){const l=i[s].element;if(l&&l.hasValue()){const h=Bi(n,l.getCenterPoint());h<a&&(a=h,o=l)}}if(o){const l=o.tooltipPosition();t=l.x,e=l.y}return{x:t,y:e}}};function _t(i,n){return n&&(j(n)?Array.prototype.push.apply(i,n):i.push(n)),i}function St(i){return("string"==typeof i||i instanceof String)&&i.indexOf("\n")>-1?i.split("\n"):i}function wh(i,n){const{element:t,datasetIndex:e,index:a}=n,s=i.getDatasetMeta(e).controller,{label:r,value:o}=s.getLabelAndValue(a);return{chart:i,label:r,parsed:s.getParsed(a),raw:i.data.datasets[e].data[a],formattedValue:o,dataset:s.getDataset(),dataIndex:a,datasetIndex:e,element:t}}function Us(i,n){const t=i.chart.ctx,{body:e,footer:a,title:s}=i,{boxWidth:r,boxHeight:o}=n,l=q(n.bodyFont),d=q(n.titleFont),h=q(n.footerFont),u=s.length,f=a.length,p=e.length,g=J(n.padding);let m=g.height,_=0,v=e.reduce((D,y)=>D+y.before.length+y.lines.length+y.after.length,0);v+=i.beforeBody.length+i.afterBody.length,u&&(m+=u*d.lineHeight+(u-1)*n.titleSpacing+n.titleMarginBottom),v&&(m+=p*(n.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(v-p)*l.lineHeight+(v-1)*n.bodySpacing),f&&(m+=n.footerMarginTop+f*h.lineHeight+(f-1)*n.footerSpacing);let b=0;const x=function(D){_=Math.max(_,t.measureText(D).width+b)};return t.save(),t.font=d.string,L(i.title,x),t.font=l.string,L(i.beforeBody.concat(i.afterBody),x),b=n.displayColors?r+2+n.boxPadding:0,L(e,D=>{L(D.before,x),L(D.lines,x),L(D.after,x)}),b=0,t.font=h.string,L(i.footer,x),t.restore(),_+=g.width,{width:_,height:m}}function Rh(i,n,t,e){const{x:a,width:s}=t,{width:r,chartArea:{left:o,right:l}}=i;let d="center";return"center"===e?d=a<=(o+l)/2?"left":"right":a<=s/2?d="left":a>=r-s/2&&(d="right"),function Ah(i,n,t,e){const{x:a,width:s}=e,r=t.caretSize+t.caretPadding;if("left"===i&&a+s+r>n.width||"right"===i&&a-s-r<0)return!0}(d,i,n,t)&&(d="center"),d}function $s(i,n,t){const e=t.yAlign||n.yAlign||function Sh(i,n){const{y:t,height:e}=n;return t<e/2?"top":t>i.height-e/2?"bottom":"center"}(i,t);return{xAlign:t.xAlign||n.xAlign||Rh(i,n,t,e),yAlign:e}}function Gs(i,n,t,e){const{caretSize:a,caretPadding:s,cornerRadius:r}=i,{xAlign:o,yAlign:l}=t,d=a+s,{topLeft:h,topRight:u,bottomLeft:f,bottomRight:p}=Yt(r);let g=function Eh(i,n){let{x:t,width:e}=i;return"right"===n?t-=e:"center"===n&&(t-=e/2),t}(n,o);const m=function Th(i,n,t){let{y:e,height:a}=i;return"top"===n?e+=t:e-="bottom"===n?a+t:a/2,e}(n,l,d);return"center"===l?"left"===o?g+=d:"right"===o&&(g-=d):"left"===o?g-=Math.max(h,f)+a:"right"===o&&(g+=Math.max(u,p)+a),{x:Q(g,0,e.width-n.width),y:Q(m,0,e.height-n.height)}}function mi(i,n,t){const e=J(t.padding);return"center"===n?i.x+i.width/2:"right"===n?i.x+i.width-e.right:i.x+e.left}function Xs(i){return _t([],St(i))}function Ks(i,n){const t=n&&n.dataset&&n.dataset.tooltip&&n.dataset.tooltip.callbacks;return t?i.override(t):i}const qs={beforeTitle:xt,title(i){if(i.length>0){const n=i[0],t=n.chart.data.labels,e=t?t.length:0;if(this&&this.options&&"dataset"===this.options.mode)return n.dataset.label||"";if(n.label)return n.label;if(e>0&&n.dataIndex<e)return t[n.dataIndex]}return""},afterTitle:xt,beforeBody:xt,beforeLabel:xt,label(i){if(this&&this.options&&"dataset"===this.options.mode)return i.label+": "+i.formattedValue||i.formattedValue;let n=i.dataset.label||"";n&&(n+=": ");const t=i.formattedValue;return R(t)||(n+=t),n},labelColor(i){const t=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const t=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:xt,afterBody:xt,beforeFooter:xt,footer:xt,afterFooter:xt};function nt(i,n,t,e){const a=i[n].call(t,e);return typeof a>"u"?qs[n].call(t,e):a}let Qs=(()=>class i extends wt{static positioners=Te;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,a=this.options.setContext(this.getContext()),s=a.enabled&&e.options.animation&&a.animations,r=new Vn(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=function Ih(i,n,t){return Pt(i,{tooltip:n,tooltipItems:t,type:"tooltip"})}(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:a}=e,s=nt(a,"beforeTitle",this,t),r=nt(a,"title",this,t),o=nt(a,"afterTitle",this,t);let l=[];return l=_t(l,St(s)),l=_t(l,St(r)),l=_t(l,St(o)),l}getBeforeBody(t,e){return Xs(nt(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:a}=e,s=[];return L(t,r=>{const o={before:[],lines:[],after:[]},l=Ks(a,r);_t(o.before,St(nt(l,"beforeLabel",this,r))),_t(o.lines,nt(l,"label",this,r)),_t(o.after,St(nt(l,"afterLabel",this,r))),s.push(o)}),s}getAfterBody(t,e){return Xs(nt(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:a}=e,s=nt(a,"beforeFooter",this,t),r=nt(a,"footer",this,t),o=nt(a,"afterFooter",this,t);let l=[];return l=_t(l,St(s)),l=_t(l,St(r)),l=_t(l,St(o)),l}_createItems(t){const e=this._active,a=this.chart.data,s=[],r=[],o=[];let d,h,l=[];for(d=0,h=e.length;d<h;++d)l.push(wh(this.chart,e[d]));return t.filter&&(l=l.filter((u,f,p)=>t.filter(u,f,p,a))),t.itemSort&&(l=l.sort((u,f)=>t.itemSort(u,f,a))),L(l,u=>{const f=Ks(t.callbacks,u);s.push(nt(f,"labelColor",this,u)),r.push(nt(f,"labelPointStyle",this,u)),o.push(nt(f,"labelTextColor",this,u))}),this.labelColors=s,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){const a=this.options.setContext(this.getContext()),s=this._active;let r,o=[];if(s.length){const l=Te[a.position].call(this,s,this._eventPosition);o=this._createItems(a),this.title=this.getTitle(o,a),this.beforeBody=this.getBeforeBody(o,a),this.body=this.getBody(o,a),this.afterBody=this.getAfterBody(o,a),this.footer=this.getFooter(o,a);const d=this._size=Us(this,a),h=Object.assign({},l,d),u=$s(this.chart,a,h),f=Gs(a,h,u,this.chart);this.xAlign=u.xAlign,this.yAlign=u.yAlign,r={opacity:1,x:f.x,y:f.y,width:d.width,height:d.height,caretX:l.x,caretY:l.y}}else 0!==this.opacity&&(r={opacity:0});this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),t&&a.external&&a.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,a,s){const r=this.getCaretPosition(t,a,s);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,a){const{xAlign:s,yAlign:r}=this,{caretSize:o,cornerRadius:l}=a,{topLeft:d,topRight:h,bottomLeft:u,bottomRight:f}=Yt(l),{x:p,y:g}=t,{width:m,height:_}=e;let v,b,x,D,y,k;return"center"===r?(y=g+_/2,"left"===s?(v=p,b=v-o,D=y+o,k=y-o):(v=p+m,b=v+o,D=y-o,k=y+o),x=v):(b="left"===s?p+Math.max(d,u)+o:"right"===s?p+m-Math.max(h,f)-o:this.caretX,"top"===r?(D=g,y=D-o,v=b-o,x=b+o):(D=g+_,y=D+o,v=b+o,x=b-o),k=D),{x1:v,x2:b,x3:x,y1:D,y2:y,y3:k}}drawTitle(t,e,a){const s=this.title,r=s.length;let o,l,d;if(r){const h=re(a.rtl,this.x,this.width);for(t.x=mi(this,a.titleAlign,a),e.textAlign=h.textAlign(a.titleAlign),e.textBaseline="middle",o=q(a.titleFont),l=a.titleSpacing,e.fillStyle=a.titleColor,e.font=o.string,d=0;d<r;++d)e.fillText(s[d],h.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+l,d+1===r&&(t.y+=a.titleMarginBottom-l)}}_drawColorBox(t,e,a,s,r){const o=this.labelColors[a],l=this.labelPointStyles[a],{boxHeight:d,boxWidth:h}=r,u=q(r.bodyFont),f=mi(this,"left",r),p=s.x(f),m=e.y+(d<u.lineHeight?(u.lineHeight-d)/2:0);if(r.usePointStyle){const _={radius:Math.min(h,d)/2,pointStyle:l.pointStyle,rotation:l.rotation,borderWidth:1},v=s.leftForLtr(p,h)+h/2,b=m+d/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,Ui(t,_,v,b),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,Ui(t,_,v,b)}else{t.lineWidth=P(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;const _=s.leftForLtr(p,h),v=s.leftForLtr(s.xPlus(p,1),h-2),b=Yt(o.borderRadius);Object.values(b).some(x=>0!==x)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,De(t,{x:_,y:m,w:h,h:d,radius:b}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),De(t,{x:v,y:m+1,w:h-2,h:d-2,radius:b}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(_,m,h,d),t.strokeRect(_,m,h,d),t.fillStyle=o.backgroundColor,t.fillRect(v,m+1,h-2,d-2))}t.fillStyle=this.labelTextColors[a]}drawBody(t,e,a){const{body:s}=this,{bodySpacing:r,bodyAlign:o,displayColors:l,boxHeight:d,boxWidth:h,boxPadding:u}=a,f=q(a.bodyFont);let p=f.lineHeight,g=0;const m=re(a.rtl,this.x,this.width),_=function(w){e.fillText(w,m.x(t.x+g),t.y+p/2),t.y+=p+r},v=m.textAlign(o);let b,x,D,y,k,C,M;for(e.textAlign=o,e.textBaseline="middle",e.font=f.string,t.x=mi(this,v,a),e.fillStyle=a.bodyColor,L(this.beforeBody,_),g=l&&"right"!==v?"center"===o?h/2+u:h+2+u:0,y=0,C=s.length;y<C;++y){for(b=s[y],x=this.labelTextColors[y],e.fillStyle=x,L(b.before,_),D=b.lines,l&&D.length&&(this._drawColorBox(e,t,y,m,a),p=Math.max(f.lineHeight,d)),k=0,M=D.length;k<M;++k)_(D[k]),p=f.lineHeight;L(b.after,_)}g=0,p=f.lineHeight,L(this.afterBody,_),t.y-=r}drawFooter(t,e,a){const s=this.footer,r=s.length;let o,l;if(r){const d=re(a.rtl,this.x,this.width);for(t.x=mi(this,a.footerAlign,a),t.y+=a.footerMarginTop,e.textAlign=d.textAlign(a.footerAlign),e.textBaseline="middle",o=q(a.footerFont),e.fillStyle=a.footerColor,e.font=o.string,l=0;l<r;++l)e.fillText(s[l],d.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+a.footerSpacing}}drawBackground(t,e,a,s){const{xAlign:r,yAlign:o}=this,{x:l,y:d}=t,{width:h,height:u}=a,{topLeft:f,topRight:p,bottomLeft:g,bottomRight:m}=Yt(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(l+f,d),"top"===o&&this.drawCaret(t,e,a,s),e.lineTo(l+h-p,d),e.quadraticCurveTo(l+h,d,l+h,d+p),"center"===o&&"right"===r&&this.drawCaret(t,e,a,s),e.lineTo(l+h,d+u-m),e.quadraticCurveTo(l+h,d+u,l+h-m,d+u),"bottom"===o&&this.drawCaret(t,e,a,s),e.lineTo(l+g,d+u),e.quadraticCurveTo(l,d+u,l,d+u-g),"center"===o&&"left"===r&&this.drawCaret(t,e,a,s),e.lineTo(l,d+f),e.quadraticCurveTo(l,d,l+f,d),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,a=this.$animations,s=a&&a.x,r=a&&a.y;if(s||r){const o=Te[t.position].call(this,this._active,this._eventPosition);if(!o)return;const l=this._size=Us(this,t),d=Object.assign({},o,this._size),h=$s(e,t,d),u=Gs(t,d,h,e);(s._to!==u.x||r._to!==u.y)&&(this.xAlign=h.xAlign,this.yAlign=h.yAlign,this.width=l.width,this.height=l.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,u))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let a=this.opacity;if(!a)return;this._updateAnimationTarget(e);const s={width:this.width,height:this.height},r={x:this.x,y:this.y};a=Math.abs(a)<.001?0:a;const o=J(e.padding);e.enabled&&(this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length)&&(t.save(),t.globalAlpha=a,this.drawBackground(r,t,s,e),wn(t,e.textDirection),r.y+=o.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),Sn(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const a=this._active,s=t.map(({datasetIndex:l,index:d})=>{const h=this.chart.getDatasetMeta(l);if(!h)throw new Error("Cannot find a dataset at index "+l);return{datasetIndex:l,element:h.data[d],index:d}}),r=!Ge(a,s),o=this._positionChanged(s,e);(r||o)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,a=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,r=this._active||[],o=this._getActiveElements(t,r,e,a),l=this._positionChanged(o,t),d=e||!Ge(o,r)||l;return d&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),d}_getActiveElements(t,e,a,s){const r=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(l=>this.chart.data.datasets[l.datasetIndex]&&void 0!==this.chart.getDatasetMeta(l.datasetIndex).controller.getParsed(l.index));const o=this.chart.getElementsAtEventForMode(t,r.mode,r,a);return r.reverse&&o.reverse(),o}_positionChanged(t,e){const{caretX:a,caretY:s,options:r}=this,o=Te[r.position].call(this,t,e);return!1!==o&&(a!==o.x||s!==o.y)}})();var Fh=Object.freeze({__proto__:null,Colors:$d,Decimation:qd,Filler:mh,Legend:Dh,SubTitle:Ch,Title:Mh,Tooltip:{id:"tooltip",_element:Qs,positioners:Te,afterInit(i,n,t){t&&(i.tooltip=new Qs({chart:i,options:t}))},beforeUpdate(i,n,t){i.tooltip&&i.tooltip.initialize(t)},reset(i,n,t){i.tooltip&&i.tooltip.initialize(t)},afterDraw(i){const n=i.tooltip;if(n&&n._willRender()){const t={tooltip:n};if(!1===i.notifyPlugins("beforeTooltipDraw",{...t,cancelable:!0}))return;n.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",t)}},afterEvent(i,n){i.tooltip&&i.tooltip.handleEvent(n.event,n.replay,n.inChartArea)&&(n.changed=!0)},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,n)=>n.bodyFont.size,boxWidth:(i,n)=>n.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:qs},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>"filter"!==i&&"itemSort"!==i&&"external"!==i,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]}});function Js(i){const n=this.getLabels();return i>=0&&i<n.length?n[i]:i}let Bh=(()=>class i extends Xt{static id="category";static defaults={ticks:{callback:Js}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const a=this.getLabels();for(const{index:s,label:r}of e)a[s]===r&&a.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(R(t))return null;const a=this.getLabels();return((i,n)=>null===i?null:Q(Math.round(i),0,n))(e=isFinite(e)&&a[e]===t?e:function Vh(i,n,t,e){const a=i.indexOf(n);return-1===a?((i,n,t,e)=>("string"==typeof n?(t=i.push(n)-1,e.unshift({index:t,label:n})):isNaN(n)&&(t=null),t))(i,n,t,e):a!==i.lastIndexOf(n)?t:a}(a,t,A(e,t),this._addedLabels),a.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:a,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(a=0),e||(s=this.getLabels().length-1)),this.min=a,this.max=s}buildTicks(){const t=this.min,e=this.max,a=this.options.offset,s=[];let r=this.getLabels();r=0===t&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-(a?0:1),1),this._startValue=this.min-(a?.5:0);for(let o=t;o<=e;o++)s.push({value:o});return s}getLabelForValue(t){return Js.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}})();function Zs(i,n,{horizontal:t,minRotation:e}){const a=ut(e),s=(t?Math.sin(a):Math.cos(a))||.001;return Math.min(n/s,.75*n*(""+i).length)}class _i extends Xt{constructor(n){super(n),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(n,t){return R(n)||("number"==typeof n||n instanceof Number)&&!isFinite(+n)?null:+n}handleTickRangeOptions(){const{beginAtZero:n}=this.options,{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:a,max:s}=this;const r=l=>a=t?a:l,o=l=>s=e?s:l;if(n){const l=gt(a),d=gt(s);l<0&&d<0?o(0):l>0&&d>0&&r(0)}if(a===s){let l=0===s?1:Math.abs(.05*s);o(s+l),n||r(a-l)}this.min=a,this.max=s}getTickLimit(){const n=this.options.ticks;let a,{maxTicksLimit:t,stepSize:e}=n;return e?(a=Math.ceil(this.max/e)-Math.floor(this.min/e)+1,a>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${e} would result generating up to ${a} ticks. Limiting to 1000.`),a=1e3)):(a=this.computeTickLimit(),t=t||11),t&&(a=Math.min(t,a)),a}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const n=this.options,t=n.ticks;let e=this.getTickLimit();e=Math.max(2,e);const r=function zh(i,n){const t=[],{bounds:a,step:s,min:r,max:o,precision:l,count:d,maxTicks:h,maxDigits:u,includeBounds:f}=i,p=s||1,g=h-1,{min:m,max:_}=n,v=!R(r),b=!R(o),x=!R(d),D=(_-m)/(u+1);let k,C,M,w,y=qa((_-m)/g/p)*p;if(y<1e-14&&!v&&!b)return[{value:m},{value:_}];w=Math.ceil(_/y)-Math.floor(m/y),w>g&&(y=qa(w*y/g/p)*p),R(l)||(k=Math.pow(10,l),y=Math.ceil(y*k)/k),"ticks"===a?(C=Math.floor(m/y)*y,M=Math.ceil(_/y)*y):(C=m,M=_),v&&b&&s&&function Ao(i,n){const t=Math.round(i);return t-n<=i&&t+n>=i}((o-r)/s,y/1e3)?(w=Math.round(Math.min((o-r)/y,h)),y=(o-r)/w,C=r,M=o):x?(C=v?r:C,M=b?o:M,w=d-1,y=(M-C)/w):(w=(M-C)/y,w=_e(w,Math.round(w),y/1e3)?Math.round(w):Math.ceil(w));const I=Math.max(Ja(y),Ja(C));k=Math.pow(10,R(l)?I:l),C=Math.round(C*k)/k,M=Math.round(M*k)/k;let T=0;for(v&&(f&&C!==r?(t.push({value:r}),C<r&&T++,_e(Math.round((C+T*y)*k)/k,r,Zs(r,D,i))&&T++):C<r&&T++);T<w;++T){const F=Math.round((C+T*y)*k)/k;if(b&&F>o)break;t.push({value:F})}return b&&f&&M!==o?t.length&&_e(t[t.length-1].value,o,Zs(o,D,i))?t[t.length-1].value=o:t.push({value:o}):(!b||M===o)&&t.push({value:M}),t}({maxTicks:e,bounds:n.bounds,min:n.min,max:n.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:!1!==t.includeBounds},this._range||this);return"ticks"===n.bounds&&Qa(r,this,"value"),n.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const n=this.ticks;let t=this.min,e=this.max;if(super.configure(),this.options.offset&&n.length){const a=(e-t)/Math.max(n.length-1,1)/2;t-=a,e+=a}this._startValue=t,this._endValue=e,this._valueRange=e-t}getLabelForValue(n){return ye(n,this.chart.options.locale,this.options.ticks.format)}}const Ie=i=>Math.floor(It(i)),Kt=(i,n)=>Math.pow(10,Ie(i)+n);function tr(i){return i/Math.pow(10,Ie(i))==1}function er(i,n,t){const e=Math.pow(10,t),a=Math.floor(i/e);return Math.ceil(n/e)-a}function ba(i){const n=i.ticks;if(n.display&&i.display){const t=J(n.backdropPadding);return A(n.font&&n.font.size,W.font.size)+t.height}return 0}function Wh(i,n,t){return t=j(t)?t:[t],{w:Uo(i,n.string,t),h:t.length*n.lineHeight}}function ir(i,n,t,e,a){return i===e||i===a?{start:n-t/2,end:n+t/2}:i<e||i>a?{start:n-t,end:n}:{start:n,end:n+t}}function $h(i,n,t,e,a){const s=Math.abs(Math.sin(t)),r=Math.abs(Math.cos(t));let o=0,l=0;e.start<n.l?(o=(n.l-e.start)/s,i.l=Math.min(i.l,n.l-o)):e.end>n.r&&(o=(e.end-n.r)/s,i.r=Math.max(i.r,n.r+o)),a.start<n.t?(l=(n.t-a.start)/r,i.t=Math.min(i.t,n.t-l)):a.end>n.b&&(l=(a.end-n.b)/r,i.b=Math.max(i.b,n.b+l))}function Gh(i,n,t){const e=i.drawingArea,{extra:a,additionalAngle:s,padding:r,size:o}=t,l=i.getPointPosition(n,e+a+r,s),d=Math.round(Li(lt(l.angle+X))),h=function Jh(i,n,t){return 90===t||270===t?i-=n/2:(t>270||t<90)&&(i-=n),i}(l.y,o.h,d),u=function qh(i){return 0===i||180===i?"center":i<180?"left":"right"}(d),f=function Qh(i,n,t){return"right"===t?i-=n:"center"===t&&(i-=n/2),i}(l.x,o.w,u);return{visible:!0,x:l.x,y:h,textAlign:u,left:f,top:h,right:f+o.w,bottom:h+o.h}}function Xh(i,n){if(!n)return!0;const{left:t,top:e,right:a,bottom:s}=i;return!(Mt({x:t,y:e},n)||Mt({x:t,y:s},n)||Mt({x:a,y:e},n)||Mt({x:a,y:s},n))}function Zh(i,n,t){const{left:e,top:a,right:s,bottom:r}=t,{backdropColor:o}=n;if(!R(o)){const l=Yt(n.borderRadius),d=J(n.backdropPadding);i.fillStyle=o;const h=e-d.left,u=a-d.top,f=s-e+d.width,p=r-a+d.height;Object.values(l).some(g=>0!==g)?(i.beginPath(),De(i,{x:h,y:u,w:f,h:p,radius:l}),i.fill()):i.fillRect(h,u,f,p)}}function ar(i,n,t,e){const{ctx:a}=i;if(t)a.arc(i.xCenter,i.yCenter,n,0,Y);else{let s=i.getPointPosition(0,n);a.moveTo(s.x,s.y);for(let r=1;r<e;r++)s=i.getPointPosition(r,n),a.lineTo(s.x,s.y)}}const bi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},st=Object.keys(bi);function nr(i,n){return i-n}function sr(i,n){if(R(n))return null;const t=i._adapter,{parser:e,round:a,isoWeekday:s}=i._parseOpts;let r=n;return"function"==typeof e&&(r=e(r)),G(r)||(r="string"==typeof e?t.parse(r,e):t.parse(r)),null===r?null:(a&&(r="week"!==a||!ae(s)&&!0!==s?t.startOf(r,a):t.startOf(r,"isoWeek",s)),+r)}function rr(i,n,t,e){const a=st.length;for(let s=st.indexOf(i);s<a-1;++s){const r=bi[st[s]];if(r.common&&Math.ceil((t-n)/((r.steps?r.steps:Number.MAX_SAFE_INTEGER)*r.size))<=e)return st[s]}return st[a-1]}function or(i,n,t){if(t){if(t.length){const{lo:e,hi:a}=zi(t,n);i[t[e]>=n?t[e]:t[a]]=!0}}else i[n]=!0}function lr(i,n,t){const e=[],a={},s=n.length;let r,o;for(r=0;r<s;++r)o=n[r],a[o]=r,e.push({value:o,major:!1});return 0!==s&&t?function ru(i,n,t,e){const a=i._adapter,s=+a.startOf(n[0].value,e),r=n[n.length-1].value;let o,l;for(o=s;o<=r;o=+a.add(o,1,e))l=t[o],l>=0&&(n[l].major=!0);return n}(i,e,a,t):e}let va=(()=>class i extends Xt{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const a=t.time||(t.time={}),s=this._adapter=new hc__date(t.adapters.date);s.init(e),ge(a.displayFormats,s.formats()),this._parseOpts={parser:a.parser,round:a.round,isoWeekday:a.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:sr(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,a=t.time.unit||"day";let{min:s,max:r,minDefined:o,maxDefined:l}=this.getUserBounds();function d(h){!o&&!isNaN(h.min)&&(s=Math.min(s,h.min)),!l&&!isNaN(h.max)&&(r=Math.max(r,h.max))}(!o||!l)&&(d(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&d(this.getMinMax(!1))),s=G(s)&&!isNaN(s)?s:+e.startOf(Date.now(),a),r=G(r)&&!isNaN(r)?r:+e.endOf(Date.now(),a)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],a=t[t.length-1]),{min:e,max:a}}buildTicks(){const t=this.options,e=t.time,a=t.ticks,s="labels"===a.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const r=this.min,l=function Io(i,n,t){let e=0,a=i.length;for(;e<a&&i[e]<n;)e++;for(;a>e&&i[a-1]>t;)a--;return e>0||a<i.length?i.slice(e,a):i}(s,r,this.max);return this._unit=e.unit||(a.autoSkip?rr(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):function nu(i,n,t,e,a){for(let s=st.length-1;s>=st.indexOf(t);s--){const r=st[s];if(bi[r].common&&i._adapter.diff(a,e,r)>=n-1)return r}return st[t?st.indexOf(t):0]}(this,l.length,e.minUnit,this.min,this.max)),this._majorUnit=a.major.enabled&&"year"!==this._unit?function su(i){for(let n=st.indexOf(i)+1,t=st.length;n<t;++n)if(bi[st[n]].common)return st[n]}(this._unit):void 0,this.initOffsets(s),t.reverse&&l.reverse(),lr(this,l,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let s,r,e=0,a=0;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),e=1===t.length?1-s:(this.getDecimalForValue(t[1])-s)/2,r=this.getDecimalForValue(t[t.length-1]),a=1===t.length?r:(r-this.getDecimalForValue(t[t.length-2]))/2);const o=t.length<3?.5:.25;e=Q(e,0,o),a=Q(a,0,o),this._offsets={start:e,end:a,factor:1/(e+1+a)}}_generate(){const t=this._adapter,e=this.min,a=this.max,s=this.options,r=s.time,o=r.unit||rr(r.minUnit,e,a,this._getLabelCapacity(e)),l=A(s.ticks.stepSize,1),d="week"===o&&r.isoWeekday,h=ae(d)||!0===d,u={};let p,g,f=e;if(h&&(f=+t.startOf(f,"isoWeek",d)),f=+t.startOf(f,h?"day":o),t.diff(a,e,o)>1e5*l)throw new Error(e+" and "+a+" are too far apart with stepSize of "+l+" "+o);const m="data"===s.ticks.source&&this.getDataTimestamps();for(p=f,g=0;p<a;p=+t.add(p,l,o),g++)or(u,p,m);return(p===a||"ticks"===s.bounds||1===g)&&or(u,p,m),Object.keys(u).sort(nr).map(_=>+_)}getLabelForValue(t){const a=this.options.time;return this._adapter.format(t,a.tooltipFormat?a.tooltipFormat:a.displayFormats.datetime)}format(t,e){return this._adapter.format(t,e||this.options.time.displayFormats[this._unit])}_tickFormatFunction(t,e,a,s){const r=this.options,o=r.ticks.callback;if(o)return B(o,[t,e,a],this);const l=r.time.displayFormats,d=this._unit,h=this._majorUnit,f=h&&l[h],p=a[e];return this._adapter.format(t,s||(h&&f&&p&&p.major?f:d&&l[d]))}generateTickLabels(t){let e,a,s;for(e=0,a=t.length;e<a;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,a=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+a)*e.factor)}getValueForPixel(t){const e=this._offsets,a=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+a*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,a=this.ctx.measureText(t).width,s=ut(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(s),o=Math.sin(s),l=this._resolveTickFontOptions(0).size;return{w:a*r+l*o,h:a*o+l*r}}_getLabelCapacity(t){const e=this.options.time,a=e.displayFormats,s=a[e.unit]||a.millisecond,r=this._tickFormatFunction(t,0,lr(this,[t],this._majorUnit),s),o=this._getLabelSize(r),l=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return l>0?l:1}getDataTimestamps(){let e,a,t=this._cache.data||[];if(t.length)return t;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(e=0,a=s.length;e<a;++e)t=t.concat(s[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,a;if(t.length)return t;const s=this.getLabels();for(e=0,a=s.length;e<a;++e)t.push(sr(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return an(t.sort(nr))}})();function vi(i,n,t){let s,r,o,l,e=0,a=i.length-1;t?(n>=i[e].pos&&n<=i[a].pos&&({lo:e,hi:a}=kt(i,"pos",n)),({pos:s,time:o}=i[e]),({pos:r,time:l}=i[a])):(n>=i[e].time&&n<=i[a].time&&({lo:e,hi:a}=kt(i,"time",n)),({time:s,pos:o}=i[e]),({time:r,pos:l}=i[a]));const d=r-s;return d?o+(l-o)*(n-s)/d:o}ca.register(dc,zd,Fh,Object.freeze({__proto__:null,CategoryScale:Bh,LinearScale:class Nh extends _i{static id="linear";static defaults={ticks:{callback:Qe.formatters.numeric}};determineDataLimits(){const{min:n,max:t}=this.getMinMax(!0);this.min=G(n)?n:0,this.max=G(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){const n=this.isHorizontal(),t=n?this.width:this.height,e=ut(this.options.ticks.minRotation),a=(n?Math.sin(e):Math.cos(e))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,s.lineHeight/a))}getPixelForValue(n){return null===n?NaN:this.getPixelForDecimal((n-this._startValue)/this._valueRange)}getValueForPixel(n){return this._startValue+this.getDecimalForPixel(n)*this._valueRange}},LogarithmicScale:class Yh extends Xt{static id="logarithmic";static defaults={ticks:{callback:Qe.formatters.logarithmic,major:{enabled:!0}}};constructor(n){super(n),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(n,t){const e=_i.prototype.parse.apply(this,[n,t]);if(0!==e)return G(e)&&e>0?e:null;this._zero=!0}determineDataLimits(){const{min:n,max:t}=this.getMinMax(!0);this.min=G(n)?Math.max(0,n):null,this.max=G(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!G(this._userMin)&&(this.min=n===Kt(this.min,0)?Kt(this.min,-1):Kt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:n,maxDefined:t}=this.getUserBounds();let e=this.min,a=this.max;const s=o=>e=n?e:o,r=o=>a=t?a:o;e===a&&(e<=0?(s(1),r(10)):(s(Kt(e,-1)),r(Kt(a,1)))),e<=0&&s(Kt(a,-1)),a<=0&&r(Kt(e,1)),this.min=e,this.max=a}buildTicks(){const n=this.options,e=function Hh(i,{min:n,max:t}){n=ot(i.min,n);const e=[],a=Ie(n);let s=function jh(i,n){let e=Ie(n-i);for(;er(i,n,e)>10;)e++;for(;er(i,n,e)<10;)e--;return Math.min(e,Ie(i))}(n,t),r=s<0?Math.pow(10,Math.abs(s)):1;const o=Math.pow(10,s),l=a>s?Math.pow(10,a):0,d=Math.round((n-l)*r)/r,h=Math.floor((n-l)/o/10)*o*10;let u=Math.floor((d-h)/Math.pow(10,s)),f=ot(i.min,Math.round((l+h+u*Math.pow(10,s))*r)/r);for(;f<t;)e.push({value:f,major:tr(f),significand:u}),u>=10?u=u<15?15:20:u++,u>=20&&(s++,u=2,r=s>=0?1:r),f=Math.round((l+h+u*Math.pow(10,s))*r)/r;const p=ot(i.max,f);return e.push({value:p,major:tr(p),significand:u}),e}({min:this._userMin,max:this._userMax},this);return"ticks"===n.bounds&&Qa(e,this,"value"),n.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(n){return void 0===n?"0":ye(n,this.chart.options.locale,this.options.ticks.format)}configure(){const n=this.min;super.configure(),this._startValue=It(n),this._valueRange=It(this.max)-It(n)}getPixelForValue(n){return(void 0===n||0===n)&&(n=this.min),null===n||isNaN(n)?NaN:this.getPixelForDecimal(n===this.min?0:(It(n)-this._startValue)/this._valueRange)}getValueForPixel(n){const t=this.getDecimalForPixel(n);return Math.pow(10,this._startValue+t*this._valueRange)}},RadialLinearScale:class au extends _i{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Qe.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:n=>n,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(n){super(n),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const n=this._padding=J(ba(this.options)/2),t=this.width=this.maxWidth-n.width,e=this.height=this.maxHeight-n.height;this.xCenter=Math.floor(this.left+t/2+n.left),this.yCenter=Math.floor(this.top+e/2+n.top),this.drawingArea=Math.floor(Math.min(t,e)/2)}determineDataLimits(){const{min:n,max:t}=this.getMinMax(!1);this.min=G(n)&&!isNaN(n)?n:0,this.max=G(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/ba(this.options))}generateTickLabels(n){_i.prototype.generateTickLabels.call(this,n),this._pointLabels=this.getLabels().map((t,e)=>{const a=B(this.options.pointLabels.callback,[t,e],this);return a||0===a?a:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){const n=this.options;n.display&&n.pointLabels.display?function Uh(i){const n={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},t=Object.assign({},n),e=[],a=[],s=i._pointLabels.length,r=i.options.pointLabels,o=r.centerPointLabels?H/s:0;for(let l=0;l<s;l++){const d=r.setContext(i.getPointLabelContext(l));a[l]=d.padding;const h=i.getPointPosition(l,i.drawingArea+a[l],o),u=q(d.font),f=Wh(i.ctx,u,i._pointLabels[l]);e[l]=f;const p=lt(i.getIndexAngle(l)+o),g=Math.round(Li(p));$h(t,n,p,ir(g,h.x,f.w,0,180),ir(g,h.y,f.h,90,270))}i.setCenterPoint(n.l-t.l,t.r-n.r,n.t-t.t,t.b-n.b),i._pointLabelItems=function Kh(i,n,t){const e=[],a=i._pointLabels.length,s=i.options,{centerPointLabels:r,display:o}=s.pointLabels,l={extra:ba(s)/2,additionalAngle:r?H/a:0};let d;for(let h=0;h<a;h++){l.padding=t[h],l.size=n[h];const u=Gh(i,h,l);e.push(u),"auto"===o&&(u.visible=Xh(u,d),u.visible&&(d=u))}return e}(i,e,a)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(n,t,e,a){this.xCenter+=Math.floor((n-t)/2),this.yCenter+=Math.floor((e-a)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(n,t,e,a))}getIndexAngle(n){return lt(n*(Y/(this._pointLabels.length||1))+ut(this.options.startAngle||0))}getDistanceFromCenterForValue(n){if(R(n))return NaN;const t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-n)*t:(n-this.min)*t}getValueForDistanceFromCenter(n){if(R(n))return NaN;const t=n/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(n){const t=this._pointLabels||[];if(n>=0&&n<t.length){const e=t[n];return function iu(i,n,t){return Pt(i,{label:t,index:n,type:"pointLabel"})}(this.getContext(),n,e)}}getPointPosition(n,t,e=0){const a=this.getIndexAngle(n)-X+e;return{x:Math.cos(a)*t+this.xCenter,y:Math.sin(a)*t+this.yCenter,angle:a}}getPointPositionForValue(n,t){return this.getPointPosition(n,this.getDistanceFromCenterForValue(t))}getBasePosition(n){return this.getPointPositionForValue(n||0,this.getBaseValue())}getPointLabelPosition(n){const{left:t,top:e,right:a,bottom:s}=this._pointLabelItems[n];return{left:t,top:e,right:a,bottom:s}}drawBackground(){const{backgroundColor:n,grid:{circular:t}}=this.options;if(n){const e=this.ctx;e.save(),e.beginPath(),ar(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),e.closePath(),e.fillStyle=n,e.fill(),e.restore()}}drawGrid(){const n=this.ctx,t=this.options,{angleLines:e,grid:a,border:s}=t,r=this._pointLabels.length;let o,l,d;if(t.pointLabels.display&&function tu(i,n){const{ctx:t,options:{pointLabels:e}}=i;for(let a=n-1;a>=0;a--){const s=i._pointLabelItems[a];if(!s.visible)continue;const r=e.setContext(i.getPointLabelContext(a));Zh(t,r,s);const o=q(r.font),{x:l,y:d,textAlign:h}=s;Ht(t,i._pointLabels[a],l,d+o.lineHeight/2,o,{color:r.color,textAlign:h,textBaseline:"middle"})}}(this,r),a.display&&this.ticks.forEach((h,u)=>{if(0!==u||0===u&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const f=this.getContext(u),p=a.setContext(f),g=s.setContext(f);!function eu(i,n,t,e,a){const s=i.ctx,r=n.circular,{color:o,lineWidth:l}=n;!r&&!e||!o||!l||t<0||(s.save(),s.strokeStyle=o,s.lineWidth=l,s.setLineDash(a.dash||[]),s.lineDashOffset=a.dashOffset,s.beginPath(),ar(i,t,r,e),s.closePath(),s.stroke(),s.restore())}(this,p,l,r,g)}}),e.display){for(n.save(),o=r-1;o>=0;o--){const h=e.setContext(this.getPointLabelContext(o)),{color:u,lineWidth:f}=h;!f||!u||(n.lineWidth=f,n.strokeStyle=u,n.setLineDash(h.borderDash),n.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),d=this.getPointPosition(o,l),n.beginPath(),n.moveTo(this.xCenter,this.yCenter),n.lineTo(d.x,d.y),n.stroke())}n.restore()}}drawBorder(){}drawLabels(){const n=this.ctx,t=this.options,e=t.ticks;if(!e.display)return;const a=this.getIndexAngle(0);let s,r;n.save(),n.translate(this.xCenter,this.yCenter),n.rotate(a),n.textAlign="center",n.textBaseline="middle",this.ticks.forEach((o,l)=>{if(0===l&&this.min>=0&&!t.reverse)return;const d=e.setContext(this.getContext(l)),h=q(d.font);if(s=this.getDistanceFromCenterForValue(this.ticks[l].value),d.showLabelBackdrop){n.font=h.string,r=n.measureText(o.label).width,n.fillStyle=d.backdropColor;const u=J(d.backdropPadding);n.fillRect(-r/2-u.left,-s-h.size/2-u.top,r+u.width,h.size+u.height)}Ht(n,o.label,0,-s,h,{color:d.color,strokeColor:d.textStrokeColor,strokeWidth:d.textStrokeWidth})}),n.restore()}drawTitle(){}},TimeScale:va,TimeSeriesScale:class ou extends va{static id="timeseries";static defaults=va.defaults;constructor(n){super(n),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const n=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(n);this._minPos=vi(t,this.min),this._tableRange=vi(t,this.max)-this._minPos,super.initOffsets(n)}buildLookupTable(n){const{min:t,max:e}=this,a=[],s=[];let r,o,l,d,h;for(r=0,o=n.length;r<o;++r)d=n[r],d>=t&&d<=e&&a.push(d);if(a.length<2)return[{time:t,pos:0},{time:e,pos:1}];for(r=0,o=a.length;r<o;++r)h=a[r+1],l=a[r-1],d=a[r],Math.round((h+l)/2)!==d&&s.push({time:d,pos:r/(o-1)});return s}_generate(){const n=this.min,t=this.max;let e=super.getDataTimestamps();return(!e.includes(n)||!e.length)&&e.splice(0,0,n),(!e.includes(t)||1===e.length)&&e.push(t),e.sort((a,s)=>a-s)}_getTimestampsForTable(){let n=this._cache.all||[];if(n.length)return n;const t=this.getDataTimestamps(),e=this.getLabelTimestamps();return n=t.length&&e.length?this.normalize(t.concat(e)):t.length?t:e,n=this._cache.all=n,n}getDecimalForValue(n){return(vi(this._table,n)-this._minPos)/this._tableRange}getValueForPixel(n){const t=this._offsets,e=this.getDecimalForPixel(n)/t.factor-t.end;return vi(this._table,e*this._tableRange+this._minPos,!0)}}}));var cr=function(){if(typeof window<"u"){if(window.devicePixelRatio)return window.devicePixelRatio;var i=window.screen;if(i)return(i.deviceXDPI||1)/(i.logicalXDPI||1)}return 1}(),Pe_textSize=function(i,n,t){var o,e=[].concat(n),a=e.length,s=i.font,r=0;for(i.font=t.string,o=0;o<a;++o)r=Math.max(i.measureText(e[o]).width,r);return i.font=s,{height:a*t.lineHeight,width:r}};function ya(i,n){var t=n.x,e=n.y;if(null===t)return{x:0,y:-1};if(null===e)return{x:1,y:0};var a=i.x-t,s=i.y-e,r=Math.sqrt(a*a+s*s);return{x:r?a/r:0,y:r?s/r:-1}}var hu=0,dr=1,hr=2,ur=4,fr=8;function yi(i,n,t){var e=hu;return i<t.left?e|=dr:i>t.right&&(e|=hr),n<t.top?e|=fr:n>t.bottom&&(e|=ur),e}function xi(i,n){var a,s,t=n.anchor,e=i;return n.clamp&&(e=function uu(i,n){for(var l,d,h,t=i.x0,e=i.y0,a=i.x1,s=i.y1,r=yi(t,e,n),o=yi(a,s,n);r|o&&!(r&o);)(l=r||o)&fr?(d=t+(a-t)*(n.top-e)/(s-e),h=n.top):l&ur?(d=t+(a-t)*(n.bottom-e)/(s-e),h=n.bottom):l&hr?(h=e+(s-e)*(n.right-t)/(a-t),d=n.right):l&dr&&(h=e+(s-e)*(n.left-t)/(a-t),d=n.left),l===r?r=yi(t=d,e=h,n):o=yi(a=d,s=h,n);return{x0:t,x1:a,y0:e,y1:s}}(e,n.area)),"start"===t?(a=e.x0,s=e.y0):"end"===t?(a=e.x1,s=e.y1):(a=(e.x0+e.x1)/2,s=(e.y0+e.y1)/2),function du(i,n,t,e,a){switch(a){case"center":t=e=0;break;case"bottom":t=0,e=1;break;case"right":t=1,e=0;break;case"left":t=-1,e=0;break;case"top":t=0,e=-1;break;case"start":t=-t,e=-e;break;case"end":break;default:a*=Math.PI/180,t=Math.cos(a),e=Math.sin(a)}return{x:i,y:n,vx:t,vy:e}}(a,s,i.vx,i.vy,n.align)}var Di={arc:function(i,n){var t=(i.startAngle+i.endAngle)/2,e=Math.cos(t),a=Math.sin(t),s=i.innerRadius,r=i.outerRadius;return xi({x0:i.x+e*s,y0:i.y+a*s,x1:i.x+e*r,y1:i.y+a*r,vx:e,vy:a},n)},point:function(i,n){var t=ya(i,n.origin),e=t.x*i.options.radius,a=t.y*i.options.radius;return xi({x0:i.x-e,y0:i.y-a,x1:i.x+e,y1:i.y+a,vx:t.x,vy:t.y},n)},bar:function(i,n){var t=ya(i,n.origin),e=i.x,a=i.y,s=0,r=0;return i.horizontal?(e=Math.min(i.x,i.base),s=Math.abs(i.base-i.x)):(a=Math.min(i.y,i.base),r=Math.abs(i.base-i.y)),xi({x0:e,y0:a+r,x1:e+s,y1:a,vx:t.x,vy:t.y},n)},fallback:function(i,n){var t=ya(i,n.origin);return xi({x0:i.x,y0:i.y,x1:i.x+(i.width||0),y1:i.y+(i.height||0),vx:t.x,vy:t.y},n)}},At=function(i){return Math.round(i*cr)/cr};function pu(i,n){var t=n.chart.getDatasetMeta(n.datasetIndex).vScale;if(!t)return null;if(void 0!==t.xCenter&&void 0!==t.yCenter)return{x:t.xCenter,y:t.yCenter};var e=t.getBasePixel();return i.horizontal?{x:e,y:null}:{x:null,y:e}}function gu(i){return i instanceof Ds?Di.arc:i instanceof ws?Di.point:i instanceof As?Di.bar:Di.fallback}function vu(i,n,t){var e=i.shadowBlur,a=t.stroked,s=At(t.x),r=At(t.y),o=At(t.w);a&&i.strokeText(n,s,r,o),t.filled&&(e&&a&&(i.shadowBlur=0),i.fillText(n,s,r,o),e&&a&&(i.shadowBlur=e))}var pr=function(i,n,t,e){var a=this;a._config=i,a._index=e,a._model=null,a._rects=null,a._ctx=n,a._el=t};pt(pr.prototype,{_modelize:function(i,n,t,e){var a=this,s=a._index,r=q(U([t.font,{}],e,s)),o=U([t.color,W.color],e,s);return{align:U([t.align,"center"],e,s),anchor:U([t.anchor,"center"],e,s),area:e.chart.chartArea,backgroundColor:U([t.backgroundColor,null],e,s),borderColor:U([t.borderColor,null],e,s),borderRadius:U([t.borderRadius,0],e,s),borderWidth:U([t.borderWidth,0],e,s),clamp:U([t.clamp,!1],e,s),clip:U([t.clip,!1],e,s),color:o,display:i,font:r,lines:n,offset:U([t.offset,4],e,s),opacity:U([t.opacity,1],e,s),origin:pu(a._el,e),padding:J(U([t.padding,4],e,s)),positioner:gu(a._el),rotation:U([t.rotation,0],e,s)*(Math.PI/180),size:Pe_textSize(a._ctx,n,r),textAlign:U([t.textAlign,"start"],e,s),textShadowBlur:U([t.textShadowBlur,0],e,s),textShadowColor:U([t.textShadowColor,o],e,s),textStrokeColor:U([t.textStrokeColor,o],e,s),textStrokeWidth:U([t.textStrokeWidth,0],e,s)}},update:function(i){var r,o,l,n=this,t=null,e=null,a=n._index,s=n._config,d=U([s.display,!0],i,a);d&&(l=R(o=A(B(s.formatter,[r=i.dataset.data[a],i]),r))?[]:function(i){var t,n=[];for(i=[].concat(i);i.length;)"string"==typeof(t=i.pop())?n.unshift.apply(n,t.split("\n")):Array.isArray(t)?i.push.apply(i,t):R(i)||n.unshift(""+t);return n}(o)).length&&(e=function fu(i){var n=i.borderWidth||0,t=i.padding,e=i.size.height,a=i.size.width,s=-a/2,r=-e/2;return{frame:{x:s-t.left-n,y:r-t.top-n,w:a+t.width+2*n,h:e+t.height+2*n},text:{x:s,y:r,w:a,h:e}}}(t=n._modelize(d,l,s,i))),n._model=t,n._rects=e},geometry:function(){return this._rects?this._rects.frame:{}},rotation:function(){return this._model?this._model.rotation:0},visible:function(){return this._model&&this._model.opacity},model:function(){return this._model},draw:function(i,n){var r,e=i.ctx,a=this._model,s=this._rects;this.visible()&&(e.save(),a.clip&&(r=a.area,e.beginPath(),e.rect(r.left,r.top,r.right-r.left,r.bottom-r.top),e.clip()),e.globalAlpha=function(i,n,t){return Math.max(i,Math.min(n,t))}(0,a.opacity,1),e.translate(At(n.x),At(n.y)),e.rotate(a.rotation),function _u(i,n,t){var e=t.backgroundColor,a=t.borderColor,s=t.borderWidth;!e&&(!a||!s)||(i.beginPath(),function mu(i,n,t,e,a,s){var r=Math.PI/2;if(s){var o=Math.min(s,a/2,e/2),l=n+o,d=t+o,h=n+e-o,u=t+a-o;i.moveTo(n,d),l<h&&d<u?(i.arc(l,d,o,-Math.PI,-r),i.arc(h,d,o,-r,0),i.arc(h,u,o,0,r),i.arc(l,u,o,r,Math.PI)):l<h?(i.moveTo(l,t),i.arc(h,d,o,-r,r),i.arc(l,d,o,r,Math.PI+r)):d<u?(i.arc(l,d,o,-Math.PI,0),i.arc(l,u,o,0,Math.PI)):i.arc(l,d,o,-Math.PI,Math.PI),i.closePath(),i.moveTo(n,t)}else i.rect(n,t,e,a)}(i,At(n.x)+s/2,At(n.y)+s/2,At(n.w)-s,At(n.h)-s,t.borderRadius),i.closePath(),e&&(i.fillStyle=e,i.fill()),a&&s&&(i.strokeStyle=a,i.lineWidth=s,i.lineJoin="miter",i.stroke()))}(e,s.frame,a),function yu(i,n,t,e){var f,a=e.textAlign,s=e.color,r=!!s,o=e.font,l=n.length,d=e.textStrokeColor,h=e.textStrokeWidth,u=d&&h;if(l&&(r||u))for(t=function bu(i,n,t){var e=t.lineHeight,a=i.w,s=i.x;return"center"===n?s+=a/2:("end"===n||"right"===n)&&(s+=a),{h:e,w:a,x:s,y:i.y+e/2}}(t,a,o),i.font=o.string,i.textAlign=a,i.textBaseline="middle",i.shadowBlur=e.textShadowBlur,i.shadowColor=e.textShadowColor,r&&(i.fillStyle=s),u&&(i.lineJoin="round",i.lineWidth=h,i.strokeStyle=d),f=0,l=n.length;f<l;++f)vu(i,n[f],{stroked:u,filled:r,w:t.w,x:t.x,y:t.y+t.h*f})}(e,a.lines,s.text,a),e.restore())}});var xu=Number.MIN_SAFE_INTEGER||-9007199254740991,Du=Number.MAX_SAFE_INTEGER||9007199254740991;function Fe(i,n,t){var e=Math.cos(t),a=Math.sin(t),s=n.x,r=n.y;return{x:s+e*(i.x-s)-a*(i.y-r),y:r+a*(i.x-s)+e*(i.y-r)}}function gr(i,n){var s,r,d,t=Du,e=xu,a=n.origin;for(s=0;s<i.length;++s)d=n.vx*((r=i[s]).x-a.x)+n.vy*(r.y-a.y),t=Math.min(t,d),e=Math.max(e,d);return{min:t,max:e}}function ki(i,n){var t=n.x-i.x,e=n.y-i.y,a=Math.sqrt(t*t+e*e);return{vx:(n.x-i.x)/a,vy:(n.y-i.y)/a,origin:i,ln:a}}var mr=function(){this._rotation=0,this._rect={x:0,y:0,w:0,h:0}};function _r(i,n,t){var e=n.positioner(i,n),a=e.vx,s=e.vy;if(!a&&!s)return{x:e.x,y:e.y};var r=t.w,o=t.h,l=n.rotation,d=Math.abs(r/2*Math.cos(l))+Math.abs(o/2*Math.sin(l)),h=Math.abs(r/2*Math.sin(l))+Math.abs(o/2*Math.cos(l)),u=1/Math.max(Math.abs(a),Math.abs(s));return d*=a*u,h*=s*u,{x:e.x+(d+=n.offset*a),y:e.y+(h+=n.offset*s)}}pt(mr.prototype,{center:function(){var i=this._rect;return{x:i.x+i.w/2,y:i.y+i.h/2}},update:function(i,n,t){this._rotation=t,this._rect={x:n.x+i.x,y:n.y+i.y,w:n.w,h:n.h}},contains:function(i){var n=this,e=n._rect;return!((i=Fe(i,n.center(),-n._rotation)).x<e.x-1||i.y<e.y-1||i.x>e.x+e.w+2||i.y>e.y+e.h+2)},intersects:function(i){var a,s,r,n=this._points(),t=i._points(),e=[ki(n[0],n[1]),ki(n[0],n[3])];for(this._rotation!==i._rotation&&e.push(ki(t[0],t[1]),ki(t[0],t[3])),a=0;a<e.length;++a)if(s=gr(n,e[a]),r=gr(t,e[a]),s.max<r.min||r.max<s.min)return!1;return!0},_points:function(){var i=this,n=i._rect,t=i._rotation,e=i.center();return[Fe({x:n.x,y:n.y},e,t),Fe({x:n.x+n.w,y:n.y},e,t),Fe({x:n.x+n.w,y:n.y+n.h},e,t),Fe({x:n.x,y:n.y+n.h},e,t)]}});var Oe={prepare:function(i){var t,e,a,s,r,n=[];for(t=0,a=i.length;t<a;++t)for(e=0,s=i[t].length;e<s;++e)n.push(r=i[t][e]),r.$layout={_box:new mr,_hidable:!1,_visible:!0,_set:t,_idx:r._index};return n.sort(function(o,l){var d=o.$layout,h=l.$layout;return d._idx===h._idx?h._set-d._set:h._idx-d._idx}),this.update(n),n},update:function(i){var t,e,a,s,r,n=!1;for(t=0,e=i.length;t<e;++t)s=(a=i[t]).model(),(r=a.$layout)._hidable=s&&"auto"===s.display,r._visible=a.visible(),n|=r._hidable;n&&function Mu(i){var n,t,e,a,s,r,o;for(n=0,t=i.length;n<t;++n)(a=(e=i[n]).$layout)._visible&&(o=new Proxy(e._el,{get:(l,d)=>l.getProps([d],!0)[d]}),s=e.geometry(),r=_r(o,e.model(),s),a._box.update(r,s,e.rotation()));(function ku(i,n){var t,e,a,s;for(t=i.length-1;t>=0;--t)for(a=i[t].$layout,e=t-1;e>=0&&a._visible;--e)(s=i[e].$layout)._visible&&a._box.intersects(s._box)&&n(a,s)})(i,function(l,d){var h=l._hidable,u=d._hidable;h&&u||u?d._visible=!1:h&&(l._visible=!1)})}(i)},lookup:function(i,n){var t,e;for(t=i.length-1;t>=0;--t)if((e=i[t].$layout)&&e._visible&&e._box.contains(n))return i[t];return null},draw:function(i,n){var t,e,a,s,r,o;for(t=0,e=n.length;t<e;++t)(s=(a=n[t]).$layout)._visible&&(r=a.geometry(),o=_r(a._el,a.model(),r),s._box.update(o,r,a.rotation()),a.draw(i,o))}},rt="$datalabels",br="$default";function xa(i,n,t,e){if(n){var r,a=t.$context,s=t.$groups;n[s._set]&&(r=n[s._set][s._key])&&!0===B(r,[a,e])&&(i[rt]._dirty=!0,t.update(a))}}var Tu={id:"datalabels",defaults:{align:"center",anchor:"center",backgroundColor:null,borderColor:null,borderRadius:0,borderWidth:0,clamp:!1,clip:!1,color:void 0,display:!0,font:{family:void 0,lineHeight:1.2,size:void 0,style:void 0,weight:null},formatter:function(i){if(R(i))return null;var t,e,a,n=i;if(P(i))if(R(i.label))if(R(i.r))for(n="",a=0,e=(t=Object.keys(i)).length;a<e;++a)n+=(0!==a?", ":"")+t[a]+": "+i[t[a]];else n=i.r;else n=i.label;return""+n},labels:void 0,listeners:{},offset:4,opacity:1,padding:{top:4,right:4,bottom:4,left:4},rotation:0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,textShadowBlur:0,textShadowColor:void 0},beforeInit:function(i){i[rt]={_actives:[]}},beforeUpdate:function(i){var n=i[rt];n._listened=!1,n._listeners={},n._datasets=[],n._labels=[]},afterDatasetUpdate:function(i,n,t){var u,f,p,g,m,_,v,b,e=n.index,a=i[rt],s=a._datasets[e]=[],r=i.isDatasetVisible(e),o=i.data.datasets[e],l=function Su(i,n){var s,r,t=i.datalabels,e={},a=[];return!1===t?null:(!0===t&&(t={}),n=pt({},[n,t]),s=n.labels||{},r=Object.keys(s),delete n.labels,r.length?r.forEach(function(o){s[o]&&a.push(pt({},[n,s[o],{_key:o}]))}):a.push(n),e=a.reduce(function(o,l){return L(l.listeners||{},function(d,h){o[h]=o[h]||{},o[h][l._key||br]=d}),delete l.listeners,o},{}),{labels:a,listeners:e})}(o,t),d=n.meta.data||[],h=i.ctx;for(h.save(),u=0,p=d.length;u<p;++u)if((v=d[u])[rt]=[],r&&v&&i.getDataVisibility(u)&&!v.skip)for(f=0,g=l.labels.length;f<g;++f)_=(m=l.labels[f])._key,(b=new pr(m,h,v,u)).$groups={_set:e,_key:_||br},b.$context={active:!1,chart:i,dataIndex:u,dataset:o,datasetIndex:e},b.update(b.$context),v[rt].push(b),s.push(b);h.restore(),pt(a._listeners,l.listeners,{merger:function(x,D,y){D[x]=D[x]||{},D[x][n.index]=y[x],a._listened=!0}})},afterUpdate:function(i){i[rt]._labels=Oe.prepare(i[rt]._datasets)},afterDatasetsDraw:function(i){Oe.draw(i,i[rt]._labels)},beforeEvent:function(i,n){if(i[rt]._listened){var t=n.event;switch(t.type){case"mousemove":case"mouseout":!function Ru(i,n){var a,s,t=i[rt],e=t._listeners;if(e.enter||e.leave){if("mousemove"===n.type)s=Oe.lookup(t._labels,n);else if("mouseout"!==n.type)return;a=t._hovered,t._hovered=s,function Au(i,n,t,e,a){var s,r;!t&&!e||(t?e?t!==e&&(r=s=!0):r=!0:s=!0,r&&xa(i,n.leave,t,a),s&&xa(i,n.enter,e,a))}(i,e,a,s,n)}}(i,t);break;case"click":!function Eu(i,n){var t=i[rt],e=t._listeners.click,a=e&&Oe.lookup(t._labels,n);a&&xa(i,e,a,n)}(i,t)}}},afterEvent:function(i){var s,r,o,l,d,h,u,n=i[rt],a=function(i,n){var a,s,r,o,t=i.slice(),e=[];for(a=0,r=n.length;a<r;++a)-1===(s=t.indexOf(o=n[a]))?e.push([o,1]):t.splice(s,1);for(a=0,r=t.length;a<r;++a)e.push([t[a],-1]);return e}(n._actives,n._actives=i.getActiveElements());for(s=0,r=a.length;s<r;++s)if((d=a[s])[1])for(o=0,l=(u=d[0].element[rt]||[]).length;o<l;++o)(h=u[o]).$context.active=1===d[1],h.update(h.$context);(n._dirty||a.length)&&(Oe.update(n._labels),i.render()),delete n._dirty}},N=O(9969);let Iu=(()=>{class i{constructor(t){this.restService=t,this.apiName="Default",this.create=(e,a)=>this.restService.request({method:"POST",url:"/api/app/geofence",body:e},{apiName:this.apiName,...a}),this.delete=(e,a)=>this.restService.request({method:"DELETE",url:`/api/app/geofence/${e}`},{apiName:this.apiName,...a}),this.get=(e,a)=>this.restService.request({method:"GET",url:`/api/app/geofence/${e}`},{apiName:this.apiName,...a}),this.getList=(e,a)=>this.restService.request({method:"GET",url:"/api/app/geofence",params:{sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},{apiName:this.apiName,...a}),this.update=(e,a,s)=>this.restService.request({method:"PUT",url:`/api/app/geofence/${e}`,body:a},{apiName:this.apiName,...s})}static{this.\u0275fac=function(e){return new(e||i)(c.KVO(fe.G9T))}}static{this.\u0275prov=c.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Pu=(()=>{class i{constructor(t){this.restService=t,this.apiName="Default",this.getAverageDwellTimeByLocation=(e,a)=>this.restService.request({method:"GET",url:"/api/app/reporting/average-dwell-time-by-location",params:{rangeBegin:e.rangeBegin,rangeEnd:e.rangeEnd,locationIds:e.locationIds}},{apiName:this.apiName,...a}),this.getAverageHoursSinceMoveByLocation=(e,a)=>this.restService.request({method:"GET",url:"/api/app/reporting/average-hours-since-move-by-location",params:{rangeBegin:e.rangeBegin,rangeEnd:e.rangeEnd,locationIds:e.locationIds}},{apiName:this.apiName,...a}),this.getInactiveAssetsByLocation=(e,a,s)=>this.restService.request({method:"GET",url:"/api/app/reporting/inactive-assets-by-location",params:{rangeBegin:e.rangeBegin,rangeEnd:e.rangeEnd,locationIds:e.locationIds,inactiveHours:a}},{apiName:this.apiName,...s}),this.getTrailerCountByLocation=(e,a)=>this.restService.request({method:"GET",url:"/api/app/reporting/trailer-count-by-location",params:{rangeBegin:e.rangeBegin,rangeEnd:e.rangeEnd,locationIds:e.locationIds}},{apiName:this.apiName,...a})}static{this.\u0275fac=function(e){return new(e||i)(c.KVO(fe.G9T))}}static{this.\u0275prov=c.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var vr=O(4721),yr=O(1997),E=O(6600),Ve=O(6039),Fu=O(5024),le=O(177);const Ou=["input"],Vu=["formField"],Lu=["*"];let xr=0;class Dr{constructor(n,t){this.source=n,this.value=t}}const Bu={provide:V.kq,useExisting:(0,c.Rfq)(()=>Mr),multi:!0},kr=new c.nKC("MatRadioGroup"),zu=new c.nKC("mat-radio-default-options",{providedIn:"root",factory:function Nu(){return{color:"accent"}}});let Mr=(()=>{class i{get name(){return this._name}set name(t){this._name=t,this._updateRadioButtonNames()}get labelPosition(){return this._labelPosition}set labelPosition(t){this._labelPosition="before"===t?"before":"after",this._markRadiosForCheck()}get value(){return this._value}set value(t){this._value!==t&&(this._value=t,this._updateSelectedRadioFromValue(),this._checkSelectedRadioButton())}_checkSelectedRadioButton(){this._selected&&!this._selected.checked&&(this._selected.checked=!0)}get selected(){return this._selected}set selected(t){this._selected=t,this.value=t?t.value:null,this._checkSelectedRadioButton()}get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._markRadiosForCheck()}get required(){return this._required}set required(t){this._required=t,this._markRadiosForCheck()}constructor(t){this._changeDetector=t,this._value=null,this._name="mat-radio-group-"+xr++,this._selected=null,this._isInitialized=!1,this._labelPosition="after",this._disabled=!1,this._required=!1,this._controlValueAccessorChangeFn=()=>{},this.onTouched=()=>{},this.change=new c.bkB}ngAfterContentInit(){this._isInitialized=!0,this._buttonChanges=this._radios.changes.subscribe(()=>{this.selected&&!this._radios.find(t=>t===this.selected)&&(this._selected=null)})}ngOnDestroy(){this._buttonChanges?.unsubscribe()}_touch(){this.onTouched&&this.onTouched()}_updateRadioButtonNames(){this._radios&&this._radios.forEach(t=>{t.name=this.name,t._markForCheck()})}_updateSelectedRadioFromValue(){this._radios&&(null===this._selected||this._selected.value!==this._value)&&(this._selected=null,this._radios.forEach(e=>{e.checked=this.value===e.value,e.checked&&(this._selected=e)}))}_emitChangeEvent(){this._isInitialized&&this.change.emit(new Dr(this._selected,this._value))}_markRadiosForCheck(){this._radios&&this._radios.forEach(t=>t._markForCheck())}writeValue(t){this.value=t,this._changeDetector.markForCheck()}registerOnChange(t){this._controlValueAccessorChangeFn=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t,this._changeDetector.markForCheck()}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.gRc))}}static{this.\u0275dir=c.FsC({type:i,selectors:[["mat-radio-group"]],contentQueries:function(e,a,s){if(1&e&&c.wni(s,Da,5),2&e){let r;c.mGM(r=c.lsd())&&(a._radios=r)}},hostAttrs:["role","radiogroup",1,"mat-mdc-radio-group"],inputs:{color:"color",name:"name",labelPosition:"labelPosition",value:"value",selected:"selected",disabled:[2,"disabled","disabled",c.L39],required:[2,"required","required",c.L39]},outputs:{change:"change"},exportAs:["matRadioGroup"],standalone:!0,features:[c.Jv_([Bu,{provide:kr,useExisting:i}]),c.GFd]})}}return i})(),Da=(()=>{class i{get checked(){return this._checked}set checked(t){this._checked!==t&&(this._checked=t,t&&this.radioGroup&&this.radioGroup.value!==this.value?this.radioGroup.selected=this:!t&&this.radioGroup&&this.radioGroup.value===this.value&&(this.radioGroup.selected=null),t&&this._radioDispatcher.notify(this.id,this.name),this._changeDetector.markForCheck())}get value(){return this._value}set value(t){this._value!==t&&(this._value=t,null!==this.radioGroup&&(this.checked||(this.checked=this.radioGroup.value===t),this.checked&&(this.radioGroup.selected=this)))}get labelPosition(){return this._labelPosition||this.radioGroup&&this.radioGroup.labelPosition||"after"}set labelPosition(t){this._labelPosition=t}get disabled(){return this._disabled||null!==this.radioGroup&&this.radioGroup.disabled}set disabled(t){this._setDisabled(t)}get required(){return this._required||this.radioGroup&&this.radioGroup.required}set required(t){this._required=t}get color(){return this._color||this.radioGroup&&this.radioGroup.color||this._providerOverride&&this._providerOverride.color||"accent"}set color(t){this._color=t}get inputId(){return`${this.id||this._uniqueId}-input`}constructor(t,e,a,s,r,o,l,d){this._elementRef=e,this._changeDetector=a,this._focusMonitor=s,this._radioDispatcher=r,this._providerOverride=l,this._uniqueId="mat-radio-"+ ++xr,this.id=this._uniqueId,this.disableRipple=!1,this.tabIndex=0,this.change=new c.bkB,this._checked=!1,this._value=null,this._removeUniqueSelectionListener=()=>{},this.radioGroup=t,this._noopAnimations="NoopAnimations"===o,d&&(this.tabIndex=(0,c.Udg)(d,0))}focus(t,e){e?this._focusMonitor.focusVia(this._inputElement,e,t):this._inputElement.nativeElement.focus(t)}_markForCheck(){this._changeDetector.markForCheck()}ngOnInit(){this.radioGroup&&(this.checked=this.radioGroup.value===this._value,this.checked&&(this.radioGroup.selected=this),this.name=this.radioGroup.name),this._removeUniqueSelectionListener=this._radioDispatcher.listen((t,e)=>{t!==this.id&&e===this.name&&(this.checked=!1)})}ngDoCheck(){this._updateTabIndex()}ngAfterViewInit(){this._updateTabIndex(),this._focusMonitor.monitor(this._elementRef,!0).subscribe(t=>{!t&&this.radioGroup&&this.radioGroup._touch()})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._removeUniqueSelectionListener()}_emitChangeEvent(){this.change.emit(new Dr(this,this._value))}_isRippleDisabled(){return this.disableRipple||this.disabled}_onInputClick(t){t.stopPropagation()}_onInputInteraction(t){if(t.stopPropagation(),!this.checked&&!this.disabled){const e=this.radioGroup&&this.value!==this.radioGroup.value;this.checked=!0,this._emitChangeEvent(),this.radioGroup&&(this.radioGroup._controlValueAccessorChangeFn(this.value),e&&this.radioGroup._emitChangeEvent())}}_onTouchTargetClick(t){this._onInputInteraction(t),this.disabled||this._inputElement.nativeElement.focus()}_setDisabled(t){this._disabled!==t&&(this._disabled=t,this._changeDetector.markForCheck())}_updateTabIndex(){const t=this.radioGroup;let e;if(e=t&&t.selected&&!this.disabled?t.selected===this?this.tabIndex:-1:this.tabIndex,e!==this._previousTabIndex){const a=this._inputElement?.nativeElement;a&&(a.setAttribute("tabindex",e+""),this._previousTabIndex=e)}}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(kr,8),c.rXU(c.aKT),c.rXU(c.gRc),c.rXU(Ve.FN),c.rXU(Fu.zP),c.rXU(c.bc$,8),c.rXU(zu,8),c.kS0("tabindex"))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-radio-button"]],viewQuery:function(e,a){if(1&e&&(c.GBs(Ou,5),c.GBs(Vu,7,c.aKT)),2&e){let s;c.mGM(s=c.lsd())&&(a._inputElement=s.first),c.mGM(s=c.lsd())&&(a._rippleTrigger=s.first)}},hostAttrs:[1,"mat-mdc-radio-button"],hostVars:15,hostBindings:function(e,a){1&e&&c.bIt("focus",function(){return a._inputElement.nativeElement.focus()}),2&e&&(c.BMQ("id",a.id)("tabindex",null)("aria-label",null)("aria-labelledby",null)("aria-describedby",null),c.AVh("mat-primary","primary"===a.color)("mat-accent","accent"===a.color)("mat-warn","warn"===a.color)("mat-mdc-radio-checked",a.checked)("_mat-animation-noopable",a._noopAnimations))},inputs:{id:"id",name:"name",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],disableRipple:[2,"disableRipple","disableRipple",c.L39],tabIndex:[2,"tabIndex","tabIndex",t=>null==t?0:(0,c.Udg)(t)],checked:[2,"checked","checked",c.L39],value:"value",labelPosition:"labelPosition",disabled:[2,"disabled","disabled",c.L39],required:[2,"required","required",c.L39],color:"color"},outputs:{change:"change"},exportAs:["matRadioButton"],standalone:!0,features:[c.GFd,c.aNF],ngContentSelectors:Lu,decls:13,vars:16,consts:[["formField",""],["input",""],["mat-internal-form-field","",3,"labelPosition"],[1,"mdc-radio"],[1,"mat-mdc-radio-touch-target",3,"click"],["type","radio",1,"mdc-radio__native-control",3,"change","id","checked","disabled","required"],[1,"mdc-radio__background"],[1,"mdc-radio__outer-circle"],[1,"mdc-radio__inner-circle"],["mat-ripple","",1,"mat-radio-ripple","mat-mdc-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mat-ripple-element","mat-radio-persistent-ripple"],[1,"mdc-label",3,"for"]],template:function(e,a){if(1&e){const s=c.RV6();c.NAR(),c.j41(0,"div",2,0)(2,"div",3)(3,"div",4),c.bIt("click",function(o){return c.eBV(s),c.Njj(a._onTouchTargetClick(o))}),c.k0s(),c.j41(4,"input",5,1),c.bIt("change",function(o){return c.eBV(s),c.Njj(a._onInputInteraction(o))}),c.k0s(),c.j41(6,"div",6),c.nrm(7,"div",7)(8,"div",8),c.k0s(),c.j41(9,"div",9),c.nrm(10,"div",10),c.k0s()(),c.j41(11,"label",11),c.SdG(12),c.k0s()()}2&e&&(c.Y8G("labelPosition",a.labelPosition),c.R7$(2),c.AVh("mdc-radio--disabled",a.disabled),c.R7$(2),c.Y8G("id",a.inputId)("checked",a.checked)("disabled",a.disabled)("required",a.required),c.BMQ("name",a.name)("value",a.value)("aria-label",a.ariaLabel)("aria-labelledby",a.ariaLabelledby)("aria-describedby",a.ariaDescribedby),c.R7$(5),c.Y8G("matRippleTrigger",a._rippleTrigger.nativeElement)("matRippleDisabled",a._isRippleDisabled())("matRippleCentered",!0),c.R7$(2),c.Y8G("for",a.inputId))},dependencies:[E.r6,E.tO],styles:['.mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:"";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:"";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:""}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}'],encapsulation:2,changeDetection:0})}}return i})(),ju=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=c.$C({type:i})}static{this.\u0275inj=c.G2t({imports:[E.yE,le.MD,E.pZ,Da,E.yE]})}}return i})();var Vt=O(6467),ce=O(6969),Le=O(6939),qt=O(8834),Hu=O(3980),Qt=O(1413),bt=O(8359),Mi=O(7786),ka=O(7673),S=O(7336),Be=O(8203),ze=O(6860),Ma=O(6697),Ca=O(9172),Yu=O(5964),Wu=O(4085);const $u=["mat-calendar-body",""];function Gu(i,n){if(1&i&&(c.j41(0,"tr",0)(1,"td",3),c.EFF(2),c.k0s()()),2&i){const t=c.XpG();c.R7$(),c.xc7("padding-top",t._cellPadding)("padding-bottom",t._cellPadding),c.BMQ("colspan",t.numCols),c.R7$(),c.SpI(" ",t.label," ")}}function Xu(i,n){if(1&i&&(c.j41(0,"td",3),c.EFF(1),c.k0s()),2&i){const t=c.XpG(2);c.xc7("padding-top",t._cellPadding)("padding-bottom",t._cellPadding),c.BMQ("colspan",t._firstRowOffset),c.R7$(),c.SpI(" ",t._firstRowOffset>=t.labelMinRequiredCells?t.label:""," ")}}function Ku(i,n){if(1&i){const t=c.RV6();c.j41(0,"td",6)(1,"button",7),c.bIt("click",function(a){const s=c.eBV(t).$implicit,r=c.XpG(2);return c.Njj(r._cellClicked(s,a))})("focus",function(a){const s=c.eBV(t).$implicit,r=c.XpG(2);return c.Njj(r._emitActiveDateChange(s,a))}),c.j41(2,"span",8),c.EFF(3),c.k0s(),c.nrm(4,"span",9),c.k0s()()}if(2&i){const t=n.$implicit,e=n.$index,a=c.XpG().$index,s=c.XpG();c.xc7("width",s._cellWidth)("padding-top",s._cellPadding)("padding-bottom",s._cellPadding),c.BMQ("data-mat-row",a)("data-mat-col",e),c.R7$(),c.AVh("mat-calendar-body-disabled",!t.enabled)("mat-calendar-body-active",s._isActiveCell(a,e))("mat-calendar-body-range-start",s._isRangeStart(t.compareValue))("mat-calendar-body-range-end",s._isRangeEnd(t.compareValue))("mat-calendar-body-in-range",s._isInRange(t.compareValue))("mat-calendar-body-comparison-bridge-start",s._isComparisonBridgeStart(t.compareValue,a,e))("mat-calendar-body-comparison-bridge-end",s._isComparisonBridgeEnd(t.compareValue,a,e))("mat-calendar-body-comparison-start",s._isComparisonStart(t.compareValue))("mat-calendar-body-comparison-end",s._isComparisonEnd(t.compareValue))("mat-calendar-body-in-comparison-range",s._isInComparisonRange(t.compareValue))("mat-calendar-body-preview-start",s._isPreviewStart(t.compareValue))("mat-calendar-body-preview-end",s._isPreviewEnd(t.compareValue))("mat-calendar-body-in-preview",s._isInPreview(t.compareValue)),c.Y8G("ngClass",t.cssClasses)("tabindex",s._isActiveCell(a,e)?0:-1),c.BMQ("aria-label",t.ariaLabel)("aria-disabled",!t.enabled||null)("aria-pressed",s._isSelected(t.compareValue))("aria-current",s.todayValue===t.compareValue?"date":null)("aria-describedby",s._getDescribedby(t.compareValue)),c.R7$(),c.AVh("mat-calendar-body-selected",s._isSelected(t.compareValue))("mat-calendar-body-comparison-identical",s._isComparisonIdentical(t.compareValue))("mat-calendar-body-today",s.todayValue===t.compareValue),c.R7$(),c.SpI(" ",t.displayValue," ")}}function qu(i,n){if(1&i&&(c.j41(0,"tr",1),c.DNE(1,Xu,2,6,"td",4),c.Z7z(2,Ku,5,48,"td",5,c.fX1),c.k0s()),2&i){const t=n.$implicit,e=n.$index,a=c.XpG();c.R7$(),c.vxM(0===e&&a._firstRowOffset?1:-1),c.R7$(),c.Dyx(t)}}function Qu(i,n){if(1&i&&(c.j41(0,"th",2)(1,"span",6),c.EFF(2),c.k0s(),c.j41(3,"span",3),c.EFF(4),c.k0s()()),2&i){const t=n.$implicit;c.R7$(2),c.JRh(t.long),c.R7$(2),c.JRh(t.narrow)}}const Ju=["*"];function Zu(i,n){}function tf(i,n){if(1&i){const t=c.RV6();c.j41(0,"mat-month-view",4),c.mxI("activeDateChange",function(a){c.eBV(t);const s=c.XpG();return c.DH7(s.activeDate,a)||(s.activeDate=a),c.Njj(a)}),c.bIt("_userSelection",function(a){c.eBV(t);const s=c.XpG();return c.Njj(s._dateSelected(a))})("dragStarted",function(a){c.eBV(t);const s=c.XpG();return c.Njj(s._dragStarted(a))})("dragEnded",function(a){c.eBV(t);const s=c.XpG();return c.Njj(s._dragEnded(a))}),c.k0s()}if(2&i){const t=c.XpG();c.R50("activeDate",t.activeDate),c.Y8G("selected",t.selected)("dateFilter",t.dateFilter)("maxDate",t.maxDate)("minDate",t.minDate)("dateClass",t.dateClass)("comparisonStart",t.comparisonStart)("comparisonEnd",t.comparisonEnd)("startDateAccessibleName",t.startDateAccessibleName)("endDateAccessibleName",t.endDateAccessibleName)("activeDrag",t._activeDrag)}}function ef(i,n){if(1&i){const t=c.RV6();c.j41(0,"mat-year-view",5),c.mxI("activeDateChange",function(a){c.eBV(t);const s=c.XpG();return c.DH7(s.activeDate,a)||(s.activeDate=a),c.Njj(a)}),c.bIt("monthSelected",function(a){c.eBV(t);const s=c.XpG();return c.Njj(s._monthSelectedInYearView(a))})("selectedChange",function(a){c.eBV(t);const s=c.XpG();return c.Njj(s._goToDateInView(a,"month"))}),c.k0s()}if(2&i){const t=c.XpG();c.R50("activeDate",t.activeDate),c.Y8G("selected",t.selected)("dateFilter",t.dateFilter)("maxDate",t.maxDate)("minDate",t.minDate)("dateClass",t.dateClass)}}function af(i,n){if(1&i){const t=c.RV6();c.j41(0,"mat-multi-year-view",6),c.mxI("activeDateChange",function(a){c.eBV(t);const s=c.XpG();return c.DH7(s.activeDate,a)||(s.activeDate=a),c.Njj(a)}),c.bIt("yearSelected",function(a){c.eBV(t);const s=c.XpG();return c.Njj(s._yearSelectedInMultiYearView(a))})("selectedChange",function(a){c.eBV(t);const s=c.XpG();return c.Njj(s._goToDateInView(a,"year"))}),c.k0s()}if(2&i){const t=c.XpG();c.R50("activeDate",t.activeDate),c.Y8G("selected",t.selected)("dateFilter",t.dateFilter)("maxDate",t.maxDate)("minDate",t.minDate)("dateClass",t.dateClass)}}function nf(i,n){}const sf=["button"],rf=[[["","matDatepickerToggleIcon",""]]],of=["[matDatepickerToggleIcon]"];function lf(i,n){1&i&&(c.qSk(),c.j41(0,"svg",2),c.nrm(1,"path",3),c.k0s())}const cf=[[["input","matStartDate",""]],[["input","matEndDate",""]]],df=["input[matStartDate]","input[matEndDate]"];let Ne=(()=>{class i{constructor(){this.changes=new Qt.B,this.calendarLabel="Calendar",this.openCalendarLabel="Open calendar",this.closeCalendarLabel="Close calendar",this.prevMonthLabel="Previous month",this.nextMonthLabel="Next month",this.prevYearLabel="Previous year",this.nextYearLabel="Next year",this.prevMultiYearLabel="Previous 24 years",this.nextMultiYearLabel="Next 24 years",this.switchToMonthViewLabel="Choose date",this.switchToMultiYearViewLabel="Choose month and year",this.startDateLabel="Start date",this.endDateLabel="End date"}formatYearRange(t,e){return`${t} \u2013 ${e}`}formatYearRangeLabel(t,e){return`${t} to ${e}`}static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275prov=c.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();class wa{constructor(n,t,e,a,s={},r=n,o){this.value=n,this.displayValue=t,this.ariaLabel=e,this.enabled=a,this.cssClasses=s,this.compareValue=r,this.rawValue=o}}let hf=1;const Cr=(0,ze.BQ)({passive:!1,capture:!0}),Lt=(0,ze.BQ)({passive:!0,capture:!0}),Ci=(0,ze.BQ)({passive:!0});let de=(()=>{class i{ngAfterViewChecked(){this._focusActiveCellAfterViewChecked&&(this._focusActiveCell(),this._focusActiveCellAfterViewChecked=!1)}constructor(t,e){this._elementRef=t,this._ngZone=e,this._platform=(0,c.WQX)(ze.OD),this._focusActiveCellAfterViewChecked=!1,this.numCols=7,this.activeCell=0,this.isRange=!1,this.cellAspectRatio=1,this.previewStart=null,this.previewEnd=null,this.selectedValueChange=new c.bkB,this.previewChange=new c.bkB,this.activeDateChange=new c.bkB,this.dragStarted=new c.bkB,this.dragEnded=new c.bkB,this._didDragSinceMouseDown=!1,this._enterHandler=a=>{if(this._skipNextFocus&&"focus"===a.type)this._skipNextFocus=!1;else if(a.target&&this.isRange){const s=this._getCellFromElement(a.target);s&&this._ngZone.run(()=>this.previewChange.emit({value:s.enabled?s:null,event:a}))}},this._touchmoveHandler=a=>{if(!this.isRange)return;const s=wr(a),r=s?this._getCellFromElement(s):null;s!==a.target&&(this._didDragSinceMouseDown=!0),Aa(a.target)&&a.preventDefault(),this._ngZone.run(()=>this.previewChange.emit({value:r?.enabled?r:null,event:a}))},this._leaveHandler=a=>{null!==this.previewEnd&&this.isRange&&("blur"!==a.type&&(this._didDragSinceMouseDown=!0),a.target&&this._getCellFromElement(a.target)&&(!a.relatedTarget||!this._getCellFromElement(a.relatedTarget))&&this._ngZone.run(()=>this.previewChange.emit({value:null,event:a})))},this._mousedownHandler=a=>{if(!this.isRange)return;this._didDragSinceMouseDown=!1;const s=a.target&&this._getCellFromElement(a.target);!s||!this._isInRange(s.compareValue)||this._ngZone.run(()=>{this.dragStarted.emit({value:s.rawValue,event:a})})},this._mouseupHandler=a=>{if(!this.isRange)return;const s=Aa(a.target);s?s.closest(".mat-calendar-body")===this._elementRef.nativeElement&&this._ngZone.run(()=>{const r=this._getCellFromElement(s);this.dragEnded.emit({value:r?.rawValue??null,event:a})}):this._ngZone.run(()=>{this.dragEnded.emit({value:null,event:a})})},this._touchendHandler=a=>{const s=wr(a);s&&this._mouseupHandler({target:s})},this._id="mat-calendar-body-"+hf++,this._startDateLabelId=`${this._id}-start-date`,this._endDateLabelId=`${this._id}-end-date`,e.runOutsideAngular(()=>{const a=t.nativeElement;a.addEventListener("touchmove",this._touchmoveHandler,Cr),a.addEventListener("mouseenter",this._enterHandler,Lt),a.addEventListener("focus",this._enterHandler,Lt),a.addEventListener("mouseleave",this._leaveHandler,Lt),a.addEventListener("blur",this._leaveHandler,Lt),a.addEventListener("mousedown",this._mousedownHandler,Ci),a.addEventListener("touchstart",this._mousedownHandler,Ci),this._platform.isBrowser&&(window.addEventListener("mouseup",this._mouseupHandler),window.addEventListener("touchend",this._touchendHandler))})}_cellClicked(t,e){this._didDragSinceMouseDown||t.enabled&&this.selectedValueChange.emit({value:t.value,event:e})}_emitActiveDateChange(t,e){t.enabled&&this.activeDateChange.emit({value:t.value,event:e})}_isSelected(t){return this.startValue===t||this.endValue===t}ngOnChanges(t){const e=t.numCols,{rows:a,numCols:s}=this;(t.rows||e)&&(this._firstRowOffset=a&&a.length&&a[0].length?s-a[0].length:0),(t.cellAspectRatio||e||!this._cellPadding)&&(this._cellPadding=50*this.cellAspectRatio/s+"%"),(e||!this._cellWidth)&&(this._cellWidth=100/s+"%")}ngOnDestroy(){const t=this._elementRef.nativeElement;t.removeEventListener("touchmove",this._touchmoveHandler,Cr),t.removeEventListener("mouseenter",this._enterHandler,Lt),t.removeEventListener("focus",this._enterHandler,Lt),t.removeEventListener("mouseleave",this._leaveHandler,Lt),t.removeEventListener("blur",this._leaveHandler,Lt),t.removeEventListener("mousedown",this._mousedownHandler,Ci),t.removeEventListener("touchstart",this._mousedownHandler,Ci),this._platform.isBrowser&&(window.removeEventListener("mouseup",this._mouseupHandler),window.removeEventListener("touchend",this._touchendHandler))}_isActiveCell(t,e){let a=t*this.numCols+e;return t&&(a-=this._firstRowOffset),a==this.activeCell}_focusActiveCell(t=!0){this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.pipe((0,Ma.s)(1)).subscribe(()=>{setTimeout(()=>{const e=this._elementRef.nativeElement.querySelector(".mat-calendar-body-active");e&&(t||(this._skipNextFocus=!0),e.focus())})})})}_scheduleFocusActiveCellAfterViewChecked(){this._focusActiveCellAfterViewChecked=!0}_isRangeStart(t){return Ra(t,this.startValue,this.endValue)}_isRangeEnd(t){return Ea(t,this.startValue,this.endValue)}_isInRange(t){return Ta(t,this.startValue,this.endValue,this.isRange)}_isComparisonStart(t){return Ra(t,this.comparisonStart,this.comparisonEnd)}_isComparisonBridgeStart(t,e,a){if(!this._isComparisonStart(t)||this._isRangeStart(t)||!this._isInRange(t))return!1;let s=this.rows[e][a-1];if(!s){const r=this.rows[e-1];s=r&&r[r.length-1]}return s&&!this._isRangeEnd(s.compareValue)}_isComparisonBridgeEnd(t,e,a){if(!this._isComparisonEnd(t)||this._isRangeEnd(t)||!this._isInRange(t))return!1;let s=this.rows[e][a+1];if(!s){const r=this.rows[e+1];s=r&&r[0]}return s&&!this._isRangeStart(s.compareValue)}_isComparisonEnd(t){return Ea(t,this.comparisonStart,this.comparisonEnd)}_isInComparisonRange(t){return Ta(t,this.comparisonStart,this.comparisonEnd,this.isRange)}_isComparisonIdentical(t){return this.comparisonStart===this.comparisonEnd&&t===this.comparisonStart}_isPreviewStart(t){return Ra(t,this.previewStart,this.previewEnd)}_isPreviewEnd(t){return Ea(t,this.previewStart,this.previewEnd)}_isInPreview(t){return Ta(t,this.previewStart,this.previewEnd,this.isRange)}_getDescribedby(t){return this.isRange?this.startValue===t&&this.endValue===t?`${this._startDateLabelId} ${this._endDateLabelId}`:this.startValue===t?this._startDateLabelId:this.endValue===t?this._endDateLabelId:null:null}_getCellFromElement(t){const e=Aa(t);if(e){const a=e.getAttribute("data-mat-row"),s=e.getAttribute("data-mat-col");if(a&&s)return this.rows[parseInt(a)][parseInt(s)]}return null}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.aKT),c.rXU(c.SKi))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["","mat-calendar-body",""]],hostAttrs:[1,"mat-calendar-body"],inputs:{label:"label",rows:"rows",todayValue:"todayValue",startValue:"startValue",endValue:"endValue",labelMinRequiredCells:"labelMinRequiredCells",numCols:"numCols",activeCell:"activeCell",isRange:"isRange",cellAspectRatio:"cellAspectRatio",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",previewStart:"previewStart",previewEnd:"previewEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName"},outputs:{selectedValueChange:"selectedValueChange",previewChange:"previewChange",activeDateChange:"activeDateChange",dragStarted:"dragStarted",dragEnded:"dragEnded"},exportAs:["matCalendarBody"],standalone:!0,features:[c.OA$,c.aNF],attrs:$u,decls:7,vars:5,consts:[["aria-hidden","true"],["role","row"],[1,"mat-calendar-body-hidden-label",3,"id"],[1,"mat-calendar-body-label"],[1,"mat-calendar-body-label",3,"paddingTop","paddingBottom"],["role","gridcell",1,"mat-calendar-body-cell-container",3,"width","paddingTop","paddingBottom"],["role","gridcell",1,"mat-calendar-body-cell-container"],["type","button",1,"mat-calendar-body-cell",3,"click","focus","ngClass","tabindex"],[1,"mat-calendar-body-cell-content","mat-focus-indicator"],["aria-hidden","true",1,"mat-calendar-body-cell-preview"]],template:function(e,a){1&e&&(c.DNE(0,Gu,3,6,"tr",0),c.Z7z(1,qu,4,1,"tr",1,c.fX1),c.j41(3,"label",2),c.EFF(4),c.k0s(),c.j41(5,"label",2),c.EFF(6),c.k0s()),2&e&&(c.vxM(a._firstRowOffset<a.labelMinRequiredCells?0:-1),c.R7$(),c.Dyx(a.rows),c.R7$(2),c.Y8G("id",a._startDateLabelId),c.R7$(),c.SpI(" ",a.startDateAccessibleName,"\n"),c.R7$(),c.Y8G("id",a._endDateLabelId),c.R7$(),c.SpI(" ",a.endDateAccessibleName,"\n"))},dependencies:[le.YU],styles:['.mat-calendar-body{min-width:224px}.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-outline-color)}.mat-calendar-body-label{height:0;line-height:0;text-align:start;padding-left:4.7142857143%;padding-right:4.7142857143%;font-size:var(--mat-datepicker-calendar-body-label-text-size);font-weight:var(--mat-datepicker-calendar-body-label-text-weight);color:var(--mat-datepicker-calendar-body-label-text-color)}.mat-calendar-body-hidden-label{display:none}.mat-calendar-body-cell-container{position:relative;height:0;line-height:0}.mat-calendar-body-cell{-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);position:absolute;top:0;left:0;width:100%;height:100%;background:none;text-align:center;outline:none;font-family:inherit;margin:0}.mat-calendar-body-cell::-moz-focus-inner{border:0}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-cell-preview{content:"";position:absolute;top:5%;left:0;z-index:0;box-sizing:border-box;display:block;height:90%;width:100%}.mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-start::after,.mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,.mat-calendar-body-comparison-start::after,.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:5%;width:95%;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,[dir=rtl] .mat-calendar-body-comparison-start::after,[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:0;border-radius:0;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,.mat-calendar-body-comparison-end::after,.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}[dir=rtl] .mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,[dir=rtl] .mat-calendar-body-comparison-end::after,[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{left:5%;border-radius:0;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-comparison-bridge-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-bridge-end.mat-calendar-body-range-start::after{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end.mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-end.mat-calendar-body-range-start::after{width:90%}.mat-calendar-body-in-preview{color:var(--mat-datepicker-calendar-date-preview-state-outline-color)}.mat-calendar-body-in-preview .mat-calendar-body-cell-preview{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:0;border-right:dashed 1px}.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:0;border-left:dashed 1px}.mat-calendar-body-disabled{cursor:default}.mat-calendar-body-disabled>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){color:var(--mat-datepicker-calendar-date-disabled-state-text-color)}.mat-calendar-body-disabled>.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-disabled-state-outline-color)}.cdk-high-contrast-active .mat-calendar-body-disabled{opacity:.5}.mat-calendar-body-cell-content{top:5%;left:5%;z-index:1;display:flex;align-items:center;justify-content:center;box-sizing:border-box;width:90%;height:90%;line-height:1;border-width:1px;border-style:solid;border-radius:999px;color:var(--mat-datepicker-calendar-date-text-color);border-color:var(--mat-datepicker-calendar-date-outline-color)}.mat-calendar-body-cell-content.mat-focus-indicator{position:absolute}.cdk-high-contrast-active .mat-calendar-body-cell-content{border:none}.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-focus-state-background-color)}@media(hover: hover){.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-hover-state-background-color)}}.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-state-background-color);color:var(--mat-datepicker-calendar-date-selected-state-text-color)}.mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-disabled-state-background-color)}.mat-calendar-body-selected.mat-calendar-body-today{box-shadow:inset 0 0 0 1px var(--mat-datepicker-calendar-date-today-selected-state-outline-color)}.mat-calendar-body-in-range::before{background:var(--mat-datepicker-calendar-date-in-range-state-background-color)}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color)}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color)}.mat-calendar-body-comparison-bridge-start::before,[dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, var(--mat-datepicker-calendar-date-in-range-state-background-color) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color) 50%)}.mat-calendar-body-comparison-bridge-end::before,[dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, var(--mat-datepicker-calendar-date-in-range-state-background-color) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color) 50%)}.mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:var(--mat-datepicker-calendar-date-in-overlap-range-state-background-color)}.mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:var(--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color)}.cdk-high-contrast-active .mat-datepicker-popup:not(:empty),.cdk-high-contrast-active .mat-calendar-body-cell:not(.mat-calendar-body-in-range) .mat-calendar-body-selected{outline:solid 1px}.cdk-high-contrast-active .mat-calendar-body-today{outline:dotted 1px}.cdk-high-contrast-active .mat-calendar-body-cell::before,.cdk-high-contrast-active .mat-calendar-body-cell::after,.cdk-high-contrast-active .mat-calendar-body-selected{background:none}.cdk-high-contrast-active .mat-calendar-body-in-range::before,.cdk-high-contrast-active .mat-calendar-body-comparison-bridge-start::before,.cdk-high-contrast-active .mat-calendar-body-comparison-bridge-end::before{border-top:solid 1px;border-bottom:solid 1px}.cdk-high-contrast-active .mat-calendar-body-range-start::before{border-left:solid 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-range-start::before{border-left:0;border-right:solid 1px}.cdk-high-contrast-active .mat-calendar-body-range-end::before{border-right:solid 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-range-end::before{border-right:0;border-left:solid 1px}.cdk-high-contrast-active .mat-calendar-body-in-comparison-range::before{border-top:dashed 1px;border-bottom:dashed 1px}.cdk-high-contrast-active .mat-calendar-body-comparison-start::before{border-left:dashed 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-comparison-start::before{border-left:0;border-right:dashed 1px}.cdk-high-contrast-active .mat-calendar-body-comparison-end::before{border-right:dashed 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-comparison-end::before{border-right:0;border-left:dashed 1px}'],encapsulation:2,changeDetection:0})}}return i})();function Sa(i){return"TD"===i?.nodeName}function Aa(i){let n;return Sa(i)?n=i:Sa(i.parentNode)?n=i.parentNode:Sa(i.parentNode?.parentNode)&&(n=i.parentNode.parentNode),null!=n?.getAttribute("data-mat-row")?n:null}function Ra(i,n,t){return null!==t&&n!==t&&i<t&&i===n}function Ea(i,n,t){return null!==n&&n!==t&&i>=n&&i===t}function Ta(i,n,t,e){return e&&null!==n&&null!==t&&n!==t&&i>=n&&i<=t}function wr(i){const n=i.changedTouches[0];return document.elementFromPoint(n.clientX,n.clientY)}class Z{constructor(n,t){this.start=n,this.end=t}}let Bt=(()=>{class i{constructor(t,e){this.selection=t,this._adapter=e,this._selectionChanged=new Qt.B,this.selectionChanged=this._selectionChanged,this.selection=t}updateSelection(t,e){const a=this.selection;this.selection=t,this._selectionChanged.next({selection:t,source:e,oldValue:a})}ngOnDestroy(){this._selectionChanged.complete()}_isValidDateInstance(t){return this._adapter.isDateInstance(t)&&this._adapter.isValid(t)}static{this.\u0275fac=function(e){c.QTQ()}}static{this.\u0275prov=c.jDH({token:i,factory:i.\u0275fac})}}return i})(),uf=(()=>{class i extends Bt{constructor(t){super(null,t)}add(t){super.updateSelection(t,this)}isValid(){return null!=this.selection&&this._isValidDateInstance(this.selection)}isComplete(){return null!=this.selection}clone(){const t=new i(this._adapter);return t.updateSelection(this.selection,this),t}static{this.\u0275fac=function(e){return new(e||i)(c.KVO(E.MJ))}}static{this.\u0275prov=c.jDH({token:i,factory:i.\u0275fac})}}return i})(),ff=(()=>{class i extends Bt{constructor(t){super(new Z(null,null),t)}add(t){let{start:e,end:a}=this.selection;null==e?e=t:null==a?a=t:(e=t,a=null),super.updateSelection(new Z(e,a),this)}isValid(){const{start:t,end:e}=this.selection;return null==t&&null==e||(null!=t&&null!=e?this._isValidDateInstance(t)&&this._isValidDateInstance(e)&&this._adapter.compareDate(t,e)<=0:(null==t||this._isValidDateInstance(t))&&(null==e||this._isValidDateInstance(e)))}isComplete(){return null!=this.selection.start&&null!=this.selection.end}clone(){const t=new i(this._adapter);return t.updateSelection(this.selection,this),t}static{this.\u0275fac=function(e){return new(e||i)(c.KVO(E.MJ))}}static{this.\u0275prov=c.jDH({token:i,factory:i.\u0275fac})}}return i})();const gf={provide:Bt,deps:[[new c.Xx1,new c.kdw,Bt],E.MJ],useFactory:function pf(i,n){return i||new uf(n)}},_f={provide:Bt,deps:[[new c.Xx1,new c.kdw,Bt],E.MJ],useFactory:function mf(i,n){return i||new ff(n)}},wi=new c.nKC("MAT_DATE_RANGE_SELECTION_STRATEGY");let bf=(()=>{class i{constructor(t){this._dateAdapter=t}selectionFinished(t,e){let{start:a,end:s}=e;return null==a?a=t:null==s&&t&&this._dateAdapter.compareDate(t,a)>=0?s=t:(a=t,s=null),new Z(a,s)}createPreview(t,e){let a=null,s=null;return e.start&&!e.end&&t&&(a=e.start,s=t),new Z(a,s)}createDrag(t,e,a){let s=e.start,r=e.end;if(!s||!r)return null;const o=this._dateAdapter,l=0!==o.compareDate(s,r),d=o.getYear(a)-o.getYear(t),h=o.getMonth(a)-o.getMonth(t),u=o.getDate(a)-o.getDate(t);return l&&o.sameDate(t,e.start)?(s=a,o.compareDate(a,r)>0&&(r=o.addCalendarYears(r,d),r=o.addCalendarMonths(r,h),r=o.addCalendarDays(r,u))):l&&o.sameDate(t,e.end)?(r=a,o.compareDate(a,s)<0&&(s=o.addCalendarYears(s,d),s=o.addCalendarMonths(s,h),s=o.addCalendarDays(s,u))):(s=o.addCalendarYears(s,d),s=o.addCalendarMonths(s,h),s=o.addCalendarDays(s,u),r=o.addCalendarYears(r,d),r=o.addCalendarMonths(r,h),r=o.addCalendarDays(r,u)),new Z(s,r)}static{this.\u0275fac=function(e){return new(e||i)(c.KVO(E.MJ))}}static{this.\u0275prov=c.jDH({token:i,factory:i.\u0275fac})}}return i})();const yf={provide:wi,deps:[[new c.Xx1,new c.kdw,wi],E.MJ],useFactory:function vf(i,n){return i||new bf(n)}};let Sr=(()=>{class i{get activeDate(){return this._activeDate}set activeDate(t){const e=this._activeDate,a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(a,this.minDate,this.maxDate),this._hasSameMonthAndYear(e,this._activeDate)||this._init()}get selected(){return this._selected}set selected(t){this._selected=t instanceof Z?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t)),this._setRanges(this._selected)}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}constructor(t,e,a,s,r){this._changeDetectorRef=t,this._dateFormats=e,this._dateAdapter=a,this._dir=s,this._rangeStrategy=r,this._rerenderSubscription=bt.yU.EMPTY,this.activeDrag=null,this.selectedChange=new c.bkB,this._userSelection=new c.bkB,this.dragStarted=new c.bkB,this.dragEnded=new c.bkB,this.activeDateChange=new c.bkB,this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe((0,Ca.Z)(null)).subscribe(()=>this._init())}ngOnChanges(t){const e=t.comparisonStart||t.comparisonEnd;e&&!e.firstChange&&this._setRanges(this.selected),t.activeDrag&&!this.activeDrag&&this._clearPreview()}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_dateSelected(t){const e=t.value,a=this._getDateFromDayOfMonth(e);let s,r;this._selected instanceof Z?(s=this._getDateInCurrentMonth(this._selected.start),r=this._getDateInCurrentMonth(this._selected.end)):s=r=this._getDateInCurrentMonth(this._selected),(s!==e||r!==e)&&this.selectedChange.emit(a),this._userSelection.emit({value:a,event:t.event}),this._clearPreview(),this._changeDetectorRef.markForCheck()}_updateActiveDate(t){const a=this._activeDate;this.activeDate=this._getDateFromDayOfMonth(t.value),this._dateAdapter.compareDate(a,this.activeDate)&&this.activeDateChange.emit(this._activeDate)}_handleCalendarBodyKeydown(t){const e=this._activeDate,a=this._isRtl();switch(t.keyCode){case S.UQ:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,a?1:-1);break;case S.LE:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,a?-1:1);break;case S.i7:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,-7);break;case S.n6:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,7);break;case S.yZ:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,1-this._dateAdapter.getDate(this._activeDate));break;case S.Kp:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,this._dateAdapter.getNumDaysInMonth(this._activeDate)-this._dateAdapter.getDate(this._activeDate));break;case S.w_:this.activeDate=t.altKey?this._dateAdapter.addCalendarYears(this._activeDate,-1):this._dateAdapter.addCalendarMonths(this._activeDate,-1);break;case S.dB:this.activeDate=t.altKey?this._dateAdapter.addCalendarYears(this._activeDate,1):this._dateAdapter.addCalendarMonths(this._activeDate,1);break;case S.Fm:case S.t6:return this._selectionKeyPressed=!0,void(this._canSelect(this._activeDate)&&t.preventDefault());case S._f:return void(null!=this._previewEnd&&!(0,S.rp)(t)&&(this._clearPreview(),this.activeDrag?this.dragEnded.emit({value:null,event:t}):(this.selectedChange.emit(null),this._userSelection.emit({value:null,event:t})),t.preventDefault(),t.stopPropagation()));default:return}this._dateAdapter.compareDate(e,this.activeDate)&&(this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked()),t.preventDefault()}_handleCalendarBodyKeyup(t){(t.keyCode===S.t6||t.keyCode===S.Fm)&&(this._selectionKeyPressed&&this._canSelect(this._activeDate)&&this._dateSelected({value:this._dateAdapter.getDate(this._activeDate),event:t}),this._selectionKeyPressed=!1)}_init(){this._setRanges(this.selected),this._todayDate=this._getCellCompareValue(this._dateAdapter.today()),this._monthLabel=this._dateFormats.display.monthLabel?this._dateAdapter.format(this.activeDate,this._dateFormats.display.monthLabel):this._dateAdapter.getMonthNames("short")[this._dateAdapter.getMonth(this.activeDate)].toLocaleUpperCase();let t=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),1);this._firstWeekOffset=(7+this._dateAdapter.getDayOfWeek(t)-this._dateAdapter.getFirstDayOfWeek())%7,this._initWeekdays(),this._createWeekCells(),this._changeDetectorRef.markForCheck()}_focusActiveCell(t){this._matCalendarBody._focusActiveCell(t)}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_previewChanged({event:t,value:e}){if(this._rangeStrategy){const a=e?e.rawValue:null,s=this._rangeStrategy.createPreview(a,this.selected,t);if(this._previewStart=this._getCellCompareValue(s.start),this._previewEnd=this._getCellCompareValue(s.end),this.activeDrag&&a){const r=this._rangeStrategy.createDrag?.(this.activeDrag.value,this.selected,a,t);r&&(this._previewStart=this._getCellCompareValue(r.start),this._previewEnd=this._getCellCompareValue(r.end))}this._changeDetectorRef.detectChanges()}}_dragEnded(t){if(this.activeDrag)if(t.value){const e=this._rangeStrategy?.createDrag?.(this.activeDrag.value,this.selected,t.value,t.event);this.dragEnded.emit({value:e??null,event:t.event})}else this.dragEnded.emit({value:null,event:t.event})}_getDateFromDayOfMonth(t){return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),t)}_initWeekdays(){const t=this._dateAdapter.getFirstDayOfWeek(),e=this._dateAdapter.getDayOfWeekNames("narrow");let s=this._dateAdapter.getDayOfWeekNames("long").map((r,o)=>({long:r,narrow:e[o]}));this._weekdays=s.slice(t).concat(s.slice(0,t))}_createWeekCells(){const t=this._dateAdapter.getNumDaysInMonth(this.activeDate),e=this._dateAdapter.getDateNames();this._weeks=[[]];for(let a=0,s=this._firstWeekOffset;a<t;a++,s++){7==s&&(this._weeks.push([]),s=0);const r=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),a+1),o=this._shouldEnableDate(r),l=this._dateAdapter.format(r,this._dateFormats.display.dateA11yLabel),d=this.dateClass?this.dateClass(r,"month"):void 0;this._weeks[this._weeks.length-1].push(new wa(a+1,e[a],l,o,d,this._getCellCompareValue(r),r))}}_shouldEnableDate(t){return!!t&&(!this.minDate||this._dateAdapter.compareDate(t,this.minDate)>=0)&&(!this.maxDate||this._dateAdapter.compareDate(t,this.maxDate)<=0)&&(!this.dateFilter||this.dateFilter(t))}_getDateInCurrentMonth(t){return t&&this._hasSameMonthAndYear(t,this.activeDate)?this._dateAdapter.getDate(t):null}_hasSameMonthAndYear(t,e){return!(!t||!e||this._dateAdapter.getMonth(t)!=this._dateAdapter.getMonth(e)||this._dateAdapter.getYear(t)!=this._dateAdapter.getYear(e))}_getCellCompareValue(t){if(t){const e=this._dateAdapter.getYear(t),a=this._dateAdapter.getMonth(t),s=this._dateAdapter.getDate(t);return new Date(e,a,s).getTime()}return null}_isRtl(){return this._dir&&"rtl"===this._dir.value}_setRanges(t){t instanceof Z?(this._rangeStart=this._getCellCompareValue(t.start),this._rangeEnd=this._getCellCompareValue(t.end),this._isRange=!0):(this._rangeStart=this._rangeEnd=this._getCellCompareValue(t),this._isRange=!1),this._comparisonRangeStart=this._getCellCompareValue(this.comparisonStart),this._comparisonRangeEnd=this._getCellCompareValue(this.comparisonEnd)}_canSelect(t){return!this.dateFilter||this.dateFilter(t)}_clearPreview(){this._previewStart=this._previewEnd=null}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.gRc),c.rXU(E.de,8),c.rXU(E.MJ,8),c.rXU(Be.dS,8),c.rXU(wi,8))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-month-view"]],viewQuery:function(e,a){if(1&e&&c.GBs(de,5),2&e){let s;c.mGM(s=c.lsd())&&(a._matCalendarBody=s.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName",activeDrag:"activeDrag"},outputs:{selectedChange:"selectedChange",_userSelection:"_userSelection",dragStarted:"dragStarted",dragEnded:"dragEnded",activeDateChange:"activeDateChange"},exportAs:["matMonthView"],standalone:!0,features:[c.OA$,c.aNF],decls:8,vars:14,consts:[["role","grid",1,"mat-calendar-table"],[1,"mat-calendar-table-header"],["scope","col"],["aria-hidden","true"],["colspan","7",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","previewChange","dragStarted","dragEnded","keyup","keydown","label","rows","todayValue","startValue","endValue","comparisonStart","comparisonEnd","previewStart","previewEnd","isRange","labelMinRequiredCells","activeCell","startDateAccessibleName","endDateAccessibleName"],[1,"cdk-visually-hidden"]],template:function(e,a){1&e&&(c.j41(0,"table",0)(1,"thead",1)(2,"tr"),c.Z7z(3,Qu,5,2,"th",2,c.fX1),c.k0s(),c.j41(5,"tr",3),c.nrm(6,"th",4),c.k0s()(),c.j41(7,"tbody",5),c.bIt("selectedValueChange",function(r){return a._dateSelected(r)})("activeDateChange",function(r){return a._updateActiveDate(r)})("previewChange",function(r){return a._previewChanged(r)})("dragStarted",function(r){return a.dragStarted.emit(r)})("dragEnded",function(r){return a._dragEnded(r)})("keyup",function(r){return a._handleCalendarBodyKeyup(r)})("keydown",function(r){return a._handleCalendarBodyKeydown(r)}),c.k0s()()),2&e&&(c.R7$(3),c.Dyx(a._weekdays),c.R7$(4),c.Y8G("label",a._monthLabel)("rows",a._weeks)("todayValue",a._todayDate)("startValue",a._rangeStart)("endValue",a._rangeEnd)("comparisonStart",a._comparisonRangeStart)("comparisonEnd",a._comparisonRangeEnd)("previewStart",a._previewStart)("previewEnd",a._previewEnd)("isRange",a._isRange)("labelMinRequiredCells",3)("activeCell",a._dateAdapter.getDate(a.activeDate)-1)("startDateAccessibleName",a.startDateAccessibleName)("endDateAccessibleName",a.endDateAccessibleName))},dependencies:[de],encapsulation:2,changeDetection:0})}}return i})();const ct=24;let Ar=(()=>{class i{get activeDate(){return this._activeDate}set activeDate(t){let e=this._activeDate;const a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(a,this.minDate,this.maxDate),Rr(this._dateAdapter,e,this._activeDate,this.minDate,this.maxDate)||this._init()}get selected(){return this._selected}set selected(t){this._selected=t instanceof Z?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t)),this._setSelectedYear(t)}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}constructor(t,e,a){this._changeDetectorRef=t,this._dateAdapter=e,this._dir=a,this._rerenderSubscription=bt.yU.EMPTY,this.selectedChange=new c.bkB,this.yearSelected=new c.bkB,this.activeDateChange=new c.bkB,this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe((0,Ca.Z)(null)).subscribe(()=>this._init())}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_init(){this._todayYear=this._dateAdapter.getYear(this._dateAdapter.today());const e=this._dateAdapter.getYear(this._activeDate)-je(this._dateAdapter,this.activeDate,this.minDate,this.maxDate);this._years=[];for(let a=0,s=[];a<ct;a++)s.push(e+a),4==s.length&&(this._years.push(s.map(r=>this._createCellForYear(r))),s=[]);this._changeDetectorRef.markForCheck()}_yearSelected(t){const e=t.value,a=this._dateAdapter.createDate(e,0,1),s=this._getDateFromYear(e);this.yearSelected.emit(a),this.selectedChange.emit(s)}_updateActiveDate(t){const a=this._activeDate;this.activeDate=this._getDateFromYear(t.value),this._dateAdapter.compareDate(a,this.activeDate)&&this.activeDateChange.emit(this.activeDate)}_handleCalendarBodyKeydown(t){const e=this._activeDate,a=this._isRtl();switch(t.keyCode){case S.UQ:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,a?1:-1);break;case S.LE:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,a?-1:1);break;case S.i7:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,-4);break;case S.n6:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,4);break;case S.yZ:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,-je(this._dateAdapter,this.activeDate,this.minDate,this.maxDate));break;case S.Kp:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,ct-je(this._dateAdapter,this.activeDate,this.minDate,this.maxDate)-1);break;case S.w_:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?10*-ct:-ct);break;case S.dB:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?10*ct:ct);break;case S.Fm:case S.t6:this._selectionKeyPressed=!0;break;default:return}this._dateAdapter.compareDate(e,this.activeDate)&&this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked(),t.preventDefault()}_handleCalendarBodyKeyup(t){(t.keyCode===S.t6||t.keyCode===S.Fm)&&(this._selectionKeyPressed&&this._yearSelected({value:this._dateAdapter.getYear(this._activeDate),event:t}),this._selectionKeyPressed=!1)}_getActiveCell(){return je(this._dateAdapter,this.activeDate,this.minDate,this.maxDate)}_focusActiveCell(){this._matCalendarBody._focusActiveCell()}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_getDateFromYear(t){const e=this._dateAdapter.getMonth(this.activeDate),a=this._dateAdapter.getNumDaysInMonth(this._dateAdapter.createDate(t,e,1));return this._dateAdapter.createDate(t,e,Math.min(this._dateAdapter.getDate(this.activeDate),a))}_createCellForYear(t){const e=this._dateAdapter.createDate(t,0,1),a=this._dateAdapter.getYearName(e),s=this.dateClass?this.dateClass(e,"multi-year"):void 0;return new wa(t,a,a,this._shouldEnableYear(t),s)}_shouldEnableYear(t){if(null==t||this.maxDate&&t>this._dateAdapter.getYear(this.maxDate)||this.minDate&&t<this._dateAdapter.getYear(this.minDate))return!1;if(!this.dateFilter)return!0;for(let a=this._dateAdapter.createDate(t,0,1);this._dateAdapter.getYear(a)==t;a=this._dateAdapter.addCalendarDays(a,1))if(this.dateFilter(a))return!0;return!1}_isRtl(){return this._dir&&"rtl"===this._dir.value}_setSelectedYear(t){if(this._selectedYear=null,t instanceof Z){const e=t.start||t.end;e&&(this._selectedYear=this._dateAdapter.getYear(e))}else t&&(this._selectedYear=this._dateAdapter.getYear(t))}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.gRc),c.rXU(E.MJ,8),c.rXU(Be.dS,8))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-multi-year-view"]],viewQuery:function(e,a){if(1&e&&c.GBs(de,5),2&e){let s;c.mGM(s=c.lsd())&&(a._matCalendarBody=s.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass"},outputs:{selectedChange:"selectedChange",yearSelected:"yearSelected",activeDateChange:"activeDateChange"},exportAs:["matMultiYearView"],standalone:!0,features:[c.aNF],decls:5,vars:7,consts:[["role","grid",1,"mat-calendar-table"],["aria-hidden","true",1,"mat-calendar-table-header"],["colspan","4",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","keyup","keydown","rows","todayValue","startValue","endValue","numCols","cellAspectRatio","activeCell"]],template:function(e,a){1&e&&(c.j41(0,"table",0)(1,"thead",1)(2,"tr"),c.nrm(3,"th",2),c.k0s()(),c.j41(4,"tbody",3),c.bIt("selectedValueChange",function(r){return a._yearSelected(r)})("activeDateChange",function(r){return a._updateActiveDate(r)})("keyup",function(r){return a._handleCalendarBodyKeyup(r)})("keydown",function(r){return a._handleCalendarBodyKeydown(r)}),c.k0s()()),2&e&&(c.R7$(4),c.Y8G("rows",a._years)("todayValue",a._todayYear)("startValue",a._selectedYear)("endValue",a._selectedYear)("numCols",4)("cellAspectRatio",4/7)("activeCell",a._getActiveCell()))},dependencies:[de],encapsulation:2,changeDetection:0})}}return i})();function Rr(i,n,t,e,a){const s=i.getYear(n),r=i.getYear(t),o=Er(i,e,a);return Math.floor((s-o)/ct)===Math.floor((r-o)/ct)}function je(i,n,t,e){return function xf(i,n){return(i%n+n)%n}(i.getYear(n)-Er(i,t,e),ct)}function Er(i,n,t){let e=0;return t?e=i.getYear(t)-ct+1:n&&(e=i.getYear(n)),e}let Tr=(()=>{class i{get activeDate(){return this._activeDate}set activeDate(t){let e=this._activeDate;const a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(a,this.minDate,this.maxDate),this._dateAdapter.getYear(e)!==this._dateAdapter.getYear(this._activeDate)&&this._init()}get selected(){return this._selected}set selected(t){this._selected=t instanceof Z?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t)),this._setSelectedMonth(t)}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}constructor(t,e,a,s){this._changeDetectorRef=t,this._dateFormats=e,this._dateAdapter=a,this._dir=s,this._rerenderSubscription=bt.yU.EMPTY,this.selectedChange=new c.bkB,this.monthSelected=new c.bkB,this.activeDateChange=new c.bkB,this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe((0,Ca.Z)(null)).subscribe(()=>this._init())}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_monthSelected(t){const e=t.value,a=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),e,1);this.monthSelected.emit(a);const s=this._getDateFromMonth(e);this.selectedChange.emit(s)}_updateActiveDate(t){const a=this._activeDate;this.activeDate=this._getDateFromMonth(t.value),this._dateAdapter.compareDate(a,this.activeDate)&&this.activeDateChange.emit(this.activeDate)}_handleCalendarBodyKeydown(t){const e=this._activeDate,a=this._isRtl();switch(t.keyCode){case S.UQ:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,a?1:-1);break;case S.LE:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,a?-1:1);break;case S.i7:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,-4);break;case S.n6:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,4);break;case S.yZ:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,-this._dateAdapter.getMonth(this._activeDate));break;case S.Kp:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,11-this._dateAdapter.getMonth(this._activeDate));break;case S.w_:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?-10:-1);break;case S.dB:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?10:1);break;case S.Fm:case S.t6:this._selectionKeyPressed=!0;break;default:return}this._dateAdapter.compareDate(e,this.activeDate)&&(this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked()),t.preventDefault()}_handleCalendarBodyKeyup(t){(t.keyCode===S.t6||t.keyCode===S.Fm)&&(this._selectionKeyPressed&&this._monthSelected({value:this._dateAdapter.getMonth(this._activeDate),event:t}),this._selectionKeyPressed=!1)}_init(){this._setSelectedMonth(this.selected),this._todayMonth=this._getMonthInCurrentYear(this._dateAdapter.today()),this._yearLabel=this._dateAdapter.getYearName(this.activeDate);let t=this._dateAdapter.getMonthNames("short");this._months=[[0,1,2,3],[4,5,6,7],[8,9,10,11]].map(e=>e.map(a=>this._createCellForMonth(a,t[a]))),this._changeDetectorRef.markForCheck()}_focusActiveCell(){this._matCalendarBody._focusActiveCell()}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_getMonthInCurrentYear(t){return t&&this._dateAdapter.getYear(t)==this._dateAdapter.getYear(this.activeDate)?this._dateAdapter.getMonth(t):null}_getDateFromMonth(t){const e=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),t,1),a=this._dateAdapter.getNumDaysInMonth(e);return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),t,Math.min(this._dateAdapter.getDate(this.activeDate),a))}_createCellForMonth(t,e){const a=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),t,1),s=this._dateAdapter.format(a,this._dateFormats.display.monthYearA11yLabel),r=this.dateClass?this.dateClass(a,"year"):void 0;return new wa(t,e.toLocaleUpperCase(),s,this._shouldEnableMonth(t),r)}_shouldEnableMonth(t){const e=this._dateAdapter.getYear(this.activeDate);if(null==t||this._isYearAndMonthAfterMaxDate(e,t)||this._isYearAndMonthBeforeMinDate(e,t))return!1;if(!this.dateFilter)return!0;for(let s=this._dateAdapter.createDate(e,t,1);this._dateAdapter.getMonth(s)==t;s=this._dateAdapter.addCalendarDays(s,1))if(this.dateFilter(s))return!0;return!1}_isYearAndMonthAfterMaxDate(t,e){if(this.maxDate){const a=this._dateAdapter.getYear(this.maxDate),s=this._dateAdapter.getMonth(this.maxDate);return t>a||t===a&&e>s}return!1}_isYearAndMonthBeforeMinDate(t,e){if(this.minDate){const a=this._dateAdapter.getYear(this.minDate),s=this._dateAdapter.getMonth(this.minDate);return t<a||t===a&&e<s}return!1}_isRtl(){return this._dir&&"rtl"===this._dir.value}_setSelectedMonth(t){this._selectedMonth=t instanceof Z?this._getMonthInCurrentYear(t.start)||this._getMonthInCurrentYear(t.end):this._getMonthInCurrentYear(t)}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.gRc),c.rXU(E.de,8),c.rXU(E.MJ,8),c.rXU(Be.dS,8))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-year-view"]],viewQuery:function(e,a){if(1&e&&c.GBs(de,5),2&e){let s;c.mGM(s=c.lsd())&&(a._matCalendarBody=s.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass"},outputs:{selectedChange:"selectedChange",monthSelected:"monthSelected",activeDateChange:"activeDateChange"},exportAs:["matYearView"],standalone:!0,features:[c.aNF],decls:5,vars:9,consts:[["role","grid",1,"mat-calendar-table"],["aria-hidden","true",1,"mat-calendar-table-header"],["colspan","4",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","keyup","keydown","label","rows","todayValue","startValue","endValue","labelMinRequiredCells","numCols","cellAspectRatio","activeCell"]],template:function(e,a){1&e&&(c.j41(0,"table",0)(1,"thead",1)(2,"tr"),c.nrm(3,"th",2),c.k0s()(),c.j41(4,"tbody",3),c.bIt("selectedValueChange",function(r){return a._monthSelected(r)})("activeDateChange",function(r){return a._updateActiveDate(r)})("keyup",function(r){return a._handleCalendarBodyKeyup(r)})("keydown",function(r){return a._handleCalendarBodyKeydown(r)}),c.k0s()()),2&e&&(c.R7$(4),c.Y8G("label",a._yearLabel)("rows",a._months)("todayValue",a._todayMonth)("startValue",a._selectedMonth)("endValue",a._selectedMonth)("labelMinRequiredCells",2)("numCols",4)("cellAspectRatio",4/7)("activeCell",a._dateAdapter.getMonth(a.activeDate)))},dependencies:[de],encapsulation:2,changeDetection:0})}}return i})(),Df=1,Ir=(()=>{class i{constructor(t,e,a,s,r){this._intl=t,this.calendar=e,this._dateAdapter=a,this._dateFormats=s,this._id="mat-calendar-header-"+Df++,this._periodButtonLabelId=`${this._id}-period-label`,this.calendar.stateChanges.subscribe(()=>r.markForCheck())}get periodButtonText(){return"month"==this.calendar.currentView?this._dateAdapter.format(this.calendar.activeDate,this._dateFormats.display.monthYearLabel).toLocaleUpperCase():"year"==this.calendar.currentView?this._dateAdapter.getYearName(this.calendar.activeDate):this._intl.formatYearRange(...this._formatMinAndMaxYearLabels())}get periodButtonDescription(){return"month"==this.calendar.currentView?this._dateAdapter.format(this.calendar.activeDate,this._dateFormats.display.monthYearLabel).toLocaleUpperCase():"year"==this.calendar.currentView?this._dateAdapter.getYearName(this.calendar.activeDate):this._intl.formatYearRangeLabel(...this._formatMinAndMaxYearLabels())}get periodButtonLabel(){return"month"==this.calendar.currentView?this._intl.switchToMultiYearViewLabel:this._intl.switchToMonthViewLabel}get prevButtonLabel(){return{month:this._intl.prevMonthLabel,year:this._intl.prevYearLabel,"multi-year":this._intl.prevMultiYearLabel}[this.calendar.currentView]}get nextButtonLabel(){return{month:this._intl.nextMonthLabel,year:this._intl.nextYearLabel,"multi-year":this._intl.nextMultiYearLabel}[this.calendar.currentView]}currentPeriodClicked(){this.calendar.currentView="month"==this.calendar.currentView?"multi-year":"month"}previousClicked(){this.calendar.activeDate="month"==this.calendar.currentView?this._dateAdapter.addCalendarMonths(this.calendar.activeDate,-1):this._dateAdapter.addCalendarYears(this.calendar.activeDate,"year"==this.calendar.currentView?-1:-ct)}nextClicked(){this.calendar.activeDate="month"==this.calendar.currentView?this._dateAdapter.addCalendarMonths(this.calendar.activeDate,1):this._dateAdapter.addCalendarYears(this.calendar.activeDate,"year"==this.calendar.currentView?1:ct)}previousEnabled(){return!this.calendar.minDate||!this.calendar.minDate||!this._isSameView(this.calendar.activeDate,this.calendar.minDate)}nextEnabled(){return!this.calendar.maxDate||!this._isSameView(this.calendar.activeDate,this.calendar.maxDate)}_isSameView(t,e){return"month"==this.calendar.currentView?this._dateAdapter.getYear(t)==this._dateAdapter.getYear(e)&&this._dateAdapter.getMonth(t)==this._dateAdapter.getMonth(e):"year"==this.calendar.currentView?this._dateAdapter.getYear(t)==this._dateAdapter.getYear(e):Rr(this._dateAdapter,t,e,this.calendar.minDate,this.calendar.maxDate)}_formatMinAndMaxYearLabels(){const e=this._dateAdapter.getYear(this.calendar.activeDate)-je(this._dateAdapter,this.calendar.activeDate,this.calendar.minDate,this.calendar.maxDate),a=e+ct-1;return[this._dateAdapter.getYearName(this._dateAdapter.createDate(e,0,1)),this._dateAdapter.getYearName(this._dateAdapter.createDate(a,0,1))]}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(Ne),c.rXU((0,c.Rfq)(()=>Fa)),c.rXU(E.MJ,8),c.rXU(E.de,8),c.rXU(c.gRc))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-calendar-header"]],exportAs:["matCalendarHeader"],standalone:!0,features:[c.aNF],ngContentSelectors:Ju,decls:13,vars:11,consts:[[1,"mat-calendar-header"],[1,"mat-calendar-controls"],[1,"cdk-visually-hidden",3,"id"],["mat-button","","type","button","aria-live","polite",1,"mat-calendar-period-button",3,"click"],["aria-hidden","true"],["viewBox","0 0 10 5","focusable","false","aria-hidden","true",1,"mat-calendar-arrow"],["points","0,0 5,5 10,0"],[1,"mat-calendar-spacer"],["mat-icon-button","","type","button",1,"mat-calendar-previous-button",3,"click","disabled"],["mat-icon-button","","type","button",1,"mat-calendar-next-button",3,"click","disabled"]],template:function(e,a){1&e&&(c.NAR(),c.j41(0,"div",0)(1,"div",1)(2,"label",2),c.EFF(3),c.k0s(),c.j41(4,"button",3),c.bIt("click",function(){return a.currentPeriodClicked()}),c.j41(5,"span",4),c.EFF(6),c.k0s(),c.qSk(),c.j41(7,"svg",5),c.nrm(8,"polygon",6),c.k0s()(),c.joV(),c.nrm(9,"div",7),c.SdG(10),c.j41(11,"button",8),c.bIt("click",function(){return a.previousClicked()}),c.k0s(),c.j41(12,"button",9),c.bIt("click",function(){return a.nextClicked()}),c.k0s()()()),2&e&&(c.R7$(2),c.Y8G("id",a._periodButtonLabelId),c.R7$(),c.JRh(a.periodButtonDescription),c.R7$(),c.BMQ("aria-label",a.periodButtonLabel)("aria-describedby",a._periodButtonLabelId),c.R7$(2),c.JRh(a.periodButtonText),c.R7$(),c.AVh("mat-calendar-invert","month"!==a.calendar.currentView),c.R7$(4),c.Y8G("disabled",!a.previousEnabled()),c.BMQ("aria-label",a.prevButtonLabel),c.R7$(),c.Y8G("disabled",!a.nextEnabled()),c.BMQ("aria-label",a.nextButtonLabel))},dependencies:[qt.$z,qt.iY],encapsulation:2,changeDetection:0})}}return i})(),Fa=(()=>{class i{get startAt(){return this._startAt}set startAt(t){this._startAt=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get selected(){return this._selected}set selected(t){this._selected=t instanceof Z?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get activeDate(){return this._clampedActiveDate}set activeDate(t){this._clampedActiveDate=this._dateAdapter.clampDate(t,this.minDate,this.maxDate),this.stateChanges.next(),this._changeDetectorRef.markForCheck()}get currentView(){return this._currentView}set currentView(t){const e=this._currentView!==t?t:null;this._currentView=t,this._moveFocusOnNextTick=!0,this._changeDetectorRef.markForCheck(),e&&this.viewChanged.emit(e)}constructor(t,e,a,s){this._dateAdapter=e,this._dateFormats=a,this._changeDetectorRef=s,this._moveFocusOnNextTick=!1,this.startView="month",this.selectedChange=new c.bkB,this.yearSelected=new c.bkB,this.monthSelected=new c.bkB,this.viewChanged=new c.bkB(!0),this._userSelection=new c.bkB,this._userDragDrop=new c.bkB,this._activeDrag=null,this.stateChanges=new Qt.B,this._intlChanges=t.changes.subscribe(()=>{s.markForCheck(),this.stateChanges.next()})}ngAfterContentInit(){this._calendarHeaderPortal=new Le.A8(this.headerComponent||Ir),this.activeDate=this.startAt||this._dateAdapter.today(),this._currentView=this.startView}ngAfterViewChecked(){this._moveFocusOnNextTick&&(this._moveFocusOnNextTick=!1,this.focusActiveCell())}ngOnDestroy(){this._intlChanges.unsubscribe(),this.stateChanges.complete()}ngOnChanges(t){const e=t.minDate&&!this._dateAdapter.sameDate(t.minDate.previousValue,t.minDate.currentValue)?t.minDate:void 0,a=t.maxDate&&!this._dateAdapter.sameDate(t.maxDate.previousValue,t.maxDate.currentValue)?t.maxDate:void 0,s=e||a||t.dateFilter;if(s&&!s.firstChange){const r=this._getCurrentViewComponent();r&&(this._changeDetectorRef.detectChanges(),r._init())}this.stateChanges.next()}focusActiveCell(){this._getCurrentViewComponent()._focusActiveCell(!1)}updateTodaysDate(){this._getCurrentViewComponent()._init()}_dateSelected(t){const e=t.value;(this.selected instanceof Z||e&&!this._dateAdapter.sameDate(e,this.selected))&&this.selectedChange.emit(e),this._userSelection.emit(t)}_yearSelectedInMultiYearView(t){this.yearSelected.emit(t)}_monthSelectedInYearView(t){this.monthSelected.emit(t)}_goToDateInView(t,e){this.activeDate=t,this.currentView=e}_dragStarted(t){this._activeDrag=t}_dragEnded(t){this._activeDrag&&(t.value&&this._userDragDrop.emit(t),this._activeDrag=null)}_getCurrentViewComponent(){return this.monthView||this.yearView||this.multiYearView}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(Ne),c.rXU(E.MJ,8),c.rXU(E.de,8),c.rXU(c.gRc))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-calendar"]],viewQuery:function(e,a){if(1&e&&(c.GBs(Sr,5),c.GBs(Tr,5),c.GBs(Ar,5)),2&e){let s;c.mGM(s=c.lsd())&&(a.monthView=s.first),c.mGM(s=c.lsd())&&(a.yearView=s.first),c.mGM(s=c.lsd())&&(a.multiYearView=s.first)}},hostAttrs:[1,"mat-calendar"],inputs:{headerComponent:"headerComponent",startAt:"startAt",startView:"startView",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName"},outputs:{selectedChange:"selectedChange",yearSelected:"yearSelected",monthSelected:"monthSelected",viewChanged:"viewChanged",_userSelection:"_userSelection",_userDragDrop:"_userDragDrop"},exportAs:["matCalendar"],standalone:!0,features:[c.Jv_([gf]),c.OA$,c.aNF],decls:5,vars:2,consts:[[3,"cdkPortalOutlet"],["cdkMonitorSubtreeFocus","","tabindex","-1",1,"mat-calendar-content"],[3,"activeDate","selected","dateFilter","maxDate","minDate","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName","activeDrag"],[3,"activeDate","selected","dateFilter","maxDate","minDate","dateClass"],[3,"activeDateChange","_userSelection","dragStarted","dragEnded","activeDate","selected","dateFilter","maxDate","minDate","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName","activeDrag"],[3,"activeDateChange","monthSelected","selectedChange","activeDate","selected","dateFilter","maxDate","minDate","dateClass"],[3,"activeDateChange","yearSelected","selectedChange","activeDate","selected","dateFilter","maxDate","minDate","dateClass"]],template:function(e,a){if(1&e&&(c.DNE(0,Zu,0,0,"ng-template",0),c.j41(1,"div",1),c.DNE(2,tf,1,11,"mat-month-view",2)(3,ef,1,6,"mat-year-view",3)(4,af,1,6,"mat-multi-year-view",3),c.k0s()),2&e){let s;c.Y8G("cdkPortalOutlet",a._calendarHeaderPortal),c.R7$(2),c.vxM("month"===(s=a.currentView)?2:"year"===s?3:"multi-year"===s?4:-1)}},dependencies:[Le.I3,Ve.vR,Sr,Tr,Ar],styles:['.mat-calendar{display:block;font-family:var(--mat-datepicker-calendar-text-font);font-size:var(--mat-datepicker-calendar-text-size)}.mat-calendar-header{padding:8px 8px 0 8px}.mat-calendar-content{padding:0 8px 8px 8px;outline:none}.mat-calendar-controls{display:flex;align-items:center;margin:5% calc(4.7142857143% - 16px)}.mat-calendar-spacer{flex:1 1 auto}.mat-calendar-period-button{min-width:0;margin:0 8px;font-size:var(--mat-datepicker-calendar-period-button-text-size);font-weight:var(--mat-datepicker-calendar-period-button-text-weight);--mdc-text-button-label-text-color:var(--mat-datepicker-calendar-period-button-text-color)}.mat-calendar-arrow{display:inline-block;width:10px;height:5px;margin:0 0 0 5px;vertical-align:middle;fill:var(--mat-datepicker-calendar-period-button-icon-color)}.mat-calendar-arrow.mat-calendar-invert{transform:rotate(180deg)}[dir=rtl] .mat-calendar-arrow{margin:0 5px 0 0}.cdk-high-contrast-active .mat-calendar-arrow{fill:CanvasText}.mat-calendar-previous-button,.mat-calendar-next-button{position:relative}.mat-datepicker-content .mat-calendar-previous-button:not(.mat-mdc-button-disabled),.mat-datepicker-content .mat-calendar-next-button:not(.mat-mdc-button-disabled){color:var(--mat-datepicker-calendar-navigation-button-icon-color)}.mat-calendar-previous-button::after,.mat-calendar-next-button::after{top:0;left:0;right:0;bottom:0;position:absolute;content:"";margin:15.5px;border:0 solid currentColor;border-top-width:2px}[dir=rtl] .mat-calendar-previous-button,[dir=rtl] .mat-calendar-next-button{transform:rotate(180deg)}.mat-calendar-previous-button::after{border-left-width:2px;transform:translateX(2px) rotate(-45deg)}.mat-calendar-next-button::after{border-right-width:2px;transform:translateX(-2px) rotate(45deg)}.mat-calendar-table{border-spacing:0;border-collapse:collapse;width:100%}.mat-calendar-table-header th{text-align:center;padding:0 0 8px 0;color:var(--mat-datepicker-calendar-header-text-color);font-size:var(--mat-datepicker-calendar-header-text-size);font-weight:var(--mat-datepicker-calendar-header-text-weight)}.mat-calendar-table-header-divider{position:relative;height:1px}.mat-calendar-table-header-divider::after{content:"";position:absolute;top:0;left:-8px;right:-8px;height:1px;background:var(--mat-datepicker-calendar-header-divider-color)}.mat-calendar-body-cell-content::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}.mat-calendar-body-cell:focus .mat-focus-indicator::before{content:""}'],encapsulation:2,changeDetection:0})}}return i})();const Pr={transformPanel:(0,N.hZ)("transformPanel",[(0,N.kY)("void => enter-dropdown",(0,N.i0)("120ms cubic-bezier(0, 0, 0.2, 1)",(0,N.i7)([(0,N.iF)({opacity:0,transform:"scale(1, 0.8)"}),(0,N.iF)({opacity:1,transform:"scale(1, 1)"})]))),(0,N.kY)("void => enter-dialog",(0,N.i0)("150ms cubic-bezier(0, 0, 0.2, 1)",(0,N.i7)([(0,N.iF)({opacity:0,transform:"scale(0.7)"}),(0,N.iF)({transform:"none",opacity:1})]))),(0,N.kY)("* => void",(0,N.i0)("100ms linear",(0,N.iF)({opacity:0})))]),fadeInCalendar:(0,N.hZ)("fadeInCalendar",[(0,N.wk)("void",(0,N.iF)({opacity:0})),(0,N.wk)("enter",(0,N.iF)({opacity:1})),(0,N.kY)("void => *",(0,N.i0)("120ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)"))])};let kf=0;const Fr=new c.nKC("mat-datepicker-scroll-strategy",{providedIn:"root",factory:()=>{const i=(0,c.WQX)(ce.hJ);return()=>i.scrollStrategies.reposition()}}),Cf={provide:Fr,deps:[ce.hJ],useFactory:function Mf(i){return()=>i.scrollStrategies.reposition()}};let Or=(()=>{class i{constructor(t,e,a,s,r,o){this._elementRef=t,this._changeDetectorRef=e,this._globalModel=a,this._dateAdapter=s,this._rangeSelectionStrategy=r,this._subscriptions=new bt.yU,this._animationDone=new Qt.B,this._isAnimating=!1,this._actionsPortal=null,this._closeButtonText=o.closeCalendarLabel}ngOnInit(){this._animationState=this.datepicker.touchUi?"enter-dialog":"enter-dropdown"}ngAfterViewInit(){this._subscriptions.add(this.datepicker.stateChanges.subscribe(()=>{this._changeDetectorRef.markForCheck()})),this._calendar.focusActiveCell()}ngOnDestroy(){this._subscriptions.unsubscribe(),this._animationDone.complete()}_handleUserSelection(t){const e=this._model.selection,a=t.value,s=e instanceof Z;if(s&&this._rangeSelectionStrategy){const r=this._rangeSelectionStrategy.selectionFinished(a,e,t.event);this._model.updateSelection(r,this)}else a&&(s||!this._dateAdapter.sameDate(a,e))&&this._model.add(a);(!this._model||this._model.isComplete())&&!this._actionsPortal&&this.datepicker.close()}_handleUserDragDrop(t){this._model.updateSelection(t.value,this)}_startExitAnimation(){this._animationState="void",this._changeDetectorRef.markForCheck()}_handleAnimationEvent(t){this._isAnimating="start"===t.phaseName,this._isAnimating||this._animationDone.next()}_getSelected(){return this._model.selection}_applyPendingSelection(){this._model!==this._globalModel&&this._globalModel.updateSelection(this._model.selection,this)}_assignActions(t,e){this._model=t?this._globalModel.clone():this._globalModel,this._actionsPortal=t,e&&this._changeDetectorRef.detectChanges()}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.aKT),c.rXU(c.gRc),c.rXU(Bt),c.rXU(E.MJ),c.rXU(wi,8),c.rXU(Ne))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-datepicker-content"]],viewQuery:function(e,a){if(1&e&&c.GBs(Fa,5),2&e){let s;c.mGM(s=c.lsd())&&(a._calendar=s.first)}},hostAttrs:[1,"mat-datepicker-content"],hostVars:5,hostBindings:function(e,a){1&e&&c.Kam("@transformPanel.start",function(r){return a._handleAnimationEvent(r)})("@transformPanel.done",function(r){return a._handleAnimationEvent(r)}),2&e&&(c.zvX("@transformPanel",a._animationState),c.HbH(a.color?"mat-"+a.color:""),c.AVh("mat-datepicker-content-touch",a.datepicker.touchUi))},inputs:{color:"color"},exportAs:["matDatepickerContent"],standalone:!0,features:[c.aNF],decls:5,vars:27,consts:[["cdkTrapFocus","","role","dialog",1,"mat-datepicker-content-container"],[3,"yearSelected","monthSelected","viewChanged","_userSelection","_userDragDrop","id","startAt","startView","minDate","maxDate","dateFilter","headerComponent","selected","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName"],[3,"cdkPortalOutlet"],["type","button","mat-raised-button","",1,"mat-datepicker-close-button",3,"focus","blur","click","color"]],template:function(e,a){if(1&e&&(c.j41(0,"div",0)(1,"mat-calendar",1),c.bIt("yearSelected",function(r){return a.datepicker._selectYear(r)})("monthSelected",function(r){return a.datepicker._selectMonth(r)})("viewChanged",function(r){return a.datepicker._viewChanged(r)})("_userSelection",function(r){return a._handleUserSelection(r)})("_userDragDrop",function(r){return a._handleUserDragDrop(r)}),c.k0s(),c.DNE(2,nf,0,0,"ng-template",2),c.j41(3,"button",3),c.bIt("focus",function(){return a._closeButtonFocused=!0})("blur",function(){return a._closeButtonFocused=!1})("click",function(){return a.datepicker.close()}),c.EFF(4),c.k0s()()),2&e){let s;c.AVh("mat-datepicker-content-container-with-custom-header",a.datepicker.calendarHeaderComponent)("mat-datepicker-content-container-with-actions",a._actionsPortal),c.BMQ("aria-modal",!0)("aria-labelledby",null!==(s=a._dialogLabelId)&&void 0!==s?s:void 0),c.R7$(),c.HbH(a.datepicker.panelClass),c.Y8G("id",a.datepicker.id)("startAt",a.datepicker.startAt)("startView",a.datepicker.startView)("minDate",a.datepicker._getMinDate())("maxDate",a.datepicker._getMaxDate())("dateFilter",a.datepicker._getDateFilter())("headerComponent",a.datepicker.calendarHeaderComponent)("selected",a._getSelected())("dateClass",a.datepicker.dateClass)("comparisonStart",a.comparisonStart)("comparisonEnd",a.comparisonEnd)("@fadeInCalendar","enter")("startDateAccessibleName",a.startDateAccessibleName)("endDateAccessibleName",a.endDateAccessibleName),c.R7$(),c.Y8G("cdkPortalOutlet",a._actionsPortal),c.R7$(),c.AVh("cdk-visually-hidden",!a._closeButtonFocused),c.Y8G("color",a.color||"primary"),c.R7$(),c.JRh(a._closeButtonText)}},dependencies:[Ve.kB,Fa,Le.I3,qt.$z],styles:[".mat-datepicker-content{display:block;border-radius:4px;background-color:var(--mat-datepicker-calendar-container-background-color);color:var(--mat-datepicker-calendar-container-text-color);box-shadow:var(--mat-datepicker-calendar-container-elevation-shadow);border-radius:var(--mat-datepicker-calendar-container-shape)}.mat-datepicker-content .mat-calendar{width:296px;height:354px}.mat-datepicker-content .mat-datepicker-content-container-with-custom-header .mat-calendar{height:auto}.mat-datepicker-content .mat-datepicker-close-button{position:absolute;top:100%;left:0;margin-top:8px}.ng-animating .mat-datepicker-content .mat-datepicker-close-button{display:none}.mat-datepicker-content-container{display:flex;flex-direction:column;justify-content:space-between}.mat-datepicker-content-touch{display:block;max-height:80vh;box-shadow:var(--mat-datepicker-calendar-container-touch-elevation-shadow);border-radius:var(--mat-datepicker-calendar-container-touch-shape);position:relative;overflow:visible}.mat-datepicker-content-touch .mat-datepicker-content-container{min-height:312px;max-height:788px;min-width:250px;max-width:750px}.mat-datepicker-content-touch .mat-calendar{width:100%;height:auto}@media all and (orientation: landscape){.mat-datepicker-content-touch .mat-datepicker-content-container{width:64vh;height:80vh}}@media all and (orientation: portrait){.mat-datepicker-content-touch .mat-datepicker-content-container{width:80vw;height:100vw}.mat-datepicker-content-touch .mat-datepicker-content-container-with-actions{height:115vw}}"],encapsulation:2,data:{animation:[Pr.transformPanel,Pr.fadeInCalendar]},changeDetection:0})}}return i})(),Vr=(()=>{class i{get startAt(){return this._startAt||(this.datepickerInput?this.datepickerInput.getStartValue():null)}set startAt(t){this._startAt=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get color(){return this._color||(this.datepickerInput?this.datepickerInput.getThemePalette():void 0)}set color(t){this._color=t}get disabled(){return void 0===this._disabled&&this.datepickerInput?this.datepickerInput.disabled:!!this._disabled}set disabled(t){t!==this._disabled&&(this._disabled=t,this.stateChanges.next(void 0))}get panelClass(){return this._panelClass}set panelClass(t){this._panelClass=(0,Wu.cc)(t)}get opened(){return this._opened}set opened(t){t?this.open():this.close()}_getMinDate(){return this.datepickerInput&&this.datepickerInput.min}_getMaxDate(){return this.datepickerInput&&this.datepickerInput.max}_getDateFilter(){return this.datepickerInput&&this.datepickerInput.dateFilter}constructor(t,e,a,s,r,o,l){this._overlay=t,this._ngZone=e,this._viewContainerRef=a,this._dateAdapter=r,this._dir=o,this._model=l,this._inputStateChanges=bt.yU.EMPTY,this._document=(0,c.WQX)(le.qQ),this.startView="month",this.touchUi=!1,this.xPosition="start",this.yPosition="below",this.restoreFocus=!0,this.yearSelected=new c.bkB,this.monthSelected=new c.bkB,this.viewChanged=new c.bkB(!0),this.openedStream=new c.bkB,this.closedStream=new c.bkB,this._opened=!1,this.id="mat-datepicker-"+kf++,this._focusedElementBeforeOpen=null,this._backdropHarnessClass=`${this.id}-backdrop`,this.stateChanges=new Qt.B,this._scrollStrategy=s}ngOnChanges(t){const e=t.xPosition||t.yPosition;if(e&&!e.firstChange&&this._overlayRef){const a=this._overlayRef.getConfig().positionStrategy;a instanceof ce.rW&&(this._setConnectedPositions(a),this.opened&&this._overlayRef.updatePosition())}this.stateChanges.next(void 0)}ngOnDestroy(){this._destroyOverlay(),this.close(),this._inputStateChanges.unsubscribe(),this.stateChanges.complete()}select(t){this._model.add(t)}_selectYear(t){this.yearSelected.emit(t)}_selectMonth(t){this.monthSelected.emit(t)}_viewChanged(t){this.viewChanged.emit(t)}registerInput(t){return this._inputStateChanges.unsubscribe(),this.datepickerInput=t,this._inputStateChanges=t.stateChanges.subscribe(()=>this.stateChanges.next(void 0)),this._model}registerActions(t){this._actionsPortal=t,this._componentRef?.instance._assignActions(t,!0)}removeActions(t){t===this._actionsPortal&&(this._actionsPortal=null,this._componentRef?.instance._assignActions(null,!0))}open(){this._opened||this.disabled||this._componentRef?.instance._isAnimating||(this._focusedElementBeforeOpen=(0,ze.vc)(),this._openOverlay(),this._opened=!0,this.openedStream.emit())}close(){if(!this._opened||this._componentRef?.instance._isAnimating)return;const t=this.restoreFocus&&this._focusedElementBeforeOpen&&"function"==typeof this._focusedElementBeforeOpen.focus,e=()=>{this._opened&&(this._opened=!1,this.closedStream.emit())};if(this._componentRef){const{instance:a,location:s}=this._componentRef;a._startExitAnimation(),a._animationDone.pipe((0,Ma.s)(1)).subscribe(()=>{const r=this._document.activeElement;t&&(!r||r===this._document.activeElement||s.nativeElement.contains(r))&&this._focusedElementBeforeOpen.focus(),this._focusedElementBeforeOpen=null,this._destroyOverlay()})}t?setTimeout(e):e()}_applyPendingSelection(){this._componentRef?.instance?._applyPendingSelection()}_forwardContentValues(t){t.datepicker=this,t.color=this.color,t._dialogLabelId=this.datepickerInput.getOverlayLabelId(),t._assignActions(this._actionsPortal,!1)}_openOverlay(){this._destroyOverlay();const t=this.touchUi,e=new Le.A8(Or,this._viewContainerRef),a=this._overlayRef=this._overlay.create(new ce.rR({positionStrategy:t?this._getDialogStrategy():this._getDropdownStrategy(),hasBackdrop:!0,backdropClass:[t?"cdk-overlay-dark-backdrop":"mat-overlay-transparent-backdrop",this._backdropHarnessClass],direction:this._dir,scrollStrategy:t?this._overlay.scrollStrategies.block():this._scrollStrategy(),panelClass:"mat-datepicker-"+(t?"dialog":"popup")}));this._getCloseStream(a).subscribe(s=>{s&&s.preventDefault(),this.close()}),a.keydownEvents().subscribe(s=>{const r=s.keyCode;(r===S.i7||r===S.n6||r===S.UQ||r===S.LE||r===S.w_||r===S.dB)&&s.preventDefault()}),this._componentRef=a.attach(e),this._forwardContentValues(this._componentRef.instance),t||this._ngZone.onStable.pipe((0,Ma.s)(1)).subscribe(()=>a.updatePosition())}_destroyOverlay(){this._overlayRef&&(this._overlayRef.dispose(),this._overlayRef=this._componentRef=null)}_getDialogStrategy(){return this._overlay.position().global().centerHorizontally().centerVertically()}_getDropdownStrategy(){const t=this._overlay.position().flexibleConnectedTo(this.datepickerInput.getConnectedOverlayOrigin()).withTransformOriginOn(".mat-datepicker-content").withFlexibleDimensions(!1).withViewportMargin(8).withLockedPosition();return this._setConnectedPositions(t)}_setConnectedPositions(t){const e="end"===this.xPosition?"end":"start",a="start"===e?"end":"start",s="above"===this.yPosition?"bottom":"top",r="top"===s?"bottom":"top";return t.withPositions([{originX:e,originY:r,overlayX:e,overlayY:s},{originX:e,originY:s,overlayX:e,overlayY:r},{originX:a,originY:r,overlayX:a,overlayY:s},{originX:a,originY:s,overlayX:a,overlayY:r}])}_getCloseStream(t){const e=["ctrlKey","shiftKey","metaKey"];return(0,Mi.h)(t.backdropClick(),t.detachments(),t.keydownEvents().pipe((0,Yu.p)(a=>a.keyCode===S._f&&!(0,S.rp)(a)||this.datepickerInput&&(0,S.rp)(a,"altKey")&&a.keyCode===S.i7&&e.every(s=>!(0,S.rp)(a,s)))))}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(ce.hJ),c.rXU(c.SKi),c.rXU(c.c1b),c.rXU(Fr),c.rXU(E.MJ,8),c.rXU(Be.dS,8),c.rXU(Bt))}}static{this.\u0275dir=c.FsC({type:i,inputs:{calendarHeaderComponent:"calendarHeaderComponent",startAt:"startAt",startView:"startView",color:"color",touchUi:[2,"touchUi","touchUi",c.L39],disabled:[2,"disabled","disabled",c.L39],xPosition:"xPosition",yPosition:"yPosition",restoreFocus:[2,"restoreFocus","restoreFocus",c.L39],dateClass:"dateClass",panelClass:"panelClass",opened:[2,"opened","opened",c.L39]},outputs:{yearSelected:"yearSelected",monthSelected:"monthSelected",viewChanged:"viewChanged",openedStream:"opened",closedStream:"closed"},features:[c.GFd,c.OA$]})}}return i})();class Si{constructor(n,t){this.target=n,this.targetElement=t,this.value=this.target.value}}let Lr=(()=>{class i{get value(){return this._model?this._getValueFromModel(this._model.selection):this._pendingValue}set value(t){this._assignValueProgrammatically(t)}get disabled(){return!!this._disabled||this._parentDisabled()}set disabled(t){const e=t,a=this._elementRef.nativeElement;this._disabled!==e&&(this._disabled=e,this.stateChanges.next(void 0)),e&&this._isInitialized&&a.blur&&a.blur()}_getValidators(){return[this._parseValidator,this._minValidator,this._maxValidator,this._filterValidator]}_registerModel(t){this._model=t,this._valueChangesSubscription.unsubscribe(),this._pendingValue&&this._assignValue(this._pendingValue),this._valueChangesSubscription=this._model.selectionChanged.subscribe(e=>{if(this._shouldHandleChangeEvent(e)){const a=this._getValueFromModel(e.selection);this._lastValueValid=this._isValidValue(a),this._cvaOnChange(a),this._onTouched(),this._formatValue(a),this.dateInput.emit(new Si(this,this._elementRef.nativeElement)),this.dateChange.emit(new Si(this,this._elementRef.nativeElement))}})}constructor(t,e,a){this._elementRef=t,this._dateAdapter=e,this._dateFormats=a,this.dateChange=new c.bkB,this.dateInput=new c.bkB,this.stateChanges=new Qt.B,this._onTouched=()=>{},this._validatorOnChange=()=>{},this._cvaOnChange=()=>{},this._valueChangesSubscription=bt.yU.EMPTY,this._localeSubscription=bt.yU.EMPTY,this._parseValidator=()=>this._lastValueValid?null:{matDatepickerParse:{text:this._elementRef.nativeElement.value}},this._filterValidator=s=>{const r=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(s.value));return!r||this._matchesFilter(r)?null:{matDatepickerFilter:!0}},this._minValidator=s=>{const r=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(s.value)),o=this._getMinDate();return!o||!r||this._dateAdapter.compareDate(o,r)<=0?null:{matDatepickerMin:{min:o,actual:r}}},this._maxValidator=s=>{const r=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(s.value)),o=this._getMaxDate();return!o||!r||this._dateAdapter.compareDate(o,r)>=0?null:{matDatepickerMax:{max:o,actual:r}}},this._lastValueValid=!1,this._localeSubscription=e.localeChanges.subscribe(()=>{this._assignValueProgrammatically(this.value)})}ngAfterViewInit(){this._isInitialized=!0}ngOnChanges(t){Br(t,this._dateAdapter)&&this.stateChanges.next(void 0)}ngOnDestroy(){this._valueChangesSubscription.unsubscribe(),this._localeSubscription.unsubscribe(),this.stateChanges.complete()}registerOnValidatorChange(t){this._validatorOnChange=t}validate(t){return this._validator?this._validator(t):null}writeValue(t){this._assignValueProgrammatically(t)}registerOnChange(t){this._cvaOnChange=t}registerOnTouched(t){this._onTouched=t}setDisabledState(t){this.disabled=t}_onKeydown(t){(0,S.rp)(t,"altKey")&&t.keyCode===S.n6&&["ctrlKey","shiftKey","metaKey"].every(s=>!(0,S.rp)(t,s))&&!this._elementRef.nativeElement.readOnly&&(this._openPopup(),t.preventDefault())}_onInput(t){const e=this._lastValueValid;let a=this._dateAdapter.parse(t,this._dateFormats.parse.dateInput);this._lastValueValid=this._isValidValue(a),a=this._dateAdapter.getValidDateOrNull(a);const s=!this._dateAdapter.sameDate(a,this.value);!a||s?this._cvaOnChange(a):(t&&!this.value&&this._cvaOnChange(a),e!==this._lastValueValid&&this._validatorOnChange()),s&&(this._assignValue(a),this.dateInput.emit(new Si(this,this._elementRef.nativeElement)))}_onChange(){this.dateChange.emit(new Si(this,this._elementRef.nativeElement))}_onBlur(){this.value&&this._formatValue(this.value),this._onTouched()}_formatValue(t){this._elementRef.nativeElement.value=null!=t?this._dateAdapter.format(t,this._dateFormats.display.dateInput):""}_assignValue(t){this._model?(this._assignValueToModel(t),this._pendingValue=null):this._pendingValue=t}_isValidValue(t){return!t||this._dateAdapter.isValid(t)}_parentDisabled(){return!1}_assignValueProgrammatically(t){t=this._dateAdapter.deserialize(t),this._lastValueValid=this._isValidValue(t),t=this._dateAdapter.getValidDateOrNull(t),this._assignValue(t),this._formatValue(t)}_matchesFilter(t){const e=this._getDateFilter();return!e||e(t)}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.aKT),c.rXU(E.MJ,8),c.rXU(E.de,8))}}static{this.\u0275dir=c.FsC({type:i,inputs:{value:"value",disabled:[2,"disabled","disabled",c.L39]},outputs:{dateChange:"dateChange",dateInput:"dateInput"},standalone:!0,features:[c.GFd,c.OA$]})}}return i})();function Br(i,n){const t=Object.keys(i);for(let e of t){const{previousValue:a,currentValue:s}=i[e];if(!n.isDateInstance(a)||!n.isDateInstance(s))return!0;if(!n.sameDate(a,s))return!0}return!1}let Af=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275dir=c.FsC({type:i,selectors:[["","matDatepickerToggleIcon",""]],standalone:!0})}}return i})(),Nr=(()=>{class i{get disabled(){return void 0===this._disabled&&this.datepicker?this.datepicker.disabled:!!this._disabled}set disabled(t){this._disabled=t}constructor(t,e,a){this._intl=t,this._changeDetectorRef=e,this._stateChanges=bt.yU.EMPTY;const s=Number(a);this.tabIndex=s||0===s?s:null}ngOnChanges(t){t.datepicker&&this._watchStateChanges()}ngOnDestroy(){this._stateChanges.unsubscribe()}ngAfterContentInit(){this._watchStateChanges()}_open(t){this.datepicker&&!this.disabled&&(this.datepicker.open(),t.stopPropagation())}_watchStateChanges(){const t=this.datepicker?this.datepicker.stateChanges:(0,ka.of)(),e=this.datepicker&&this.datepicker.datepickerInput?this.datepicker.datepickerInput.stateChanges:(0,ka.of)(),a=this.datepicker?(0,Mi.h)(this.datepicker.openedStream,this.datepicker.closedStream):(0,ka.of)();this._stateChanges.unsubscribe(),this._stateChanges=(0,Mi.h)(this._intl.changes,t,e,a).subscribe(()=>this._changeDetectorRef.markForCheck())}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(Ne),c.rXU(c.gRc),c.kS0("tabindex"))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-datepicker-toggle"]],contentQueries:function(e,a,s){if(1&e&&c.wni(s,Af,5),2&e){let r;c.mGM(r=c.lsd())&&(a._customIcon=r.first)}},viewQuery:function(e,a){if(1&e&&c.GBs(sf,5),2&e){let s;c.mGM(s=c.lsd())&&(a._button=s.first)}},hostAttrs:[1,"mat-datepicker-toggle"],hostVars:8,hostBindings:function(e,a){1&e&&c.bIt("click",function(r){return a._open(r)}),2&e&&(c.BMQ("tabindex",null)("data-mat-calendar",a.datepicker?a.datepicker.id:null),c.AVh("mat-datepicker-toggle-active",a.datepicker&&a.datepicker.opened)("mat-accent",a.datepicker&&"accent"===a.datepicker.color)("mat-warn",a.datepicker&&"warn"===a.datepicker.color))},inputs:{datepicker:[0,"for","datepicker"],tabIndex:"tabIndex",ariaLabel:[0,"aria-label","ariaLabel"],disabled:[2,"disabled","disabled",c.L39],disableRipple:"disableRipple"},exportAs:["matDatepickerToggle"],standalone:!0,features:[c.GFd,c.OA$,c.aNF],ngContentSelectors:of,decls:4,vars:6,consts:[["button",""],["mat-icon-button","","type","button",3,"disabled","disableRipple"],["viewBox","0 0 24 24","width","24px","height","24px","fill","currentColor","focusable","false","aria-hidden","true",1,"mat-datepicker-toggle-default-icon"],["d","M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"]],template:function(e,a){1&e&&(c.NAR(rf),c.j41(0,"button",1,0),c.DNE(2,lf,2,0,":svg:svg",2),c.SdG(3),c.k0s()),2&e&&(c.Y8G("disabled",a.disabled)("disableRipple",a.disableRipple),c.BMQ("aria-haspopup",a.datepicker?"dialog":null)("aria-label",a.ariaLabel||a._intl.openCalendarLabel)("tabindex",a.disabled?-1:a.tabIndex),c.R7$(2),c.vxM(a._customIcon?-1:2))},dependencies:[qt.iY],styles:[".mat-datepicker-toggle{pointer-events:auto;color:var(--mat-datepicker-toggle-icon-color)}.mat-datepicker-toggle-active{color:var(--mat-datepicker-toggle-active-state-icon-color)}.cdk-high-contrast-active .mat-datepicker-toggle-default-icon{color:CanvasText}"],encapsulation:2,changeDetection:0})}}return i})();function Rf(i){return Oa(i,!0)}function jr(i){return i.nodeType===Node.ELEMENT_NODE}function Oa(i,n){if(jr(i)&&n){const e=(i.getAttribute?.("aria-labelledby")?.split(/\s+/g)||[]).reduce((a,s)=>{const r=document.getElementById(s);return r&&a.push(r),a},[]);if(e.length)return e.map(a=>Oa(a,!1)).join(" ")}if(jr(i)){const t=i.getAttribute("aria-label")?.trim();if(t)return t}if(function Ef(i){return"INPUT"===i.nodeName}(i)||function Tf(i){return"TEXTAREA"===i.nodeName}(i)){if(i.labels?.length)return Array.from(i.labels).map(a=>Oa(a,!1)).join(" ");const t=i.getAttribute("placeholder")?.trim();if(t)return t;const e=i.getAttribute("title")?.trim();if(e)return e}return(i.textContent||"").replace(/\s+/g," ").trim()}const Ai=new c.nKC("MAT_DATE_RANGE_INPUT_PARENT");let Hr=(()=>{class i extends Lr{get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(t){this._errorStateTracker.matcher=t}get errorState(){return this._errorStateTracker.errorState}set errorState(t){this._errorStateTracker.errorState=t}constructor(t,e,a,s,r,o,l,d){super(e,l,d),this._rangeInput=t,this._elementRef=e,this._defaultErrorStateMatcher=a,this._injector=s,this._parentForm=r,this._parentFormGroup=o,this._dir=(0,c.WQX)(Be.dS,{optional:!0}),this._errorStateTracker=new E.X0(this._defaultErrorStateMatcher,null,this._parentFormGroup,this._parentForm,this.stateChanges)}ngOnInit(){const t=this._injector.get(V.vO,null,{optional:!0,self:!0});t&&(this.ngControl=t,this._errorStateTracker.ngControl=t)}ngDoCheck(){this.ngControl&&this.updateErrorState()}isEmpty(){return 0===this._elementRef.nativeElement.value.length}_getPlaceholder(){return this._elementRef.nativeElement.placeholder}focus(){this._elementRef.nativeElement.focus()}getMirrorValue(){const t=this._elementRef.nativeElement,e=t.value;return e.length>0?e:t.placeholder}updateErrorState(){this._errorStateTracker.updateErrorState()}_onInput(t){super._onInput(t),this._rangeInput._handleChildValueChange()}_openPopup(){this._rangeInput._openDatepicker()}_getMinDate(){return this._rangeInput.min}_getMaxDate(){return this._rangeInput.max}_getDateFilter(){return this._rangeInput.dateFilter}_parentDisabled(){return this._rangeInput._groupDisabled}_shouldHandleChangeEvent({source:t}){return t!==this._rangeInput._startInput&&t!==this._rangeInput._endInput}_assignValueProgrammatically(t){super._assignValueProgrammatically(t),(this===this._rangeInput._startInput?this._rangeInput._endInput:this._rangeInput._startInput)?._validatorOnChange()}_getAccessibleName(){return Rf(this._elementRef.nativeElement)}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(Ai),c.rXU(c.aKT),c.rXU(E.es),c.rXU(c.zZn),c.rXU(V.cV,8),c.rXU(V.j4,8),c.rXU(E.MJ,8),c.rXU(E.de,8))}}static{this.\u0275dir=c.FsC({type:i,inputs:{errorStateMatcher:"errorStateMatcher"},standalone:!0,features:[c.Vt3]})}}return i})(),Yr=(()=>{class i extends Hr{constructor(t,e,a,s,r,o,l,d){super(t,e,a,s,r,o,l,d),this._startValidator=h=>{const u=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(h.value)),f=this._model?this._model.selection.end:null;return!u||!f||this._dateAdapter.compareDate(u,f)<=0?null:{matStartDateInvalid:{end:f,actual:u}}},this._validator=V.k0.compose([...super._getValidators(),this._startValidator])}_getValueFromModel(t){return t.start}_shouldHandleChangeEvent(t){return!(!super._shouldHandleChangeEvent(t)||(t.oldValue?.start?t.selection.start&&!this._dateAdapter.compareDate(t.oldValue.start,t.selection.start):!t.selection.start))}_assignValueToModel(t){if(this._model){const e=new Z(t,this._model.selection.end);this._model.updateSelection(e,this)}}_formatValue(t){super._formatValue(t),this._rangeInput._handleChildValueChange()}_onKeydown(t){const e=this._rangeInput._endInput,a=this._elementRef.nativeElement,s="rtl"!==this._dir?.value;(t.keyCode===S.LE&&s||t.keyCode===S.UQ&&!s)&&a.selectionStart===a.value.length&&a.selectionEnd===a.value.length?(t.preventDefault(),e._elementRef.nativeElement.setSelectionRange(0,0),e.focus()):super._onKeydown(t)}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(Ai),c.rXU(c.aKT),c.rXU(E.es),c.rXU(c.zZn),c.rXU(V.cV,8),c.rXU(V.j4,8),c.rXU(E.MJ,8),c.rXU(E.de,8))}}static{this.\u0275dir=c.FsC({type:i,selectors:[["input","matStartDate",""]],hostAttrs:["type","text",1,"mat-start-date","mat-date-range-input-inner"],hostVars:5,hostBindings:function(e,a){1&e&&c.bIt("input",function(r){return a._onInput(r.target.value)})("change",function(){return a._onChange()})("keydown",function(r){return a._onKeydown(r)})("blur",function(){return a._onBlur()}),2&e&&(c.Mr5("disabled",a.disabled),c.BMQ("aria-haspopup",a._rangeInput.rangePicker?"dialog":null)("aria-owns",(null==a._rangeInput.rangePicker?null:a._rangeInput.rangePicker.opened)&&a._rangeInput.rangePicker.id||null)("min",a._getMinDate()?a._dateAdapter.toIso8601(a._getMinDate()):null)("max",a._getMaxDate()?a._dateAdapter.toIso8601(a._getMaxDate()):null))},outputs:{dateChange:"dateChange",dateInput:"dateInput"},standalone:!0,features:[c.Jv_([{provide:V.kq,useExisting:i,multi:!0},{provide:V.cz,useExisting:i,multi:!0}]),c.Vt3]})}}return i})(),Wr=(()=>{class i extends Hr{constructor(t,e,a,s,r,o,l,d){super(t,e,a,s,r,o,l,d),this._endValidator=h=>{const u=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(h.value)),f=this._model?this._model.selection.start:null;return!u||!f||this._dateAdapter.compareDate(u,f)>=0?null:{matEndDateInvalid:{start:f,actual:u}}},this._validator=V.k0.compose([...super._getValidators(),this._endValidator])}_getValueFromModel(t){return t.end}_shouldHandleChangeEvent(t){return!(!super._shouldHandleChangeEvent(t)||(t.oldValue?.end?t.selection.end&&!this._dateAdapter.compareDate(t.oldValue.end,t.selection.end):!t.selection.end))}_assignValueToModel(t){if(this._model){const e=new Z(this._model.selection.start,t);this._model.updateSelection(e,this)}}_moveCaretToEndOfStartInput(){const t=this._rangeInput._startInput._elementRef.nativeElement,e=t.value;e.length>0&&t.setSelectionRange(e.length,e.length),t.focus()}_onKeydown(t){const e=this._elementRef.nativeElement,a="rtl"!==this._dir?.value;t.keyCode!==S.G_||e.value?(t.keyCode===S.UQ&&a||t.keyCode===S.LE&&!a)&&0===e.selectionStart&&0===e.selectionEnd?(t.preventDefault(),this._moveCaretToEndOfStartInput()):super._onKeydown(t):this._moveCaretToEndOfStartInput()}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(Ai),c.rXU(c.aKT),c.rXU(E.es),c.rXU(c.zZn),c.rXU(V.cV,8),c.rXU(V.j4,8),c.rXU(E.MJ,8),c.rXU(E.de,8))}}static{this.\u0275dir=c.FsC({type:i,selectors:[["input","matEndDate",""]],hostAttrs:["type","text",1,"mat-end-date","mat-date-range-input-inner"],hostVars:5,hostBindings:function(e,a){1&e&&c.bIt("input",function(r){return a._onInput(r.target.value)})("change",function(){return a._onChange()})("keydown",function(r){return a._onKeydown(r)})("blur",function(){return a._onBlur()}),2&e&&(c.Mr5("disabled",a.disabled),c.BMQ("aria-haspopup",a._rangeInput.rangePicker?"dialog":null)("aria-owns",(null==a._rangeInput.rangePicker?null:a._rangeInput.rangePicker.opened)&&a._rangeInput.rangePicker.id||null)("min",a._getMinDate()?a._dateAdapter.toIso8601(a._getMinDate()):null)("max",a._getMaxDate()?a._dateAdapter.toIso8601(a._getMaxDate()):null))},outputs:{dateChange:"dateChange",dateInput:"dateInput"},standalone:!0,features:[c.Jv_([{provide:V.kq,useExisting:i,multi:!0},{provide:V.cz,useExisting:i,multi:!0}]),c.Vt3]})}}return i})(),If=0,Pf=(()=>{class i{get value(){return this._model?this._model.selection:null}get shouldLabelFloat(){return this.focused||!this.empty}get placeholder(){const t=this._startInput?._getPlaceholder()||"",e=this._endInput?._getPlaceholder()||"";return t||e?`${t} ${this.separator} ${e}`:""}get rangePicker(){return this._rangePicker}set rangePicker(t){t&&(this._model=t.registerInput(this),this._rangePicker=t,this._closedSubscription.unsubscribe(),this._closedSubscription=t.closedStream.subscribe(()=>{this._startInput?._onTouched(),this._endInput?._onTouched()}),this._registerModel(this._model))}get required(){return this._required??(this._isTargetRequired(this)||this._isTargetRequired(this._startInput)||this._isTargetRequired(this._endInput))??!1}set required(t){this._required=t}get dateFilter(){return this._dateFilter}set dateFilter(t){const e=this._startInput,a=this._endInput,s=e&&e._matchesFilter(e.value),r=a&&a._matchesFilter(e.value);this._dateFilter=t,e&&e._matchesFilter(e.value)!==s&&e._validatorOnChange(),a&&a._matchesFilter(a.value)!==r&&a._validatorOnChange()}get min(){return this._min}set min(t){const e=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t));this._dateAdapter.sameDate(e,this._min)||(this._min=e,this._revalidate())}get max(){return this._max}set max(t){const e=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t));this._dateAdapter.sameDate(e,this._max)||(this._max=e,this._revalidate())}get disabled(){return this._startInput&&this._endInput?this._startInput.disabled&&this._endInput.disabled:this._groupDisabled}set disabled(t){t!==this._groupDisabled&&(this._groupDisabled=t,this.stateChanges.next(void 0))}get errorState(){return!(!this._startInput||!this._endInput)&&(this._startInput.errorState||this._endInput.errorState)}get empty(){const t=!!this._startInput&&this._startInput.isEmpty(),e=!!this._endInput&&this._endInput.isEmpty();return t&&e}constructor(t,e,a,s,r){this._changeDetectorRef=t,this._elementRef=e,this._dateAdapter=s,this._formField=r,this._closedSubscription=bt.yU.EMPTY,this.id="mat-date-range-input-"+If++,this.focused=!1,this.controlType="mat-date-range-input",this._groupDisabled=!1,this._ariaDescribedBy=null,this.separator="\u2013",this.comparisonStart=null,this.comparisonEnd=null,this.stateChanges=new Qt.B,this.disableAutomaticLabeling=!0,r?._elementRef.nativeElement.classList.contains("mat-mdc-form-field")&&e.nativeElement.classList.add("mat-mdc-input-element","mat-mdc-form-field-input-control","mdc-text-field__input"),this.ngControl=a}setDescribedByIds(t){this._ariaDescribedBy=t.length?t.join(" "):null}onContainerClick(){!this.focused&&!this.disabled&&(this._model&&this._model.selection.start?this._endInput.focus():this._startInput.focus())}ngAfterContentInit(){this._model&&this._registerModel(this._model),(0,Mi.h)(this._startInput.stateChanges,this._endInput.stateChanges).subscribe(()=>{this.stateChanges.next(void 0)})}ngOnChanges(t){Br(t,this._dateAdapter)&&this.stateChanges.next(void 0)}ngOnDestroy(){this._closedSubscription.unsubscribe(),this.stateChanges.complete()}getStartValue(){return this.value?this.value.start:null}getThemePalette(){return this._formField?this._formField.color:void 0}getConnectedOverlayOrigin(){return this._formField?this._formField.getConnectedOverlayOrigin():this._elementRef}getOverlayLabelId(){return this._formField?this._formField.getLabelId():null}_getInputMirrorValue(t){const e="start"===t?this._startInput:this._endInput;return e?e.getMirrorValue():""}_shouldHidePlaceholders(){return!!this._startInput&&!this._startInput.isEmpty()}_handleChildValueChange(){this.stateChanges.next(void 0),this._changeDetectorRef.markForCheck()}_openDatepicker(){this._rangePicker&&this._rangePicker.open()}_shouldHideSeparator(){return(!this._formField||this._formField.getLabelId()&&!this._formField._shouldLabelFloat())&&this.empty}_getAriaLabelledby(){const t=this._formField;return t&&t._hasFloatingLabel()?t._labelId:null}_getStartDateAccessibleName(){return this._startInput._getAccessibleName()}_getEndDateAccessibleName(){return this._endInput._getAccessibleName()}_updateFocus(t){this.focused=null!==t,this.stateChanges.next()}_revalidate(){this._startInput&&this._startInput._validatorOnChange(),this._endInput&&this._endInput._validatorOnChange()}_registerModel(t){this._startInput&&this._startInput._registerModel(t),this._endInput&&this._endInput._registerModel(t)}_isTargetRequired(t){return t?.ngControl?.control?.hasValidator(V.k0.required)}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.gRc),c.rXU(c.aKT),c.rXU(V.ZU,10),c.rXU(E.MJ,8),c.rXU(Vt.xb,8))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-date-range-input"]],contentQueries:function(e,a,s){if(1&e&&(c.wni(s,Yr,5),c.wni(s,Wr,5)),2&e){let r;c.mGM(r=c.lsd())&&(a._startInput=r.first),c.mGM(r=c.lsd())&&(a._endInput=r.first)}},hostAttrs:["role","group",1,"mat-date-range-input"],hostVars:8,hostBindings:function(e,a){2&e&&(c.BMQ("id",a.id)("aria-labelledby",a._getAriaLabelledby())("aria-describedby",a._ariaDescribedBy)("data-mat-calendar",a.rangePicker?a.rangePicker.id:null),c.AVh("mat-date-range-input-hide-placeholders",a._shouldHidePlaceholders())("mat-date-range-input-required",a.required))},inputs:{rangePicker:"rangePicker",required:[2,"required","required",c.L39],dateFilter:"dateFilter",min:"min",max:"max",disabled:[2,"disabled","disabled",c.L39],separator:"separator",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd"},exportAs:["matDateRangeInput"],standalone:!0,features:[c.Jv_([{provide:Vt.qT,useExisting:i},{provide:Ai,useExisting:i}]),c.GFd,c.OA$,c.aNF],ngContentSelectors:df,decls:11,vars:5,consts:[["cdkMonitorSubtreeFocus","",1,"mat-date-range-input-container",3,"cdkFocusChange"],[1,"mat-date-range-input-wrapper"],["aria-hidden","true",1,"mat-date-range-input-mirror"],[1,"mat-date-range-input-separator"],[1,"mat-date-range-input-wrapper","mat-date-range-input-end-wrapper"]],template:function(e,a){1&e&&(c.NAR(cf),c.j41(0,"div",0),c.bIt("cdkFocusChange",function(r){return a._updateFocus(r)}),c.j41(1,"div",1),c.SdG(2),c.j41(3,"span",2),c.EFF(4),c.k0s()(),c.j41(5,"span",3),c.EFF(6),c.k0s(),c.j41(7,"div",4),c.SdG(8,1),c.j41(9,"span",2),c.EFF(10),c.k0s()()()),2&e&&(c.R7$(4),c.JRh(a._getInputMirrorValue("start")),c.R7$(),c.AVh("mat-date-range-input-separator-hidden",a._shouldHideSeparator()),c.R7$(),c.JRh(a.separator),c.R7$(4),c.JRh(a._getInputMirrorValue("end")))},dependencies:[Ve.vR],styles:[".mat-date-range-input{display:block;width:100%}.mat-date-range-input-container{display:flex;align-items:center}.mat-date-range-input-separator{transition:opacity 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);margin:0 4px;color:var(--mat-datepicker-range-input-separator-color)}.mat-form-field-disabled .mat-date-range-input-separator{color:var(--mat-datepicker-range-input-disabled-state-separator-color)}._mat-animation-noopable .mat-date-range-input-separator{transition:none}.mat-date-range-input-separator-hidden{-webkit-user-select:none;user-select:none;opacity:0;transition:none}.mat-date-range-input-wrapper{position:relative;overflow:hidden;max-width:calc(50% - 4px)}.mat-date-range-input-end-wrapper{flex-grow:1}.mat-date-range-input-inner{position:absolute;top:0;left:0;font:inherit;background:rgba(0,0,0,0);color:currentColor;border:none;outline:none;padding:0;margin:0;vertical-align:bottom;text-align:inherit;-webkit-appearance:none;width:100%;height:100%}.mat-date-range-input-inner:-moz-ui-invalid{box-shadow:none}.mat-date-range-input-inner::placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner::-moz-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner::-webkit-input-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner:-ms-input-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-date-range-input-inner[disabled]{color:var(--mat-datepicker-range-input-disabled-state-text-color)}.mat-form-field-hide-placeholder .mat-date-range-input-inner::placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-date-range-input-inner::placeholder,.cdk-high-contrast-active .mat-date-range-input-hide-placeholders .mat-date-range-input-inner::placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-date-range-input-inner::-moz-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-moz-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-date-range-input-inner::-moz-placeholder,.cdk-high-contrast-active .mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-moz-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-date-range-input-inner::-webkit-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-webkit-input-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-date-range-input-inner::-webkit-input-placeholder,.cdk-high-contrast-active .mat-date-range-input-hide-placeholders .mat-date-range-input-inner::-webkit-input-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-date-range-input-inner:-ms-input-placeholder,.mat-date-range-input-hide-placeholders .mat-date-range-input-inner:-ms-input-placeholder{-webkit-user-select:none;user-select:none;color:rgba(0,0,0,0) !important;-webkit-text-fill-color:rgba(0,0,0,0);transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-date-range-input-inner:-ms-input-placeholder,.cdk-high-contrast-active .mat-date-range-input-hide-placeholders .mat-date-range-input-inner:-ms-input-placeholder{opacity:0}._mat-animation-noopable .mat-date-range-input-inner::placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner::-moz-placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner::-webkit-input-placeholder{transition:none}._mat-animation-noopable .mat-date-range-input-inner:-ms-input-placeholder{transition:none}.mat-date-range-input-mirror{-webkit-user-select:none;user-select:none;visibility:hidden;white-space:nowrap;display:inline-block;min-width:2px}.mat-mdc-form-field-type-mat-date-range-input .mat-mdc-form-field-infix{width:200px}"],encapsulation:2,changeDetection:0})}}return i})(),Ff=(()=>{class i extends Vr{_forwardContentValues(t){super._forwardContentValues(t);const e=this.datepickerInput;e&&(t.comparisonStart=e.comparisonStart,t.comparisonEnd=e.comparisonEnd,t.startDateAccessibleName=e._getStartDateAccessibleName(),t.endDateAccessibleName=e._getEndDateAccessibleName())}static{this.\u0275fac=(()=>{let t;return function(a){return(t||(t=c.xGo(i)))(a||i)}})()}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-date-range-picker"]],exportAs:["matDateRangePicker"],standalone:!0,features:[c.Jv_([_f,yf,{provide:Vr,useExisting:i}]),c.Vt3,c.aNF],decls:0,vars:0,template:function(e,a){},encapsulation:2,changeDetection:0})}}return i})(),Of=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=c.$C({type:i})}static{this.\u0275inj=c.G2t({providers:[Ne,Cf],imports:[le.MD,qt.Hl,ce.z_,Ve.Pd,Le.jc,E.yE,Or,Nr,Ir,Hu.Gj]})}}return i})();var Ri=O(2798);const Vf=["determinateSpinner"];function Lf(i,n){if(1&i&&(c.qSk(),c.j41(0,"svg",11),c.nrm(1,"circle",12),c.k0s()),2&i){const t=c.XpG();c.BMQ("viewBox",t._viewBox()),c.R7$(),c.xc7("stroke-dasharray",t._strokeCircumference(),"px")("stroke-dashoffset",t._strokeCircumference()/2,"px")("stroke-width",t._circleStrokeWidth(),"%"),c.BMQ("r",t._circleRadius())}}const Bf=new c.nKC("mat-progress-spinner-default-options",{providedIn:"root",factory:function zf(){return{diameter:Ur}}}),Ur=100;let jf=(()=>{class i{get color(){return this._color||this._defaultColor}set color(t){this._color=t}constructor(t,e,a){this._elementRef=t,this._defaultColor="primary",this._value=0,this._diameter=Ur,this._noopAnimations="NoopAnimations"===e&&!!a&&!a._forceAnimations,this.mode="mat-spinner"===t.nativeElement.nodeName.toLowerCase()?"indeterminate":"determinate",a&&(a.color&&(this.color=this._defaultColor=a.color),a.diameter&&(this.diameter=a.diameter),a.strokeWidth&&(this.strokeWidth=a.strokeWidth))}get value(){return"determinate"===this.mode?this._value:0}set value(t){this._value=Math.max(0,Math.min(100,t||0))}get diameter(){return this._diameter}set diameter(t){this._diameter=t||0}get strokeWidth(){return this._strokeWidth??this.diameter/10}set strokeWidth(t){this._strokeWidth=t||0}_circleRadius(){return(this.diameter-10)/2}_viewBox(){const t=2*this._circleRadius()+this.strokeWidth;return`0 0 ${t} ${t}`}_strokeCircumference(){return 2*Math.PI*this._circleRadius()}_strokeDashOffset(){return"determinate"===this.mode?this._strokeCircumference()*(100-this._value)/100:null}_circleStrokeWidth(){return this.strokeWidth/this.diameter*100}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(c.aKT),c.rXU(c.bc$,8),c.rXU(Bf))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["mat-progress-spinner"],["mat-spinner"]],viewQuery:function(e,a){if(1&e&&c.GBs(Vf,5),2&e){let s;c.mGM(s=c.lsd())&&(a._determinateCircle=s.first)}},hostAttrs:["role","progressbar","tabindex","-1",1,"mat-mdc-progress-spinner","mdc-circular-progress"],hostVars:18,hostBindings:function(e,a){2&e&&(c.BMQ("aria-valuemin",0)("aria-valuemax",100)("aria-valuenow","determinate"===a.mode?a.value:null)("mode",a.mode),c.HbH("mat-"+a.color),c.xc7("width",a.diameter,"px")("height",a.diameter,"px")("--mdc-circular-progress-size",a.diameter+"px")("--mdc-circular-progress-active-indicator-width",a.diameter+"px"),c.AVh("_mat-animation-noopable",a._noopAnimations)("mdc-circular-progress--indeterminate","indeterminate"===a.mode))},inputs:{color:"color",mode:"mode",value:[2,"value","value",c.Udg],diameter:[2,"diameter","diameter",c.Udg],strokeWidth:[2,"strokeWidth","strokeWidth",c.Udg]},exportAs:["matProgressSpinner"],standalone:!0,features:[c.GFd,c.aNF],decls:14,vars:11,consts:[["circle",""],["determinateSpinner",""],["aria-hidden","true",1,"mdc-circular-progress__determinate-container"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__determinate-circle-graphic"],["cx","50%","cy","50%",1,"mdc-circular-progress__determinate-circle"],["aria-hidden","true",1,"mdc-circular-progress__indeterminate-container"],[1,"mdc-circular-progress__spinner-layer"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-left"],[3,"ngTemplateOutlet"],[1,"mdc-circular-progress__gap-patch"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-right"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__indeterminate-circle-graphic"],["cx","50%","cy","50%"]],template:function(e,a){if(1&e&&(c.DNE(0,Lf,2,8,"ng-template",null,0,c.C5r),c.j41(2,"div",2,1),c.qSk(),c.j41(4,"svg",3),c.nrm(5,"circle",4),c.k0s()(),c.joV(),c.j41(6,"div",5)(7,"div",6)(8,"div",7),c.eu8(9,8),c.k0s(),c.j41(10,"div",9),c.eu8(11,8),c.k0s(),c.j41(12,"div",10),c.eu8(13,8),c.k0s()()()),2&e){const s=c.sdS(1);c.R7$(4),c.BMQ("viewBox",a._viewBox()),c.R7$(),c.xc7("stroke-dasharray",a._strokeCircumference(),"px")("stroke-dashoffset",a._strokeDashOffset(),"px")("stroke-width",a._circleStrokeWidth(),"%"),c.BMQ("r",a._circleRadius()),c.R7$(4),c.Y8G("ngTemplateOutlet",s),c.R7$(2),c.Y8G("ngTemplateOutlet",s),c.R7$(2),c.Y8G("ngTemplateOutlet",s)}},dependencies:[le.T3],styles:["@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-color-1-fade-in-out{from{opacity:.99}25%{opacity:.99}26%{opacity:0}89%{opacity:0}90%{opacity:.99}to{opacity:.99}}@keyframes mdc-circular-progress-color-2-fade-in-out{from{opacity:0}15%{opacity:0}25%{opacity:.99}50%{opacity:.99}51%{opacity:0}to{opacity:0}}@keyframes mdc-circular-progress-color-3-fade-in-out{from{opacity:0}40%{opacity:0}50%{opacity:.99}75%{opacity:.99}76%{opacity:0}to{opacity:0}}@keyframes mdc-circular-progress-color-4-fade-in-out{from{opacity:0}65%{opacity:0}75%{opacity:.99}90%{opacity:.99}to{opacity:0}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}.mdc-circular-progress{display:inline-flex;position:relative;direction:ltr;line-height:0;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__color-1{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdc-circular-progress-color-1-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__color-2{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdc-circular-progress-color-2-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__color-3{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdc-circular-progress-color-3-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__color-4{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdc-circular-progress-color-4-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--closed{opacity:0}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color)}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width)}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mat-mdc-progress-spinner .mdc-circular-progress--four-color .mdc-circular-progress__color-1 .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mat-mdc-progress-spinner .mdc-circular-progress--four-color .mdc-circular-progress__color-2 .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mat-mdc-progress-spinner .mdc-circular-progress--four-color .mdc-circular-progress__color-3 .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mat-mdc-progress-spinner .mdc-circular-progress--four-color .mdc-circular-progress__color-4 .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mat-mdc-progress-spinner .mdc-circular-progress{width:var(--mdc-circular-progress-size) !important;height:var(--mdc-circular-progress-size) !important}.mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}.cdk-high-contrast-active .mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.cdk-high-contrast-active .mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}"],encapsulation:2,changeDetection:0})}}return i})(),Hf=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=c.$C({type:i})}static{this.\u0275inj=c.G2t({imports:[le.MD,E.yE]})}}return i})();const Yf=["canvas"],Wf=["locationPicker"],Uf=(i,n)=>n[0];function $f(i,n){if(1&i&&(c.j41(0,"mat-radio-button",5),c.EFF(1),c.k0s()),2&i){const t=n.$implicit;c.Y8G("value",t),c.R7$(),c.JRh(t.optionName)}}function Gf(i,n){if(1&i){const t=c.RV6();c.j41(0,"mat-option",20),c.bIt("click",function(){c.eBV(t);const a=c.XpG();return c.Njj(a.validateSelectionStatus())}),c.EFF(1),c.k0s()}if(2&i){const t=n.$implicit;c.Y8G("value",t.id),c.R7$(),c.JRh(t.name)}}const Xf=[{path:"",title:"::Page:graphMenu",component:(()=>{class i{static{this.StatType=class{constructor(e,a,s,r){this.optionName=e,this.chartTitle=a,this.usesDateRange=s,this.sendQuery=r}}}constructor(t,e){this.geofenceData=t,this.reportingData=e,this.locale=(0,c.WQX)(c.xe9),this.localizationService=(0,c.WQX)(fe.PiF),this.statistics=[new i.StatType(this.localizationService.instant("::Stat:avgDwellTime:Option"),this.localizationService.instant("::Stat:avgDwellTime:Title"),!0,this.reportingData.getAverageDwellTimeByLocation),new i.StatType(this.localizationService.instant("::Stat:trailerCount:Option"),this.localizationService.instant("::Stat:trailerCount:Title"),!1,this.reportingData.getTrailerCountByLocation),new i.StatType(this.localizationService.instant("::Stat:avgTimeSinceMove:Option"),this.localizationService.instant("::Stat:avgTimeSinceMove:Title"),!0,this.reportingData.getAverageHoursSinceMoveByLocation),new i.StatType(this.localizationService.instant("::Stat:24hInactive:Option"),this.localizationService.instant("::Stat:24hInactive:Title"),!1,a=>this.reportingData.getInactiveAssetsByLocation(a,24))],this.selectedStatistic=null,this.dateRangeEnabled=!1,this.dateRange=new V.gE({start:new V.MJ(null),end:new V.MJ(null)}),this.geofences=[],this.selectedGeofences=[],this.loadedData=[],this.buttonPressed=!1,this.loadingChart=!1,this.stringSelectAllButton=this.localizationService.instant("::Chart:allLocations"),this.selectAllStatus=!0}ngAfterViewInit(){this.configureChart(),this.loadGeofences()}onSelectedStatisticChanged(){this.dateRangeEnabled=this.selectedStatistic.usesDateRange,this.dateRangeEnabled?(this.dateRange.controls.start.setValue(null),this.dateRange.controls.end.setValue(null)):(this.dateRange.controls.end.setValue(new Date),this.dateRange.controls.start.setValue(new Date))}chartGenerationEnabled(){return null!=this.selectedStatistic&&this.selectedGeofences.length>0&&(!this.selectedStatistic.usesDateRange||null!=this.dateRange.controls.start.value&&null!=this.dateRange.controls.end.value)}selectAllOrUnselectAll(){this.selectedGeofences=[],this.selectAllStatus?this.geofences.forEach(t=>this.selectedGeofences.push(t.id)):this.selectedGeofences=[],this.selectAllStatus=!this.selectAllStatus,this.setSelectAllString(this.selectAllStatus)}validateSelectionStatus(){this.selectAllStatus=this.selectedGeofences.length<this.geofences.length,this.setSelectAllString(this.selectAllStatus)}setSelectAllString(t){this.stringSelectAllButton=this.localizationService.instant(t?"::Chart:allLocations":"::Chart:noLocations")}loadGeofences(){var t=new fe.f$J;t.maxResultCount=1e3,this.geofenceData.getList(t).subscribe(e=>{console.log("Returned locations:",e),this.geofences=e.items.map(a=>({name:a.geofenceName,id:a.geofenceId})).sort((a,s)=>a.name.localeCompare(s.name))})}generateChart(){this.loadingChart=!0,this.buttonPressed=!0;let t=this.selectedStatistic.chartTitle,e=this.getDateRangeEnd().toLocaleDateString(this.locale),a=this.localizationService.instant("::Chart:assets");this.selectedStatistic.usesDateRange&&(e=`${this.getDateRangeBegin().toLocaleDateString(this.locale)} - ${this.getDateRangeEnd().toLocaleDateString(this.locale)}`,a=this.localizationService.instant("::Chart:hours")),this.isCurrentDate(this.getDateRangeEnd())&&(e=`${e} (${this.localizationService.instant("::Chart:asOf")} ${this.getCurrentDate().toLocaleTimeString(this.locale)})`),this.generateQuery().subscribe(s=>{console.log(s),this.loadedData=s.map(r=>({name:r.bin,value:r.value})),this.formatChartOptions(t,e,this.localizationService.instant("::Chart:location"),a),this.formatChartData(),this.chart.update(),this.loadingChart=!1})}getCurrentDate(){return new Date}getDateRangeBegin(){return this.dateRange.controls.start.value??this.getCurrentDate()}getDateRangeEnd(){return this.dateRange.controls.end.value??this.getCurrentDate()}getInclusiveDateRangeEnd(){if(null==this.dateRange.controls.end.value)return this.getCurrentDate();{let e=new Date(this.dateRange.controls.end.value.valueOf()+864e5);return new Date(e.getFullYear(),e.getMonth(),e.getDate())}}isCurrentDate(t){let e=this.getCurrentDate();return t.getFullYear()==e.getFullYear()&&t.getMonth()==e.getMonth()&&t.getDate()==e.getDate()}generateQuery(){let t={rangeBegin:this.getDateRangeBegin().toUTCString(),rangeEnd:this.getInclusiveDateRangeEnd().toUTCString(),locationIds:this.selectedGeofences};return this.selectedStatistic.sendQuery(t)}configureChart(){this.chart=new ca(this.canvasRef.nativeElement,{type:"bar",plugins:[Tu],options:{animation:!1,plugins:{title:{display:!0,text:"{Placeholder}",align:"center",font:{size:20,weight:"bold"}},subtitle:{display:!0,text:"{Placeholder}",align:"center",font:{size:15,style:"italic"},padding:{bottom:20}},legend:{display:!1},tooltip:{enabled:!1},datalabels:{anchor:"end",align:"top",font:{weight:"bold"}}},scales:{x:{display:!0},y:{display:!0}}},data:{labels:this.loadedData.map(t=>t.name),datasets:[{label:"{Placeholder}",data:this.loadedData.map(t=>t.value)}]}})}formatChartOptions(t,e,a,s){this.chart.options.plugins.title.text=t,this.chart.options.plugins.subtitle.text=e,this.chart.options.scales.x={title:{display:!0,text:a,font:{size:15,weight:"bold"}}};let r=1.1667*Math.ceil(Math.max(...this.loadedData.map(d=>d.value))),o=Math.max(5,Math.pow(10,Math.floor(Math.log10(r)))/4),l=Math.ceil(r/o)*o;console.log(`Max ${r} -> Max ${l} (Divisor ${o})`),this.chart.options.scales.y={title:{display:!0,text:s,font:{size:15,weight:"bold"}},ticks:{precision:0,stepSize:o},suggestedMin:0,max:l}}formatChartData(){this.chart.data.labels=this.loadedData.map(t=>t.name),this.chart.data.datasets[0].data=this.loadedData.map(t=>Math.floor(t.value))}static{this.\u0275fac=function(e){return new(e||i)(c.rXU(Iu),c.rXU(Pu))}}static{this.\u0275cmp=c.VBU({type:i,selectors:[["app-root"]],viewQuery:function(e,a){if(1&e&&(c.GBs(Yf,5),c.GBs(Wf,5)),2&e){let s;c.mGM(s=c.lsd())&&(a.canvasRef=s.first),c.mGM(s=c.lsd())&&(a.locationPicker=s.first)}},decls:46,vars:29,consts:[["dateRangePicker",""],["canvas",""],[1,"graph-menu"],[1,"radio"],["layout","row",1,"radio-group",3,"ngModelChange","change","ngModel"],[1,"radio-button",3,"value"],[1,"controls"],[1,"singleControl",3,"hidden"],[3,"formGroup","rangePicker","max"],["matStartDate","","placeholder","Start date","formControlName","start",3,"disabled"],["matEndDate","","placeholder","End date","formControlName","end",3,"disabled"],["matIconSuffix","",3,"for"],[1,"singleControl"],["multiple","",3,"valueChange","value"],[3,"value"],["mat-raised-button","",1,"mat-button",3,"click"],["mat-raised-button","",1,"mat-button",3,"click","disabled"],[3,"hidden"],[1,"vertical-layout",3,"hidden"],[1,"canvas"],[3,"click","value"]],template:function(e,a){if(1&e){const s=c.RV6();c.j41(0,"div",2)(1,"section",3)(2,"mat-label"),c.EFF(3),c.nI1(4,"abpLocalization"),c.k0s(),c.j41(5,"mat-radio-group",4),c.mxI("ngModelChange",function(o){return c.eBV(s),c.DH7(a.selectedStatistic,o)||(a.selectedStatistic=o),c.Njj(o)}),c.bIt("change",function(){return c.eBV(s),c.Njj(a.onSelectedStatisticChanged())}),c.Z7z(6,$f,2,2,"mat-radio-button",5,Uf),c.k0s()(),c.j41(8,"div"),c.nrm(9,"mat-divider"),c.k0s(),c.j41(10,"section")(11,"div",6)(12,"mat-form-field",7)(13,"mat-label"),c.EFF(14),c.nI1(15,"abpLocalization"),c.k0s(),c.j41(16,"mat-date-range-input",8),c.nrm(17,"input",9)(18,"input",10),c.k0s(),c.nrm(19,"mat-datepicker-toggle",11)(20,"mat-date-range-picker",null,0),c.k0s(),c.j41(22,"mat-form-field",12)(23,"mat-label"),c.EFF(24),c.nI1(25,"abpLocalization"),c.k0s(),c.j41(26,"mat-select",13),c.mxI("valueChange",function(o){return c.eBV(s),c.DH7(a.selectedGeofences,o)||(a.selectedGeofences=o),c.Njj(o)}),c.j41(27,"mat-select-trigger"),c.EFF(28),c.k0s(),c.Z7z(29,Gf,2,2,"mat-option",14,c.fX1),c.k0s()(),c.j41(31,"div",12)(32,"button",15),c.bIt("click",function(){return c.eBV(s),c.Njj(a.selectAllOrUnselectAll())}),c.EFF(33),c.k0s()()()(),c.j41(34,"section")(35,"button",16),c.bIt("click",function(){return c.eBV(s),c.Njj(a.generateChart())}),c.EFF(36),c.nI1(37,"abpLocalization"),c.k0s()(),c.j41(38,"div",17),c.nrm(39,"mat-divider"),c.k0s(),c.j41(40,"section",18)(41,"div"),c.nrm(42,"mat-spinner",17),c.k0s(),c.j41(43,"div",19),c.nrm(44,"canvas",17,1),c.k0s()()()}if(2&e){const s=c.sdS(21);c.R7$(3),c.JRh(c.bMT(4,21,"::Chart:select")),c.R7$(2),c.R50("ngModel",a.selectedStatistic),c.R7$(),c.Dyx(a.statistics),c.R7$(6),c.Y8G("hidden",!a.dateRangeEnabled),c.R7$(2),c.JRh(c.bMT(15,23,"::Chart:dateRange")),c.R7$(2),c.Y8G("formGroup",a.dateRange)("rangePicker",s)("max",a.getCurrentDate()),c.R7$(),c.Y8G("disabled",!a.dateRangeEnabled),c.R7$(),c.Y8G("disabled",!a.dateRangeEnabled),c.R7$(),c.Y8G("for",s),c.R7$(5),c.JRh(c.bMT(25,25,"::Chart:locations")),c.R7$(2),c.R50("value",a.selectedGeofences),c.R7$(2),c.SpI(" (",a.selectedGeofences.length," selected) "),c.R7$(),c.Dyx(a.geofences),c.R7$(4),c.JRh(a.stringSelectAllButton),c.R7$(2),c.Y8G("disabled",!a.chartGenerationEnabled()),c.R7$(),c.JRh(c.bMT(37,27,"::Chart:generate")),c.R7$(2),c.Y8G("hidden",0==a.buttonPressed),c.R7$(2),c.Y8G("hidden",0==a.buttonPressed),c.R7$(2),c.Y8G("hidden",!a.loadingChart),c.R7$(),c.Y8G("@unhidden",!a.loadingChart),c.R7$(),c.Y8G("hidden",a.loadingChart)}},dependencies:[V.me,V.BC,V.cb,V.vS,V.j4,V.JD,vr.a5,vr.lt,yr.q,Mr,Da,Vt.rl,Vt.nJ,Vt.yw,Nr,Pf,Yr,Wr,Ff,Ri.VO,Ri.$2,E.wT,qt.$z,jf,fe.aM3],styles:["section[_ngcontent-%COMP%]{display:flex;justify-content:space-around;width:100%}section.vertical-layout[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.graph-menu[_ngcontent-%COMP%], .radio[_ngcontent-%COMP%]{display:flex;flex-direction:column}.radio-group[_ngcontent-%COMP%]{display:flex;justify-content:space-around}.controls[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:center;align-content:center}mat-divider[_ngcontent-%COMP%]{margin-top:20px;margin-bottom:20px}.singleControl[_ngcontent-%COMP%]{padding-left:20px;padding-right:20px}.canvas[_ngcontent-%COMP%]{width:90%;display:flex;justify-content:center}"],data:{animation:[(0,N.hZ)("unhidden",[(0,N.kY)("false => true",[(0,N.iF)({opacity:"0"}),(0,N.i0)("500ms ease-in",(0,N.iF)({opacity:"1"}))])])]}})}}return i})()}];let Kf=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=c.$C({type:i})}static{this.\u0275inj=c.G2t({imports:[La.iI.forChild(Xf),La.iI]})}}return i})();var qf=O(5596);let Qf=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=c.$C({type:i})}static{this.\u0275inj=c.G2t({providers:[(0,E.aw)()],imports:[$r.G,Kf,Gr.wq,yr.w,ju,Vt.RG,Of,qf.RN,Ri.VO,Vt.RG,Ri.Ve,V.YN,V.X1,qt.Hl,Hf]})}}return i})()}}]);
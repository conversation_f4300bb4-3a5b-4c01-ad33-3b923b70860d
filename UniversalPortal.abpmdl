{"template": "app", "imports": {"Volo.Abp.LeptonXLiteTheme": {"version": "4.0.5", "isInstalled": true}, "Volo.Abp.Account": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.OpenIddict": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.Identity": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.TenantManagement": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.SettingManagement": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.PermissionManagement": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.FeatureManagement": {"version": "9.0.4", "isInstalled": true}}, "folders": {"items": {"src": {}, "test": {}}}, "packages": {"UniversalPortal.Application": {"path": "src/UniversalPortal.Application/UniversalPortal.Application.abppkg", "folder": "src"}, "UniversalPortal.Application.Tests": {"path": "test/UniversalPortal.Application.Tests/UniversalPortal.Application.Tests.abppkg", "folder": "test"}, "UniversalPortal.Domain.Shared": {"path": "src/UniversalPortal.Domain.Shared/UniversalPortal.Domain.Shared.abppkg", "folder": "src"}, "UniversalPortal.Application.Contracts": {"path": "src/UniversalPortal.Application.Contracts/UniversalPortal.Application.Contracts.abppkg", "folder": "src"}, "UniversalPortal.HttpApi": {"path": "src/UniversalPortal.HttpApi/UniversalPortal.HttpApi.abppkg", "folder": "src"}, "UniversalPortal.HttpApi.Client": {"path": "src/UniversalPortal.HttpApi.Client/UniversalPortal.HttpApi.Client.abppkg", "folder": "src"}, "UniversalPortal.EntityFrameworkCore.Tests": {"path": "test/UniversalPortal.EntityFrameworkCore.Tests/UniversalPortal.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "UniversalPortal.EntityFrameworkCore": {"path": "src/UniversalPortal.EntityFrameworkCore/UniversalPortal.EntityFrameworkCore.abppkg", "folder": "src"}, "UniversalPortal.TestBase": {"path": "test/UniversalPortal.TestBase/UniversalPortal.TestBase.abppkg", "folder": "test"}, "UniversalPortal.Domain.Tests": {"path": "test/UniversalPortal.Domain.Tests/UniversalPortal.Domain.Tests.abppkg", "folder": "test"}, "UniversalPortal.HttpApi.Client.ConsoleTestApp": {"path": "test/UniversalPortal.HttpApi.Client.ConsoleTestApp/UniversalPortal.HttpApi.Client.ConsoleTestApp.abppkg", "folder": "test"}, "UniversalPortal.DbMigrator": {"path": "src/UniversalPortal.DbMigrator/UniversalPortal.DbMigrator.abppkg", "folder": "src"}, "UniversalPortal.HttpApi.Host": {"path": "src/UniversalPortal.HttpApi.Host/UniversalPortal.HttpApi.Host.abppkg", "folder": "src"}, "UniversalPortal.Domain": {"path": "src/UniversalPortal.Domain/UniversalPortal.Domain.abppkg", "folder": "src"}}}
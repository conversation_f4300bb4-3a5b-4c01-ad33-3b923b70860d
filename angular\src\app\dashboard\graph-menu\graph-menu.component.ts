import { Component, After<PERSON><PERSON>w<PERSON>nit, ViewChild, ElementRef, inject, LOCALE_ID } from '@angular/core';

import { GeofenceService, ReportingService } from '../../proxy/services';
import { LocalizationService, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import { MatSelect } from '@angular/material/select';

import { FormControl, FormGroup } from '@angular/forms';
import { Observable } from 'rxjs';
import { BinnedQuantityDto } from '../../proxy/dtos/semantic-data';
import type { LocationReportQueryDto } from '../../proxy/dtos/queries/models';

import { Chart } from 'chart.js/auto'
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { animate, style, transition, trigger } from '@angular/animations';

@Component({
  selector: 'app-root',
  styleUrl: 'graph-menu.component.scss',
  templateUrl: 'graph-menu.component.html',
  standalone: false,
  animations: [
    trigger('unhidden', [
      transition('false => true', [
        style({ opacity: '0' }),
        animate('500ms ease-in', style({ opacity: '1' }))
      ]),
    ])
  ],
})
export class GraphMenuComponent implements AfterViewInit {

  private locale: string = inject(LOCALE_ID);
  private localizationService: LocalizationService = inject(LocalizationService);

  private static StatType = class StatType {
    constructor(
      public readonly optionName: string,
      public readonly chartTitle: string,
      public readonly usesDateRange: boolean,
      public readonly sendQuery: (query: LocationReportQueryDto) => Observable<BinnedQuantityDto[]>
    ) { }
  }

  public statistics = [
    new GraphMenuComponent.StatType(this.localizationService.instant('::Stat:avgDwellTime:Option'), this.localizationService.instant('::Stat:avgDwellTime:Title'), true, this.reportingData.getAverageDwellTimeByLocation),
    new GraphMenuComponent.StatType(this.localizationService.instant('::Stat:trailerCount:Option'), this.localizationService.instant('::Stat:trailerCount:Title'), false, this.reportingData.getTrailerCountByLocation),
    new GraphMenuComponent.StatType(this.localizationService.instant('::Stat:avgTimeSinceMove:Option'), this.localizationService.instant('::Stat:avgTimeSinceMove:Title'), true, this.reportingData.getAverageHoursSinceMoveByLocation),
    new GraphMenuComponent.StatType(this.localizationService.instant('::Stat:24hInactive:Option'), this.localizationService.instant('::Stat:24hInactive:Title'), false, query => this.reportingData.getInactiveAssetsByLocation(query, 24))
  ];
  public selectedStatistic: InstanceType<typeof GraphMenuComponent.StatType> = null;

  public dateRangeEnabled: boolean = false;
  public readonly dateRange = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null)
  })

  public geofences: { name: string, id: number }[] = [];
  public selectedGeofences: number[] = [];

  private loadedData: { name: string, value: number }[] = [];

  public buttonPressed: boolean = false;
  public loadingChart: boolean = false;

  public stringSelectAllButton: string = this.localizationService.instant('::Chart:allLocations');
  public selectAllStatus: boolean = true;


  @ViewChild('canvas') canvasRef: ElementRef<HTMLCanvasElement>;
  @ViewChild('locationPicker') locationPicker: MatSelect;

  chart: Chart;

  constructor(
    private geofenceData: GeofenceService,
    private reportingData: ReportingService
  ) { }

  ngAfterViewInit() {
    this.configureChart();

    this.loadGeofences();
  }

  public onSelectedStatisticChanged() {
    this.dateRangeEnabled = this.selectedStatistic.usesDateRange;
    if (!this.dateRangeEnabled) {
      this.dateRange.controls.end.setValue(new Date());
      this.dateRange.controls.start.setValue(new Date())
    }
    else {
      this.dateRange.controls.start.setValue(null);
      this.dateRange.controls.end.setValue(null);
    }
  }

  public chartGenerationEnabled(): boolean {
    return this.selectedStatistic != null
      && this.selectedGeofences.length > 0
      && (!this.selectedStatistic.usesDateRange
        || (this.dateRange.controls.start.value != null
          && this.dateRange.controls.end.value != null));
  }

  public selectAllOrUnselectAll() {
    this.selectedGeofences = [];
    if (this.selectAllStatus) {
      this.geofences.forEach(x => this.selectedGeofences.push(x.id))
    }
    else {
      this.selectedGeofences = [];
    }
    this.selectAllStatus = !this.selectAllStatus;
    this.setSelectAllString(this.selectAllStatus);
  }

  public validateSelectionStatus() {
    this.selectAllStatus = this.selectedGeofences.length < this.geofences.length;
    this.setSelectAllString(this.selectAllStatus);
  }

  private setSelectAllString(status: boolean) {
    if (status) {
      this.stringSelectAllButton = this.localizationService.instant('::Chart:allLocations');
    }
    else {
      this.stringSelectAllButton = this.localizationService.instant('::Chart:noLocations');
    }
  }

  private loadGeofences(): void {
    var request = new PagedAndSortedResultRequestDto();
    request.maxResultCount = 1000;

    this.geofenceData.getList(request).subscribe((responses) => {

      console.log("Returned locations:", responses);

      this.geofences = responses.items.map(x => ({
        name: x.geofenceName,
        id: x.geofenceId
      })).sort((a, b) => a.name.localeCompare(b.name));
    })
  }


  public generateChart(): void {
    this.loadingChart = true;
    this.buttonPressed = true;

    let newTitle = this.selectedStatistic.chartTitle;
    let subTitle = this.getDateRangeEnd().toLocaleDateString(this.locale);

    let unit = this.localizationService.instant('::Chart:assets');

    if (this.selectedStatistic.usesDateRange) {
      subTitle = `${this.getDateRangeBegin().toLocaleDateString(this.locale)} - ${this.getDateRangeEnd().toLocaleDateString(this.locale)}`;
      unit = this.localizationService.instant('::Chart:hours');
    }

    if (this.isCurrentDate(this.getDateRangeEnd())) {
      subTitle = `${subTitle} (${this.localizationService.instant('::Chart:asOf')} ${this.getCurrentDate().toLocaleTimeString(this.locale)})`;
    }

    this.generateQuery().subscribe((data) => {
      console.log(data);
      this.loadedData = data.map(x => ({ name: x.bin, value: x.value }))
      this.formatChartOptions(newTitle, subTitle, this.localizationService.instant('::Chart:location'), unit);
      this.formatChartData();
      this.chart.update();
      this.loadingChart = false;
    })

  }

  public getCurrentDate(): Date {
    return new Date();
  }

  private getDateRangeBegin(): Date {
    return this.dateRange.controls.start.value ?? this.getCurrentDate();
  }

  private getDateRangeEnd(): Date {
    return this.dateRange.controls.end.value ?? this.getCurrentDate();
  }

  private getInclusiveDateRangeEnd(): Date {
    if (this.dateRange.controls.end.value == null) {
      return this.getCurrentDate();
    }
    else {
      const msPerDay = (24 * 60 * 60 * 1000);
      let tomorrow = new Date(this.dateRange.controls.end.value.valueOf() + msPerDay);
      return new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate());
    }
  }

  private isCurrentDate(date: Date): boolean {
    let present = this.getCurrentDate();

    return date.getFullYear() == present.getFullYear()
      && date.getMonth() == present.getMonth()
      && date.getDate() == present.getDate();
  }

  private generateQuery(): Observable<BinnedQuantityDto[]> {

    let query: LocationReportQueryDto = {
      rangeBegin: this.getDateRangeBegin().toUTCString(),
      rangeEnd: this.getInclusiveDateRangeEnd().toUTCString(),
      locationIds: this.selectedGeofences
    };

    return this.selectedStatistic.sendQuery(query);
  }

  private configureChart(): void {
    this.chart = new Chart(this.canvasRef.nativeElement, {
      type: 'bar',
      plugins: [ChartDataLabels],
      options: {
        animation: false,
        plugins: {
          title: {
            display: true,
            text: "{Placeholder}",
            align: 'center',
            font: {
              size: 20,
              weight: 'bold'
            }
          },
          subtitle: {
            display: true,
            text: "{Placeholder}",
            align: 'center',
            font: {
              size: 15,
              style: 'italic'
            },
            padding: {
              bottom: 20
            }
          },
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          },
          datalabels: {
            anchor: 'end',
            align: 'top',
            font: {
              weight: 'bold'
            }
          }
        },
        scales: {
          x: {
            display: true
          },
          y: {
            display: true
          }
        }
      },
      data: {
        labels: this.loadedData.map(row => row.name),
        datasets: [
          {
            label: '{Placeholder}',
            data: this.loadedData.map(row => row.value)
          }
        ]
      }
    });
  }

  private formatChartOptions(title: string, subTitle: string, xAxis: string, yAxis: string): void {
    this.chart.options.plugins.title.text = title;
    this.chart.options.plugins.subtitle.text = subTitle;

    this.chart.options.scales['x'] = {
      title: {
        display: true,
        text: xAxis,
        font: {
          size: 15,
          weight: 'bold'
        }
      }
    };

    // solve for a "nice" amount by which to divide the Y-axis with a slightly-inflated maximum
    let maxValue = Math.ceil(Math.max(...this.loadedData.map(x => x.value))) * 1.1667;
    let niceDiv = Math.max(5, Math.pow(10, Math.floor(Math.log10(maxValue))) / 4);
    let yAxisMax = Math.ceil(maxValue / niceDiv) * niceDiv;
    console.log(`Max ${maxValue} -> Max ${yAxisMax} (Divisor ${niceDiv})`);

    this.chart.options.scales['y'] = {
      title: {
        display: true,
        text: yAxis,
        font: {
          size: 15,
          weight: 'bold'
        }
      },
      ticks: {
        precision: 0,
        stepSize: niceDiv
      },
      suggestedMin: 0,
      max: yAxisMax
    };
  }

  private formatChartData(): void {
    this.chart.data.labels = this.loadedData.map(x => x.name);
    this.chart.data.datasets[0].data = this.loadedData.map(x => Math.floor(x.value));
  }
}

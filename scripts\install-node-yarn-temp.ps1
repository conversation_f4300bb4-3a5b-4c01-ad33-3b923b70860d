# scripts/install-node-yarn-temp.ps1

# Ensure TLS 1.2 is used for secure HTTPS connections (fixes cert errors)
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

# Clean Angular install
Remove-Item -Recurse -Force "angular/yarn.lock" -ErrorAction SilentlyContinue

# Define Node.js version and paths
$nodeVersion = 'v20.14.0'
$nodeDir = "$env:USERPROFILE\node-$nodeVersion"
$nodeBin = Join-Path $nodeDir "node-$nodeVersion-win-x64"

# Remove old temp Node.js folder
if (Test-Path $nodeDir) {
    Write-Host "Removing old node directory: $nodeDir"
    Get-ChildItem $nodeDir -Recurse -Force | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
    Remove-Item $nodeDir -Force -ErrorAction SilentlyContinue
}

# Download and install Node.js if not already in cache
if (!(Test-Path $nodeBin)) {
    Write-Host "Installing Node.js $nodeVersion in temp folder..."
    $zipUrl = "https://nodejs.org/dist/$nodeVersion/node-$nodeVersion-win-x64.zip"
    $zipPath = "$env:TEMP\node.zip"
    Invoke-WebRequest -Uri $zipUrl -OutFile $zipPath
    Expand-Archive -Path $zipPath -DestinationPath $nodeDir -Force
    Write-Host "Node extracted to $nodeDir"
} else {
    Write-Host "Node already installed in cache."
}

# Confirm node binary
if (!(Test-Path $nodeBin)) {
    Write-Error "Node binary folder not found at $nodeBin"
    exit 1
}

if (!(Test-Path $nodeBin)) {
    Write-Error "Node binary folder not found at $nodeBin"
    exit 1
}

# Add Node.js to path
$env:Path = "$nodeBin;$env:Path"

# Configure NPM
node -v
npm -v

# Install Yarn only if not present
if (-not (Get-Command yarn -ErrorAction SilentlyContinue)) {
    Write-Host "Yarn not found. Installing Yarn..."
    npm install -g yarn
} else {
    Write-Host "Yarn is already installed."
}

Write-Host "Yarn version:"
yarn -v

# Go to Angular project
Set-Location angular
$envFilePath = ".env"

# Setup .env file
$sourceEnvFile = ".env.development"
$targetEnvFile = ".env"

if (Test-Path $sourceEnvFile) {
    Copy-Item -Path $sourceEnvFile -Destination $targetEnvFile -Force
    Write-Host "$sourceEnvFile copied to $targetEnvFile"
} else {
    Write-Warning "$sourceEnvFile not found, skipping .env setup"
}

# Set Yarn registry (to avoid local cert issues)
yarn config set registry https://registry.npmjs.org

# Clean Yarn cache and install
yarn cache clean
yarn install --ignore-optional

# Force install esbuild in case Angular doesn't pull it correctly
Write-Host "Ensuring 'esbuild' is installed explicitly..."
yarn add -D esbuild

# Build frontend
Write-Host "Running frontend build..."
yarn run build:prod --output-path ../publish/angular

# Return to root before running ABP CLI
Set-Location ..
#Set-Location aspnet-core

# (fixes local issuer cert errors)
# use cert
$env:NODE_EXTRA_CA_CERTS = "C:\certs\umbrella-root.cer"

# Run ABP CLI install-libs command
Write-Host "Running ABP CLI install-libs..."
& "$env:USERPROFILE\.dotnet\tools\abp.exe" install-libs

# Return to root
#Set-Location ..
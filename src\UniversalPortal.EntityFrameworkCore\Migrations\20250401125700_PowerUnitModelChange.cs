﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class PowerUnitModelChange : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Geolocations_PowerUnits_PowerUnitId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropIndex(
                name: "IX_Geolocations_PowerUnitId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "PowerUnitId",
                schema: "ControlTower",
                table: "Geolocations");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PowerUnitId",
                schema: "ControlTower",
                table: "Geolocations",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Geolocations_PowerUnitId",
                schema: "ControlTower",
                table: "Geolocations",
                column: "PowerUnitId");

            migrationBuilder.AddForeignKey(
                name: "FK_Geolocations_PowerUnits_PowerUnitId",
                schema: "ControlTower",
                table: "Geolocations",
                column: "PowerUnitId",
                principalSchema: "ControlTower",
                principalTable: "PowerUnits",
                principalColumn: "PowerUnitId");
        }
    }
}

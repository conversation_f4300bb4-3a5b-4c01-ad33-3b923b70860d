
  @if(debugColumns?.length > 0) {
  <div>
    Last Refresh: {{ lastRefresh | date: 'hh:mm:ss.SSS' }}
  </div>
  }

<div #tableContainerRef
     class="table-container">
    <div class="mat-elevation-z8">
    <table mat-table #table
            role="grid"
            matSort
            (matSortChange)="sortChange($event)">
      
      @for (column of debugColumns; track column) {
        <ng-container [matColumnDef]="column" sticky="false">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            {{ formatter.formatFieldName(column) }}
          </th>
          <td mat-cell *matCellDef="let record"
              [@highlightChanges]="record.lastUpdated.getTime()"
              [@highlightNew]="record.importedTime?.getTime() > lastRefresh?.getTime()">
            <span class="cell-text"
                  [@gridFade]="paginator.pageIndex">
              {{ formatter.formatField(column, record) }}
            </span>
          </td>
        </ng-container>
      }
      
      @for (column of stickyColumns; track column) {
        <ng-container [matColumnDef]="column" sticky="true" class="double-sticky">
          <th mat-header-cell *matHeaderCellDef mat-sort-header
              sortActionDescription="{{ '::Table:sortBy' | abpLocalization }} '{{ formatter.formatFieldName(column) }}'">
            {{ formatter.formatFieldName(column) }}
          </th>
          <td mat-cell *matCellDef="let record"
              [@highlightChanges]="record.lastUpdated.getTime()"
              [@highlightNew]="record.importedTime?.getTime() > lastRefresh?.getTime()">
            <span class="cell-text"
                  [@gridFade]="paginator.pageIndex">
              {{ formatter.formatField(column, record) }}
            </span>
          </td>
        </ng-container>
      }

      <!-- Generic "inspect" column displayed after sticky columns -->
      <ng-container [matColumnDef]="'inspectButtonColumn'" sticky="true" class="double-sticky">
        <th mat-header-cell *matHeaderCellDef>
          <mat-icon>search</mat-icon>
        </th>
        <td mat-cell *matCellDef="let record"
            [@highlightChanges]="record.lastUpdated.getTime()"
            [@highlightNew]="record.importedTime?.getTime() > lastRefresh?.getTime()">
          <span class="cell-text"
                [@gridFade]="paginator.pageIndex">
            <button mat-icon-button arial-label="Inspect order #{{ record.orderNumber }}"
                    (click)="inspectRecord(record)">
              <mat-icon class="inline-button">expand_circle_down</mat-icon>
            </button>
          </span>
        </td>
      </ng-container>

      @for (column of regularColumns; track column) {
        <ng-container [matColumnDef]="column" sticky="false">
          <th mat-header-cell *matHeaderCellDef mat-sort-header
              sortActionDescription="{{ '::Table:sortBy' | abpLocalization }} '{{ formatter.formatFieldName(column) }}'">
            {{ formatter.formatFieldName(column) }}
          </th>
          <td mat-cell *matCellDef="let record"
              [@highlightChanges]="record.lastUpdated.getTime()"
              [@highlightNew]="record.importedTime?.getTime() > lastRefresh?.getTime()">
            <span class="cell-text"
                  [@gridFade]="paginator.pageIndex">
              {{ formatter.formatField(column, record) }}
            </span>
          </td>
        </ng-container>
      }

      <!-- Header definitions NEED to be explicitly marked sticky so as not to conflict with sticky columns -->
      <tr mat-header-row *matHeaderRowDef="allColumns; sticky: true"> </tr>

      <tr mat-row *matRowDef="let row; columns: allColumns;">
      </tr>

      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" [attr.colspan]="allColumns.length">
          No data available.
        </td>
      </tr>

    </table>
  </div>

</div>
    

<div class="dash-footer">
  <mat-paginator #paginator
                  [pageSizeOptions]="[5, 10, 20, 50, 100]"
                  showFirstLastButtons
                  (page)="pageChange($event)"
                  aria-label="Select page of orders">
  </mat-paginator>
</div>

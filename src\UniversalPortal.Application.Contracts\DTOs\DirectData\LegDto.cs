﻿using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class LegDto : AuditedEntityDto
{
    public LegDto()
    {
        Stops = [];
    }

    public int LegId { get; set; }
    public int LegNumber { get;set; }
    public int MoveNumber {  get; set; }
    public int? Driver1Id { get; set; }
    public int? Driver2Id { get; set; }
    public int? PowerUnitId { get; set; }

    public ICollection<StopDto> Stops { get; set; }    
    public DriverDto? Driver1 { get; set; }
    public DriverDto? Driver2 { get; set; }
    public PowerUnitDto? PowerUnit { get; set; }
}
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { PowerUnitDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class PowerUnitService {
  apiName = 'Default';
  

  create = (input: PowerUnitDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PowerUnitDto>({
      method: 'POST',
      url: '/api/app/power-unit',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/power-unit/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PowerUnitDto>({
      method: 'GET',
      url: `/api/app/power-unit/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<PowerUnitDto>>({
      method: 'GET',
      url: '/api/app/power-unit',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: PowerUnitDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PowerUnitDto>({
      method: 'PUT',
      url: `/api/app/power-unit/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

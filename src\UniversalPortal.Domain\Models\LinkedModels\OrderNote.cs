﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.DirectModels;
using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

public class OrderNote : AuditedEntity
{
    [Key, Column(Order = 0)]
    public int OrderId { get; set; }
    [Key, Column(Order = 1)]
    public int NoteId { get; set; }
    [InverseProperty(nameof(Order.OrderNotes))]
    public Order? Order { get; set; }
    [InverseProperty(nameof(Note.OrderNotes))]
    public Note? Note { get; set; }

    public override object[] GetKeys()
    {
        return [OrderId, NoteId];
    }
}
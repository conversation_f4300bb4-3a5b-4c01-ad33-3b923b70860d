﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using UniversalPortal.DTOs.Queries;
using UniversalPortal.DTOs.SemanticData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.Models.SemanticModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class ReportingService(IRepository<Geofence> geofenceRepository, IRepository<Dwell> dwellRepository) : ApplicationService, IReportingService
{
    private const double SECONDS_PER_HOUR = 60.0 * 60.0;

    public async Task<ICollection<BinnedQuantityDto>> GetAverageDwellTimeByLocationAsync(LocationReportQueryDto input)
    {
        var dwells = await dwellRepository.GetQueryableAsync();
        var fences = await geofenceRepository.GetQueryableAsync();

        var query = from f in fences
                    where input.LocationIds.Contains(f.GeofenceId)
                    join d in dwells
                        on f.GeofenceId equals d.GeofenceId
                        into fd
                    from t in fd.DefaultIfEmpty()
                    let dwellBegin = t == null
                        ? default
                        : t.CreationTime < input.RangeBegin
                            ? input.RangeBegin
                            : t.CreationTime > input.RangeEnd
                            ? input.RangeEnd
                            : t.CreationTime 
                    let dwellEnd = t == null
                        ? default
                        : t.LastModificationTime == null
                            ? input.RangeEnd
                            : t.LastModificationTime > input.RangeEnd
                                ? input.RangeEnd
                                : t.LastModificationTime
                    where t == null
                        || (input.RangeBegin <= dwellBegin && dwellBegin <= input.RangeEnd)
                        || (input.RangeBegin <= dwellEnd && dwellEnd <= input.RangeEnd)
                    let dwellHours = (EF.Functions.DateDiffSecond(dwellBegin, dwellEnd) / SECONDS_PER_HOUR) ?? 0
                    group dwellHours
                        by f.GeofenceName
                        into grp
                    select new BinnedQuantity()
                    {
                        Bin = grp.Key,
                        Value = grp.Average()
                    };

        var result = await query.AsSingleQuery().ToListAsync();
        return ObjectMapper.Map<List<BinnedQuantity>, List<BinnedQuantityDto>>(result);
    }

    public async Task<ICollection<BinnedQuantityDto>> GetTrailerCountByLocationAsync(LocationReportQueryDto input)
    {
        var dwells = await dwellRepository.GetQueryableAsync();
        var fences = await geofenceRepository.GetQueryableAsync();

        var query = from f in fences
                    where input.LocationIds.Contains(f.GeofenceId)
                    join d in dwells
                        on f.GeofenceId equals d.GeofenceId
                        into fd
                    from t in fd.DefaultIfEmpty()
                    where t == null || !(t.LastModificationTime > input.RangeEnd)
                    group t
                        by f.GeofenceName
                        into grp
                    select new BinnedQuantity()
                    {
                        Bin = grp.Key,
                        Value = grp.Count()
                    };

        var result = await query.AsSingleQuery().ToListAsync();
        return ObjectMapper.Map<List<BinnedQuantity>, List<BinnedQuantityDto>>(result);
    }

    public async Task<ICollection<BinnedQuantityDto>> GetInactiveAssetsByLocationAsync(LocationReportQueryDto input, int inactiveHours)
    {
        DateTime threshold = input.RangeEnd.AddHours(-1 * inactiveHours);

        var dwells = await dwellRepository.GetQueryableAsync();
        var fences = await geofenceRepository.GetQueryableAsync();

        var query = from f in fences
                    where input.LocationIds.Contains(f.GeofenceId)
                    join d in dwells
                        on f.GeofenceId equals d.GeofenceId
                        into fd
                    from t in fd.DefaultIfEmpty()
                    where t == null || !(t.LastModificationTime > input.RangeEnd)
                    where t == null || t.CreationTime <= threshold
                    group t
                        by f.GeofenceName
                        into grp
                    select new BinnedQuantity()
                    {
                        Bin = grp.Key,
                        Value = grp.Count()
                    };

        var result = await query.AsSingleQuery().ToListAsync();
        return ObjectMapper.Map<List<BinnedQuantity>, List<BinnedQuantityDto>>(result);
    }

    public async Task<ICollection<BinnedQuantityDto>> GetAverageHoursSinceMoveByLocationAsync(LocationReportQueryDto input)
    {
        var dwells = await dwellRepository.GetQueryableAsync();
        var fences = await geofenceRepository.GetQueryableAsync();

        var query = from f in fences
                    where input.LocationIds.Contains(f.GeofenceId)
                    join d in dwells
                        on f.GeofenceId equals d.GeofenceId
                        into fd
                    from t in fd.DefaultIfEmpty()
                    where t == null || !(t.LastModificationTime > input.RangeEnd) && t.CreationTime < input.RangeEnd
                    let hoursSinceMove = t == null
                        ? 0
                        : EF.Functions.DateDiffSecond(t.CreationTime, t.LastModificationTime ?? input.RangeEnd) / SECONDS_PER_HOUR
                    group hoursSinceMove
                        by f.GeofenceName
                        into grp
                    select new BinnedQuantity()
                    {
                        Bin = grp.Key,
                        Value = grp.Average()
                    };

        var result = await query.AsSingleQuery().ToListAsync();
        return ObjectMapper.Map<List<BinnedQuantity>, List<BinnedQuantityDto>>(result);
    }
}
﻿using UniversalPortal.Models;

namespace UniversalPortal.DTOs.DirectData;

public class GeofenceDto
{
    public int GeofenceId { get; set; }
    public int GeolocationId {  get; set; }
    public string GeofenceName { get; set; } = string.Empty;
    public int AddressId {  get; set; }
    public string GeofenceCoordinates { get; set; } = string.Empty;
    public GeolocationCoordinate? Center { get ;set ; }
}
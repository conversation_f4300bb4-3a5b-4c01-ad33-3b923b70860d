import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { EquipmentTypeDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class EquipmentTypeService {
  apiName = 'Default';
  

  create = (input: EquipmentTypeDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EquipmentTypeDto>({
      method: 'POST',
      url: '/api/app/equipment-type',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/equipment-type/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EquipmentTypeDto>({
      method: 'GET',
      url: `/api/app/equipment-type/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<EquipmentTypeDto>>({
      method: 'GET',
      url: '/api/app/equipment-type',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: EquipmentTypeDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EquipmentTypeDto>({
      method: 'PUT',
      url: `/api/app/equipment-type/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Equipment : TenantAuditedEntity
{

    public Equipment()
    {
        StopEquipment = [];
        EquipmentGeolocations = [];
        Dwells = [];
    }

    [Key]
    public int EquipmentId { get; set; }
    public string EquipmentNumber { get; set; } = string.Empty;
    public int EquipmentTypeId { get; set; }
    public string Vin { get; set; } = string.Empty;
    public DateTime ExpirationDate { get; set; }
    public string? Description { get; set; }
    [InverseProperty(nameof(LinkedModels.StopEquipment.Equipment))]
    public ICollection<StopEquipment> StopEquipment { get; set; }
    [InverseProperty(nameof(DirectModels.EquipmentType.Equipment))]
    public EquipmentType? EquipmentType { get; set; }
    [InverseProperty(nameof(DirectModels.EquipmentType.Equipment))]
    public ICollection<EquipmentGeolocation> EquipmentGeolocations { get; set; }
    [InverseProperty(nameof(Dwell.Equipment))]
    public ICollection<Dwell> Dwells { get; set; }

    public override object[] GetKeys()
    {
        return [EquipmentId];
    }
}
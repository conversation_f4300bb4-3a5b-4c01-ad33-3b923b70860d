import { authGuard, permissionGuard } from '@abp/ng.core';
import { NgModule } from '@angular/core';
import { RouterModule, Routes, TitleStrategy } from '@angular/router';
import { IntlTitleStrategy } from './shared/services/intl-title.strategy';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'dashboard'
  },
  //{
  //  path: 'account',
  //  loadChildren: () => import('@abp/ng.account').then(m => m.AccountModule.forLazy()),
  //},
  {
    path: 'dashboard',
    title: '::Page:dashboard',
    loadChildren: () => import('./dashboard/host-dashboard/dashboard.module').then(m => m.DashboardModule),
  },
  {
    path: 'graphMenu',
    title: '::Page:graphMenu',
    loadChildren: () => import('./dashboard/graph-menu/graph-menu.module').then(m => m.GraphMenuModule),
  }
  //{
  //  path: 'identity',
  //  loadChildren: () => import('@abp/ng.identity').then(m => m.IdentityModule.forLazy()),
  //},
  //{
  //  path: 'tenant-management',
  //  loadChildren: () =>
  //    import('@abp/ng.tenant-management').then(m => m.TenantManagementModule.forLazy()),
  //},
  //{
  //  path: 'setting-management',
  //  loadChildren: () =>
  //    import('@abp/ng.setting-management').then(m => m.SettingManagementModule.forLazy()),
  //},
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {})],
  exports: [RouterModule]
})
export class AppRoutingModule {}

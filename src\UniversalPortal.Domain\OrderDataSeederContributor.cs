﻿using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using UniversalPortal.Models.DirectModels;

namespace UniversalPortal
{
    internal class OrderDataSeederContributor(IRepository<Order> orderRepository) :
        IDataSeedContributor,
        ITransientDependency
    {
        public async Task SeedAsync(DataSeedContext context)
        {
            if (await orderRepository.GetCountAsync() <= 0)
            {
                //await _orderRepository.InsertAsync(_exampleOrder1);
                //await _orderRepository.InsertAsync(_exampleOrder2);

                //IEnumerable<Order> exampleOrders = ParseCsvInput();

                //await _orderRepository.InsertManyAsync(exampleOrders);
            }
        }

        //private static readonly Order _exampleOrder1 = new()
        //{
        //    OrderNumber = 6016642,
        //    Date = new DateTime(2024, 10, 7),
        //    Route = "200M",
        //    OrderMiles = 473,
        //    StopSequencing = 1,
        //    StopMileage = 0,
        //    SupplierId = "GMSSAN01",
        //    Supplier = "GM SODC GM SODC",
        //    Count = 1,

        //    ScheduledArrival = new DateTime(2024, 10, 7, 5, 20, 0),
        //    ActualArrival = new DateTime(2024, 10, 7, 1, 32, 0),
        //    ArrivalStatus = ItinerantStatus.Early,
        //    LateArrivalReason = ItinerantLateReason.Unknown,

        //    ScheduledDeparture = new DateTime(2024, 10, 7, 5, 50, 0),
        //    ActualDeparture = new DateTime(2024, 10, 7, 1, 33, 0),
        //    DepartureStatus = ItinerantStatus.Early,
        //    LateDepartureReason = ItinerantLateReason.Unknown,

        //    UniversalLateDeparture = 0,
        //    MinutesWaiting = 1
        //};

        //private static readonly Order _exampleOrder2 = new()
        //{
        //    OrderNumber = 6016728,
        //    Date = new DateTime(2024, 10, 8),
        //    Route = "240T",
        //    OrderMiles = 508,
        //    StopSequencing = 4,
        //    StopMileage = 19,
        //    SupplierId = "MACTEN01",
        //    Supplier = "MACIMEX, S.A. DE C.V. 8109538",
        //    Count = 1,

        //    ScheduledArrival = new DateTime(2024, 10, 8, 14, 0, 0),
        //    ActualArrival = new DateTime(2024, 10, 7, 15, 15, 0),
        //    ArrivalStatus = ItinerantStatus.Late,
        //    LateArrivalReason = ItinerantLateReason.FivePENCarrierDelay,

        //    ScheduledDeparture = new DateTime(2024, 10, 8, 15, 0, 0),
        //    ActualDeparture = new DateTime(2024, 10, 7, 16, 55, 0),
        //    DepartureStatus = ItinerantStatus.Late,
        //    LateDepartureReason = ItinerantLateReason.FivePENCarrierDelay,

        //    UniversalLateDeparture = 0,
        //    MinutesWaiting = 100
        //};

        //private static readonly string _csvInput = "./SeedData.csv";

        //private static IEnumerable<Order> ParseCsvInput()
        //{
        //    using (TextFieldParser parser = new TextFieldParser(_csvInput))
        //    {
        //        parser.HasFieldsEnclosedInQuotes = true;
        //        parser.SetDelimiters(",");

        //        parser.ReadLine(); //skip header row

        //        while (!parser.EndOfData)
        //        {
        //            if (parser.ReadFields() is string[] row)
        //            {
        //                yield return ParseCsvRow(row);
        //            }
        //        }
        //    }
        //}

        //private static Order ParseCsvRow(string[] row)
        //{
        //    int i = 0;
        //    return new Order()
        //    {
        //        OrderNumber = int.Parse(row[i++]),
        //        Date = DateTime.Parse(row[i++]),
        //        Route = row[i++],
        //        OrderMiles = int.Parse(row[i++]),
        //        StopSequencing = int.Parse(row[i++]),
        //        StopMileage = int.Parse(row[i++]),
        //        SupplierId = row[i++],
        //        Supplier = row[i++],
        //        Count = int.Parse(row[i++]),

        //        ScheduledArrival = DateTime.Parse(row[i++]),
        //        ActualArrival = DateTime.Parse(row[i++]),
        //        ArrivalStatus = (ItinerantStatus)int.Parse(row[i++]),
        //        LateArrivalReason = (ItinerantLateReason)int.Parse(row[i++]),

        //        ScheduledDeparture = DateTime.Parse(row[i++]),
        //        ActualDeparture = DateTime.Parse(row[i++]),
        //        DepartureStatus = (ItinerantStatus)int.Parse(row[i++]),
        //        LateDepartureReason = (ItinerantLateReason)int.Parse(row[i++]),

        //        UniversalLateDeparture = int.Parse(row[i++]),
        //        MinutesWaiting = int.Parse(row[i++])
        //    };
        //}
    }
}

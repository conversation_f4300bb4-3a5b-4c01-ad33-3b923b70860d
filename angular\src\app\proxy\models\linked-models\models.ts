import type { AuditedEntity } from '../../volo/abp/domain/entities/auditing/models';
import type { Address, Contact, Equipment, Geolocation, Note, Order, PowerUnit, Stop } from '../direct-models/models';

export interface AddressContact extends AuditedEntity {
  addressId: number;
  contactId: number;
  address: Address;
  contact: Contact;
}

export interface EquipmentGeolocation extends AuditedEntity {
  equipmentId: number;
  geolocationId: number;
  equipment: Equipment;
  geolocation: Geolocation;
}

export interface OrderContact extends AuditedEntity {
  orderId: number;
  contactId: number;
  order: Order;
  contact: Contact;
}

export interface OrderNote extends AuditedEntity {
  orderId: number;
  noteId: number;
  order: Order;
  note: Note;
}

export interface PowerUnitGeolocation extends AuditedEntity {
  powerUnitId: number;
  geolocationId: number;
  powerUnit: PowerUnit;
  geolocations: Geolocation;
}

export interface StopContact extends AuditedEntity {
  stopId: number;
  contactId: number;
  stop: Stop;
  contact: Contact;
}

export interface StopEquipment extends AuditedEntity {
  stopId: number;
  equipmentId: number;
  stop: Stop;
  equipment: Equipment;
}

export interface StopNote extends AuditedEntity {
  stopId: number;
  noteId: number;
  stop: Stop;
  note: Note;
}

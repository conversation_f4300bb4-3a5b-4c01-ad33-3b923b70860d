﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class EquipmentType : TenantAuditedEntity 
{
    public EquipmentType()
    {
        Equipment = [];
    }

    [Key]
    public int EquipmentTypeId { get; set; }
    public required string EquipmentTypeName { get; set; }
    public string? Description { get; set; }
    [InverseProperty(nameof(DirectModels.Equipment.EquipmentType))]
    public ICollection<Equipment> Equipment { get; set; }

    public override object[] GetKeys()
    {
        return [EquipmentTypeId];
    }
}
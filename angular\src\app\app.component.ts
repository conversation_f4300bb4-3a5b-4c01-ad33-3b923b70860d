import { Component } from '@angular/core';
import { ReplaceableComponentsService } from '@abp/ng.core'; // imported ReplaceableComponentsService
import { eThemeLeptonXComponents } from '@abp/ng.theme.lepton-x';   // imported eThemeLeptonXComponents enum
import { EmptyComponent } from './UI/empty/empty.component';
import { LogoComponent } from './UI/logo/logo.component';
import { environment } from '../environments/environment';


@Component({
  selector: 'app-root',
  template: `
    <abp-loader-bar></abp-loader-bar>
    <abp-dynamic-layout></abp-dynamic-layout>
  `,
})
export class AppComponent {

  private static thingsToRemove: eThemeLeptonXComponents[] = [
    eThemeLeptonXComponents.Breadcrumb,
    //eThemeLeptonXComponents.Languages,
    eThemeLeptonXComponents.Login,
    eThemeLeptonXComponents.CurrentUser,
    eThemeLeptonXComponents.CurrentUserImage,
    eThemeLeptonXComponents.Toolbar,
    eThemeLeptonXComponents.Footer
  ];


  constructor(
    private replaceableComponents: ReplaceableComponentsService
  )
  {
    // disable console logging if not running in debug mode
    if (!environment.debug) {
      console.log = function () { };
    }

    AppComponent.thingsToRemove.forEach((component) => {
      this.replaceableComponents.add({
        component: EmptyComponent,
        key: component
      });
    });

    this.replaceableComponents.add({
      component: LogoComponent,
      key: eThemeLeptonXComponents.Logo
    });

  }
}

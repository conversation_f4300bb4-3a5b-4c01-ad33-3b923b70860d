// to override with specified environment run
// ng build --configuration=<config-name>

import { Environment } from '@abp/ng.core';

const baseUrl = 'http://localhost:4200';

const oAuthConfig = {
  issuer: 'https://localhost:44369/',
  redirectUri: baseUrl,
  clientId: 'UniversalPortal_App',
  responseType: 'code',
  scope: 'offline_access UniversalPortal',
  requireHttps: true,
};

export const environment = {
  production: false,
  debug: true,
  application: {
    baseUrl,
    name: 'UniversalPortal',
  },
  mapTiles: {
    styleUrl: 'https://tiles.openfreemap.org/styles/bright',
    ToS: 'https://openfreemap.org/tos/'
  },
  oAuthConfig,
  apis: {
    default: {
      url: 'https://localhost:44369',
      rootNamespace: 'UniversalPortal',
    },
    AbpAccountPublic: {
      url: oAuthConfig.issuer,
      rootNamespace: 'AbpAccountPublic',
    },
  },
} as Environment;

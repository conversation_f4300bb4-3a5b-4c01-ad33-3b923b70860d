<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
    <RootNamespace>UniversalPortal</RootNamespace>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(RunConfiguration)' == 'UniversalPortal.HttpApi.Host' " />

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.SignalR" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.MultiTenancy" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="4.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UniversalPortal.Application\UniversalPortal.Application.csproj" />
    <ProjectReference Include="..\UniversalPortal.HttpApi\UniversalPortal.HttpApi.csproj" />
    <ProjectReference Include="..\UniversalPortal.EntityFrameworkCore\UniversalPortal.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Studio.Client.AspNetCore" Version="0.9.23" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

</Project>

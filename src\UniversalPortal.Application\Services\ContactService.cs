﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class ContactService(IRepository<Contact> repository) : ApplicationService, IContactService
{
    public async Task<ContactDto> CreateAsync(ContactDto input)
    {
        var driver = ObjectMapper.Map<ContactDto, Contact>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Contact, ContactDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.ContactId == id);
    }

    public async Task<ContactDto> GetAsync(int id)
    {
        var driver = await repository.GetAsync(x => x.ContactId == id);
        return ObjectMapper.Map<Contact, ContactDto>(driver);
    }

    public async Task<PagedResultDto<ContactDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var driver = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<ContactDto>(
            totalCount,
            ObjectMapper.Map<List<Contact>, List<ContactDto>>(driver)
        );
    }

    public Task<ContactDto> UpdateAsync(int id, ContactDto input)
    {
        throw new NotImplementedException();
    }
}
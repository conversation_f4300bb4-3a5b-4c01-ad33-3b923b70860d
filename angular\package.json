{"name": "UniversalPortal", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@abp/ng.account": "~9.0.4", "@abp/ng.components": "~9.0.4", "@abp/ng.core": "~9.0.4", "@abp/ng.feature-management": "~9.0.4", "@abp/ng.identity": "~9.0.4", "@abp/ng.oauth": "~9.0.4", "@abp/ng.setting-management": "~9.0.4", "@abp/ng.tenant-management": "~9.0.4", "@abp/ng.theme.lepton-x": "~4.0.5", "@abp/ng.theme.shared": "~9.0.4", "@abp/signalr": "^9.0.6", "@angular/animations": "~18.1.0", "@angular/cdk": "^17.0.0", "@angular/common": "~18.1.0", "@angular/compiler": "~18.1.0", "@angular/core": "~18.1.0", "@angular/forms": "~18.1.0", "@angular/localize": "~18.1.0", "@angular/material": "^17.0.0", "@angular/platform-browser": "~18.1.0", "@angular/platform-browser-dynamic": "~18.1.0", "@angular/router": "~18.1.0", "@ngx-env/builder": "^18.0.1", "@swimlane/ngx-charts": "^22.0.0", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "maplibre-gl": "^5.4.0", "rxjs": "~7.8.0", "tslib": "^2.0.0", "zone.js": "~0.14.0"}, "devDependencies": {"@abp/ng.schematics": "~9.0.4", "@angular-devkit/build-angular": "~18.1.0", "@angular-eslint/builder": "~18.1.0", "@angular-eslint/eslint-plugin": "~18.1.0", "@angular-eslint/eslint-plugin-template": "~18.1.0", "@angular-eslint/schematics": "~18.1.0", "@angular-eslint/template-parser": "~18.1.0", "@angular/cli": "~18.1.0", "@angular/compiler-cli": "~18.1.0", "@angular/language-service": "~18.1.0", "@types/jasmine": "~3.6.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.0.0", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.0.0", "typescript": "~5.5.0"}}
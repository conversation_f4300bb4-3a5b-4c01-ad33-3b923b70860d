﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class SharedAddressTableRequiresLinkTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contacts_Addresses_AddressId",
                schema: "ControlTower",
                table: "Contacts");

            migrationBuilder.DropIndex(
                name: "IX_Contacts_AddressId",
                schema: "ControlTower",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "AddressId",
                schema: "ControlTower",
                table: "Contacts");

            migrationBuilder.CreateTable(
                name: "AddressContact",
                schema: "dbo",
                columns: table => new
                {
                    AddressId = table.Column<int>(type: "int", nullable: false),
                    ContactId = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AddressContact", x => new { x.AddressId, x.ContactId });
                    table.ForeignKey(
                        name: "FK_AddressContact_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalSchema: "ControlTower",
                        principalTable: "Addresses",
                        principalColumn: "AddressId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AddressContact_Contacts_ContactId",
                        column: x => x.ContactId,
                        principalSchema: "ControlTower",
                        principalTable: "Contacts",
                        principalColumn: "ContactId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AddressContact_ContactId",
                schema: "dbo",
                table: "AddressContact",
                column: "ContactId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AddressContact",
                schema: "dbo");

            migrationBuilder.AddColumn<int>(
                name: "AddressId",
                schema: "ControlTower",
                table: "Contacts",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Contacts_AddressId",
                schema: "ControlTower",
                table: "Contacts",
                column: "AddressId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contacts_Addresses_AddressId",
                schema: "ControlTower",
                table: "Contacts",
                column: "AddressId",
                principalSchema: "ControlTower",
                principalTable: "Addresses",
                principalColumn: "AddressId");
        }
    }
}

import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { NoteDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class NoteService {
  apiName = 'Default';
  

  create = (input: NoteDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, NoteDto>({
      method: 'POST',
      url: '/api/app/note',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/note/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, NoteDto>({
      method: 'GET',
      url: `/api/app/note/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<NoteDto>>({
      method: 'GET',
      url: '/api/app/note',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: NoteDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, NoteDto>({
      method: 'PUT',
      url: `/api/app/note/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

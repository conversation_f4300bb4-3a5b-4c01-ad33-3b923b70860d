﻿@page
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.AspNetCore.Http.Extensions
@using UniversalPortal.Localization
@using UniversalPortal.Pages
@using Volo.Abp.Account.Localization
@using Volo.Abp.Users
@using Volo.Abp.AspNetCore.Mvc.UI.Theming
@using Volo.Abp.Ui.Branding
@model IndexModel
@inject IHtmlLocalizer<CtpLocalizationResource> L
@inject IHtmlLocalizer<AccountResource> AccountLocalizer
@inject ICurrentUser CurrentUser
@inject IBrandingProvider BrandingProvider
@inject ITheme Theme
@{
    Layout = Theme.GetEmptyLayout();
}

<div class="d-flex align-items-center" style="min-height: 100vh;">
    <div class="container">
        <abp-row>

            <div class="col mx-auto account-column">
                <div class="account-brand p-4 text-center mb-1">

                    @if (!BrandingProvider.LogoUrl.IsNullOrEmpty())
                    {
                        <a class="navbar-brand" href="~/" alt="@BrandingProvider.AppName"></a>
                    }
                    else
                    {
                        <h1>@BrandingProvider.AppName</h1>
                    }
                </div>
                <abp-card class="border rounded">

                    <abp-card-body>

                        <div class="container">
                            <abp-row>
                                <abp-column size="_9">

                                    <div class="mr-auto p-2 float-start">
                                        @if (CurrentUser.IsAuthenticated)
                                        {
                                            <div>
                                                <div>
                                                    <a abp-button="Outline_Primary" asp-controller="Manage" asp-action="Index" asp-area="Account" class="me-2">@AccountLocalizer["MyAccount"]</a>
                                                    <a abp-button="Primary" asp-controller="Logout" asp-action="Index" asp-area="Account">@L["Logout"]</a>
                                                </div>
                                            </div>

                                        }
                                        else
                                        {
                                            <div class="text-center">
                                                <a abp-button="Primary" asp-controller="Login" asp-action="Index" asp-area="Account">@L["Login"]</a>
                                            </div>
                                        }
                                    </div>
                                </abp-column>
                                <abp-column size="_3">
                                    <div class="ml-auto p-2 float-end">
                                        <abp-dropdown>
                                            @if (Model.CurrentLanguage != null)
                                            {
                                                <abp-dropdown-button text="@Model.CurrentLanguage"/>
                                            }

                                            @if (Model.Languages != null)
                                            {
                                                <abp-dropdown-menu>
                                                    @foreach (var language in Model.Languages)
                                                    {
                                                        var languageUrl = Url.Content($"~/Abp/Languages/Switch?culture={language.CultureName}&uiCulture={language.UiCultureName}&returnUrl={System.Net.WebUtility.UrlEncode(Request.GetEncodedPathAndQuery())}");
                                                        <abp-dropdown-item href="@languageUrl">@language.DisplayName</abp-dropdown-item>
                                                    }
                                                </abp-dropdown-menu>
                                            }
                                        </abp-dropdown>
                                    </div>
                                </abp-column>

                            </abp-row>
                            <hr class="m-4" />

                            <abp-row>
                                @if (Model.Applications != null)
                                {
                                    @foreach (var application in Model.Applications)
                                    {
                                        <abp-column size-md="_4" class="mb-2">
                                            <a href="@application.ClientUri" style="text-decoration:none">
                                                <abp-card>
                                                    <abp-card-body>
                                                        @if (!application.LogoUri.IsNullOrEmpty())
                                                        {
                                                            var logoUri = application.LogoUri;
                                                            if(application.LogoUri.StartsWith('/'))
                                                            {
                                                                logoUri = @Url.Content(application.LogoUri.EnsureStartsWith('~'));
                                                            }

                                                            <div class="mx-auto">
                                                                <img src="@logoUri" style="height:64px" class="mb-3" />
                                                            </div>
                                                        }
                                                        <h4>@application.DisplayName</h4>
                                                        <span class="text-muted">@application.ClientUri</span>
                                                    </abp-card-body>
                                                </abp-card>
                                            </a>
                                        </abp-column>
                                    }
                                }
                            </abp-row>
                        </div>

                    </abp-card-body>

                </abp-card>
            </div>

        </abp-row>
    </div>
</div>

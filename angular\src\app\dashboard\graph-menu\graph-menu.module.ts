import { NgModule } from '@angular/core';
import { GraphMenuComponent } from './graph-menu.component';
import { SharedModule } from '../../shared/shared.module';
import { PageModule } from '@abp/ng.components/page';
import { GraphMenuRoutingModule } from './graph-menu-routing.module';
import { MatDividerModule } from '@angular/material/divider';
import { MatRadioModule } from '@angular/material/radio';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatCard } from '@angular/material/card';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
//import { MatTimepickerModule } from '@angular/material/timepicker';

@NgModule({
  declarations: [
    GraphMenuComponent
  ],
  imports: [
    SharedModule,
    GraphMenuRoutingModule,
    PageModule,
    MatDividerModule,
    MatRadioModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatCard,
    MatSelect,
    MatFormFieldModule, MatSelectModule, FormsModule, ReactiveFormsModule,
    MatButtonModule,
    MatProgressSpinnerModule
  ],
  providers: [
    provideNativeDateAdapter()
  ]
})
export class GraphMenuModule { }

﻿using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class OrderService(IRepository<Order> repository) : ApplicationService, IOrderService
{
    public async Task<OrderDto> GetAsync(int id)
    {
        var order = await repository.GetAsync(x => x.OrderId == id);
        return ObjectMapper.Map<Order, OrderDto>(order);
    }
    public async Task<PagedResultDto<OrderDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Include(x => x.Stops)
            .OrderBy(x => input.Sorting.IsNullOrWhiteSpace() ? "ScheduledArrival" : input.Sorting)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var orders = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<OrderDto>(
            totalCount,
            ObjectMapper.Map<List<Order>, List<OrderDto>>(orders)
        );
    }


    public async Task<OrderDto> CreateAsync(OrderDto input)
    {
        var driver = ObjectMapper.Map<OrderDto, Order>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Order, OrderDto>(insertedEntity);
    }

    public Task<OrderDto> UpdateAsync(int id, OrderDto input) => throw new NotImplementedException();
    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.OrderId == id);
    }
}
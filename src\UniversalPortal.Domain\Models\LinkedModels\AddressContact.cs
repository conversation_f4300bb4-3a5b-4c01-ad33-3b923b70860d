﻿using Microsoft.EntityFrameworkCore;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.DirectModels;

using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

[PrimaryKey(nameof(AddressId), nameof(ContactId))]
public class AddressContact : AuditedEntity
{
   [Key, Column(Order = 0)]
    public int AddressId { get; set; }
    [Key, Column(Order = 1)]
    public int ContactId { get; set; }

    [InverseProperty(nameof(DirectModels.Address.AddressContacts))]
    public Address? Address { get; set; }
    [InverseProperty(nameof(DirectModels.Contact.AddressContacts))]
    public Contact? Contact { get; set; }

    public override object[] GetKeys()
    {
        return [AddressId, ContactId];
    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Geofence : TenantAuditedEntity
{
    public Geofence()
    {
        Address = new Address();
        Dwells = [];
    }

    [Key]
    public int GeofenceId { get; set; }
    public int GeolocationId {  get; set; }
    public string GeofenceName {  get; set; } = string.Empty;
    public int AddressId {  get; set; }
    public string GeofenceCoordinates { get; set; } = string.Empty;
    [InverseProperty(nameof(DirectModels.Address.Geofences))]
    public Address? Address { get; set; }
    [InverseProperty(nameof(DirectModels.Geolocation.Geofences))]
    public Geolocation? Geolocation { get; set; }
    [InverseProperty(nameof(Dwell.Geofence))]
    public ICollection<Dwell>?  Dwells { get; set; }

    public override object[] GetKeys()
    {
        return [GeofenceId];
    }

    public IEnumerable<Tuple<double, double>> ParseCoordinates()
    {
        return GeofenceCoordinates
            .Split(';', StringSplitOptions.TrimEntries)
            .Select(pair =>
            {
                double[] coords = [.. pair
                    .Split(',', StringSplitOptions.TrimEntries)
                    .Select(x => double.Parse(x))];

                return new Tuple<double, double>(coords[0], coords[1]);
            });
    }
}
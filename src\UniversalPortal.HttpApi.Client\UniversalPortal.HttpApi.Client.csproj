﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>UniversalPortal</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\UniversalPortal.Application.Contracts\UniversalPortal.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi.Client" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi.Client" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi.Client" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi.Client" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Account.HttpApi.Client" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi.Client" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="**\*generate-proxy.json" />
    <Content Remove="**\*generate-proxy.json" />
  </ItemGroup>

</Project>

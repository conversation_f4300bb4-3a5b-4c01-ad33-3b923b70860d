﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.DirectModels;

using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

public class OrderContact : AuditedEntity
{
    [Key, Column(Order = 0)]
    public int OrderId { get; set; }
    [Key, Column(Order = 1)]
    public int ContactId { get; set; }
    [InverseProperty(nameof(Order.OrderContacts))]
    public Order? Order { get; set; }
    [InverseProperty(nameof(Contact.OrderContacts))]
    public Contact? Contact { get; set; }

    public override object[] GetKeys()
    {
        return [OrderId, ContactId];
    }
}
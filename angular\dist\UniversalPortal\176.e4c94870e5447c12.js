"use strict";(self.webpackChunkUniversalPortal=self.webpackChunkUniversalPortal||[]).push([[176],{3887:(St,ft,f)=>{f.d(ft,{G:()=>Z});var t=f(7),h=f(998),s=f(9862),B=f(4721),nt=f(3953);let Z=(()=>{class z{static{this.\u0275fac=function(P){return new(P||z)}}static{this.\u0275mod=nt.$C({type:z})}static{this.\u0275inj=nt.G2t({imports:[t.Ui,s.Ts,h.zH,B.fs,t.Ui,s.Ts,h.zH,B.fs]})}}return z})()},7647:(St,ft,f)=>{f.d(ft,{u:()=>nt});var t=f(8750),h=f(1413),s=f(7707),B=f(9974);function nt(z={}){const{connector:W=(()=>new h.B),resetOnError:A=!0,resetOnComplete:P=!0,resetOnRefCountZero:ct=!0}=z;return tt=>{let it,et,pt,$=0,S=!1,L=!1;const X=()=>{et?.unsubscribe(),et=void 0},dt=()=>{X(),it=pt=void 0,S=L=!1},lt=()=>{const q=it;dt(),q?.unsubscribe()};return(0,B.N)((q,k)=>{$++,!L&&!S&&X();const O=pt=pt??W();k.add(()=>{$--,0===$&&!L&&!S&&(et=Z(lt,ct))}),O.subscribe(k),!it&&$>0&&(it=new s.Ms({next:T=>O.next(T),error:T=>{L=!0,X(),et=Z(dt,A,T),O.error(T)},complete:()=>{S=!0,X(),et=Z(dt,P),O.complete()}}),(0,t.Tg)(q).subscribe(it))})(tt)}}function Z(z,W,...A){if(!0===W)return void z();if(!1===W)return;const P=new s.Ms({next:()=>{P.unsubscribe(),z()}});return(0,t.Tg)(W(...A)).subscribe(P)}},6039:(St,ft,f)=>{f.d(ft,{Pd:()=>C,Au:()=>Ut,vr:()=>kt,vR:()=>Zt,kB:()=>n,Bu:()=>Yt,FN:()=>Ot,Q_:()=>c,Ai:()=>g,px:()=>Rt,_G:()=>J,w6:()=>Y,Ae:()=>ut});var t=f(177),h=f(3953),s=f(6860),B=f(1413),nt=f(8359),Z=f(4412),z=f(7673),W=f(7336),A=f(8141),P=f(152),ct=f(5964),tt=f(6354),it=f(6697),et=f(5245),pt=f(3294),$=f(6977),S=f(2318),L=f(4085),X=f(4572),dt=f(8793),lt=f(1985),q=f(9172);const O=new Set;let T,v=(()=>{class l{constructor(o,d){this._platform=o,this._nonce=d,this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):st}matchMedia(o){return(this._platform.WEBKIT||this._platform.BLINK)&&function rt(l,m){if(!O.has(l))try{T||(T=document.createElement("style"),m&&T.setAttribute("nonce",m),T.setAttribute("type","text/css"),document.head.appendChild(T)),T.sheet&&(T.sheet.insertRule(`@media ${l} {body{ }}`,0),O.add(l))}catch(o){console.error(o)}}(o,this._nonce),this._matchMedia(o)}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(s.OD),h.KVO(h.BIS,8))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})();function st(l){return{matches:"all"===l||""===l,media:l,addListener:()=>{},removeListener:()=>{}}}let V=(()=>{class l{constructor(o,d){this._mediaMatcher=o,this._zone=d,this._queries=new Map,this._destroySubject=new B.B}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(o){return bt((0,L.FG)(o)).some(_=>this._registerQuery(_).mql.matches)}observe(o){const _=bt((0,L.FG)(o)).map(G=>this._registerQuery(G).observable);let M=(0,X.z)(_);return M=(0,dt.x)(M.pipe((0,it.s)(1)),M.pipe((0,et.i)(1),(0,P.B)(0))),M.pipe((0,tt.T)(G=>{const wt={matches:!1,breakpoints:{}};return G.forEach(({matches:Ht,query:Wt})=>{wt.matches=wt.matches||Ht,wt.breakpoints[Wt]=Ht}),wt}))}_registerQuery(o){if(this._queries.has(o))return this._queries.get(o);const d=this._mediaMatcher.matchMedia(o),M={observable:new lt.c(G=>{const wt=Ht=>this._zone.run(()=>G.next(Ht));return d.addListener(wt),()=>{d.removeListener(wt)}}).pipe((0,q.Z)(d),(0,tt.T)(({matches:G})=>({query:o,matches:G})),(0,$.Q)(this._destroySubject)),mql:d};return this._queries.set(o,M),M}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(v),h.KVO(h.SKi))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})();function bt(l){return l.map(m=>m.split(",")).reduce((m,o)=>m.concat(o)).map(m=>m.trim())}const jt=" ";function Rt(l,m,o){const d=Mt(l,m);o=o.trim(),!d.some(_=>_.trim()===o)&&(d.push(o),l.setAttribute(m,d.join(jt)))}function ut(l,m,o){const d=Mt(l,m);o=o.trim();const _=d.filter(M=>M!==o);_.length?l.setAttribute(m,_.join(jt)):l.removeAttribute(m)}function Mt(l,m){return l.getAttribute(m)?.match(/\S+/g)??[]}const U="cdk-describedby-message",K="cdk-describedby-host";let vt=0,kt=(()=>{class l{constructor(o,d){this._platform=d,this._messageRegistry=new Map,this._messagesContainer=null,this._id=""+vt++,this._document=o,this._id=(0,h.WQX)(h.sZ2)+"-"+vt++}describe(o,d,_){if(!this._canBeDescribed(o,d))return;const M=Ct(d,_);"string"!=typeof d?(It(d,this._id),this._messageRegistry.set(M,{messageElement:d,referenceCount:0})):this._messageRegistry.has(M)||this._createMessageElement(d,_),this._isElementDescribedByMessage(o,M)||this._addMessageReference(o,M)}removeDescription(o,d,_){if(!d||!this._isElementNode(o))return;const M=Ct(d,_);if(this._isElementDescribedByMessage(o,M)&&this._removeMessageReference(o,M),"string"==typeof d){const G=this._messageRegistry.get(M);G&&0===G.referenceCount&&this._deleteMessageElement(M)}0===this._messagesContainer?.childNodes.length&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){const o=this._document.querySelectorAll(`[${K}="${this._id}"]`);for(let d=0;d<o.length;d++)this._removeCdkDescribedByReferenceIds(o[d]),o[d].removeAttribute(K);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(o,d){const _=this._document.createElement("div");It(_,this._id),_.textContent=o,d&&_.setAttribute("role",d),this._createMessagesContainer(),this._messagesContainer.appendChild(_),this._messageRegistry.set(Ct(o,d),{messageElement:_,referenceCount:0})}_deleteMessageElement(o){this._messageRegistry.get(o)?.messageElement?.remove(),this._messageRegistry.delete(o)}_createMessagesContainer(){if(this._messagesContainer)return;const o="cdk-describedby-message-container",d=this._document.querySelectorAll(`.${o}[platform="server"]`);for(let M=0;M<d.length;M++)d[M].remove();const _=this._document.createElement("div");_.style.visibility="hidden",_.classList.add(o),_.classList.add("cdk-visually-hidden"),this._platform&&!this._platform.isBrowser&&_.setAttribute("platform","server"),this._document.body.appendChild(_),this._messagesContainer=_}_removeCdkDescribedByReferenceIds(o){const d=Mt(o,"aria-describedby").filter(_=>0!=_.indexOf(U));o.setAttribute("aria-describedby",d.join(" "))}_addMessageReference(o,d){const _=this._messageRegistry.get(d);Rt(o,"aria-describedby",_.messageElement.id),o.setAttribute(K,this._id),_.referenceCount++}_removeMessageReference(o,d){const _=this._messageRegistry.get(d);_.referenceCount--,ut(o,"aria-describedby",_.messageElement.id),o.removeAttribute(K)}_isElementDescribedByMessage(o,d){const _=Mt(o,"aria-describedby"),M=this._messageRegistry.get(d),G=M&&M.messageElement.id;return!!G&&-1!=_.indexOf(G)}_canBeDescribed(o,d){if(!this._isElementNode(o))return!1;if(d&&"object"==typeof d)return!0;const _=null==d?"":`${d}`.trim(),M=o.getAttribute("aria-label");return!(!_||M&&M.trim()===_)}_isElementNode(o){return o.nodeType===this._document.ELEMENT_NODE}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(t.qQ),h.KVO(s.OD))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})();function Ct(l,m){return"string"==typeof l?`${m||""}/${l}`:l}function It(l,m){l.id||(l.id=`${U}-${m}-${vt++}`)}class Pt{constructor(m,o){this._items=m,this._activeItemIndex=-1,this._activeItem=null,this._wrap=!1,this._letterKeyStream=new B.B,this._typeaheadSubscription=nt.yU.EMPTY,this._vertical=!0,this._allowedModifierKeys=[],this._homeAndEnd=!1,this._pageUpAndDown={enabled:!1,delta:10},this._skipPredicateFn=d=>d.disabled,this._pressedLetters=[],this.tabOut=new B.B,this.change=new B.B,m instanceof h.rOR?this._itemChangesSubscription=m.changes.subscribe(d=>this._itemsChanged(d.toArray())):(0,h.Hps)(m)&&(this._effectRef=(0,h.QZP)(()=>this._itemsChanged(m()),{injector:o}))}skipPredicate(m){return this._skipPredicateFn=m,this}withWrap(m=!0){return this._wrap=m,this}withVerticalOrientation(m=!0){return this._vertical=m,this}withHorizontalOrientation(m){return this._horizontal=m,this}withAllowedModifierKeys(m){return this._allowedModifierKeys=m,this}withTypeAhead(m=200){return this._typeaheadSubscription.unsubscribe(),this._typeaheadSubscription=this._letterKeyStream.pipe((0,A.M)(o=>this._pressedLetters.push(o)),(0,P.B)(m),(0,ct.p)(()=>this._pressedLetters.length>0),(0,tt.T)(()=>this._pressedLetters.join(""))).subscribe(o=>{const d=this._getItemsArray();for(let _=1;_<d.length+1;_++){const M=(this._activeItemIndex+_)%d.length,G=d[M];if(!this._skipPredicateFn(G)&&0===G.getLabel().toUpperCase().trim().indexOf(o)){this.setActiveItem(M);break}}this._pressedLetters=[]}),this}cancelTypeahead(){return this._pressedLetters=[],this}withHomeAndEnd(m=!0){return this._homeAndEnd=m,this}withPageUpDown(m=!0,o=10){return this._pageUpAndDown={enabled:m,delta:o},this}setActiveItem(m){const o=this._activeItem;this.updateActiveItem(m),this._activeItem!==o&&this.change.next(this._activeItemIndex)}onKeydown(m){const o=m.keyCode,_=["altKey","ctrlKey","metaKey","shiftKey"].every(M=>!m[M]||this._allowedModifierKeys.indexOf(M)>-1);switch(o){case W.wn:return void this.tabOut.next();case W.n6:if(this._vertical&&_){this.setNextItemActive();break}return;case W.i7:if(this._vertical&&_){this.setPreviousItemActive();break}return;case W.LE:if(this._horizontal&&_){"rtl"===this._horizontal?this.setPreviousItemActive():this.setNextItemActive();break}return;case W.UQ:if(this._horizontal&&_){"rtl"===this._horizontal?this.setNextItemActive():this.setPreviousItemActive();break}return;case W.yZ:if(this._homeAndEnd&&_){this.setFirstItemActive();break}return;case W.Kp:if(this._homeAndEnd&&_){this.setLastItemActive();break}return;case W.w_:if(this._pageUpAndDown.enabled&&_){const M=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(M>0?M:0,1);break}return;case W.dB:if(this._pageUpAndDown.enabled&&_){const M=this._activeItemIndex+this._pageUpAndDown.delta,G=this._getItemsArray().length;this._setActiveItemByIndex(M<G?M:G-1,-1);break}return;default:return void((_||(0,W.rp)(m,"shiftKey"))&&(m.key&&1===m.key.length?this._letterKeyStream.next(m.key.toLocaleUpperCase()):(o>=W.A&&o<=W.Z||o>=W.f2&&o<=W.bn)&&this._letterKeyStream.next(String.fromCharCode(o))))}this._pressedLetters=[],m.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem}isTyping(){return this._pressedLetters.length>0}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(m){const o=this._getItemsArray(),d="number"==typeof m?m:o.indexOf(m);this._activeItem=o[d]??null,this._activeItemIndex=d}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._letterKeyStream.complete(),this.tabOut.complete(),this.change.complete(),this._pressedLetters=[]}_setActiveItemByDelta(m){this._wrap?this._setActiveInWrapMode(m):this._setActiveInDefaultMode(m)}_setActiveInWrapMode(m){const o=this._getItemsArray();for(let d=1;d<=o.length;d++){const _=(this._activeItemIndex+m*d+o.length)%o.length;if(!this._skipPredicateFn(o[_]))return void this.setActiveItem(_)}}_setActiveInDefaultMode(m){this._setActiveItemByIndex(this._activeItemIndex+m,m)}_setActiveItemByIndex(m,o){const d=this._getItemsArray();if(d[m]){for(;this._skipPredicateFn(d[m]);)if(!d[m+=o])return;this.setActiveItem(m)}}_getItemsArray(){return(0,h.Hps)(this._items)?this._items():this._items instanceof h.rOR?this._items.toArray():this._items}_itemsChanged(m){if(this._activeItem){const o=m.indexOf(this._activeItem);o>-1&&o!==this._activeItemIndex&&(this._activeItemIndex=o)}}}class Ut extends Pt{setActiveItem(m){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(m),this.activeItem&&this.activeItem.setActiveStyles()}}class Yt extends Pt{constructor(){super(...arguments),this._origin="program"}setFocusOrigin(m){return this._origin=m,this}setActiveItem(m){super.setActiveItem(m),this.activeItem&&this.activeItem.focus(this._origin)}}let Lt=(()=>{class l{constructor(o){this._platform=o}isDisabled(o){return o.hasAttribute("disabled")}isVisible(o){return function D(l){return!!(l.offsetWidth||l.offsetHeight||"function"==typeof l.getClientRects&&l.getClientRects().length)}(o)&&"visible"===getComputedStyle(o).visibility}isTabbable(o){if(!this._platform.isBrowser)return!1;const d=function p(l){try{return l.frameElement}catch{return null}}(function Bt(l){return l.ownerDocument&&l.ownerDocument.defaultView||window}(o));if(d&&(-1===Vt(d)||!this.isVisible(d)))return!1;let _=o.nodeName.toLowerCase(),M=Vt(o);return o.hasAttribute("contenteditable")?-1!==M:!("iframe"===_||"object"===_||this._platform.WEBKIT&&this._platform.IOS&&!function Nt(l){let m=l.nodeName.toLowerCase(),o="input"===m&&l.type;return"text"===o||"password"===o||"select"===m||"textarea"===m}(o))&&("audio"===_?!!o.hasAttribute("controls")&&-1!==M:"video"===_?-1!==M&&(null!==M||this._platform.FIREFOX||o.hasAttribute("controls")):o.tabIndex>=0)}isFocusable(o,d){return function Xt(l){return!function Q(l){return function xt(l){return"input"==l.nodeName.toLowerCase()}(l)&&"hidden"==l.type}(l)&&(function I(l){let m=l.nodeName.toLowerCase();return"input"===m||"select"===m||"button"===m||"textarea"===m}(l)||function _t(l){return function Et(l){return"a"==l.nodeName.toLowerCase()}(l)&&l.hasAttribute("href")}(l)||l.hasAttribute("contenteditable")||zt(l))}(o)&&!this.isDisabled(o)&&(d?.ignoreVisibility||this.isVisible(o))}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(s.OD))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})();function zt(l){if(!l.hasAttribute("tabindex")||void 0===l.tabIndex)return!1;let m=l.getAttribute("tabindex");return!(!m||isNaN(parseInt(m,10)))}function Vt(l){if(!zt(l))return null;const m=parseInt(l.getAttribute("tabindex")||"",10);return isNaN(m)?-1:m}class w{get enabled(){return this._enabled}set enabled(m){this._enabled=m,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(m,this._startAnchor),this._toggleAnchorTabIndex(m,this._endAnchor))}constructor(m,o,d,_,M=!1){this._element=m,this._checker=o,this._ngZone=d,this._document=_,this._hasAttached=!1,this.startAnchorListener=()=>this.focusLastTabbableElement(),this.endAnchorListener=()=>this.focusFirstTabbableElement(),this._enabled=!0,M||this.attachAnchors()}destroy(){const m=this._startAnchor,o=this._endAnchor;m&&(m.removeEventListener("focus",this.startAnchorListener),m.remove()),o&&(o.removeEventListener("focus",this.endAnchorListener),o.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return!!this._hasAttached||(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(m){return new Promise(o=>{this._executeOnStable(()=>o(this.focusInitialElement(m)))})}focusFirstTabbableElementWhenReady(m){return new Promise(o=>{this._executeOnStable(()=>o(this.focusFirstTabbableElement(m)))})}focusLastTabbableElementWhenReady(m){return new Promise(o=>{this._executeOnStable(()=>o(this.focusLastTabbableElement(m)))})}_getRegionBoundary(m){const o=this._element.querySelectorAll(`[cdk-focus-region-${m}], [cdkFocusRegion${m}], [cdk-focus-${m}]`);return"start"==m?o.length?o[0]:this._getFirstTabbableElement(this._element):o.length?o[o.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(m){const o=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(o){if(!this._checker.isFocusable(o)){const d=this._getFirstTabbableElement(o);return d?.focus(m),!!d}return o.focus(m),!0}return this.focusFirstTabbableElement(m)}focusFirstTabbableElement(m){const o=this._getRegionBoundary("start");return o&&o.focus(m),!!o}focusLastTabbableElement(m){const o=this._getRegionBoundary("end");return o&&o.focus(m),!!o}hasAttached(){return this._hasAttached}_getFirstTabbableElement(m){if(this._checker.isFocusable(m)&&this._checker.isTabbable(m))return m;const o=m.children;for(let d=0;d<o.length;d++){const _=o[d].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(o[d]):null;if(_)return _}return null}_getLastTabbableElement(m){if(this._checker.isFocusable(m)&&this._checker.isTabbable(m))return m;const o=m.children;for(let d=o.length-1;d>=0;d--){const _=o[d].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(o[d]):null;if(_)return _}return null}_createAnchor(){const m=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,m),m.classList.add("cdk-visually-hidden"),m.classList.add("cdk-focus-trap-anchor"),m.setAttribute("aria-hidden","true"),m}_toggleAnchorTabIndex(m,o){m?o.setAttribute("tabindex","0"):o.removeAttribute("tabindex")}toggleAnchors(m){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(m,this._startAnchor),this._toggleAnchorTabIndex(m,this._endAnchor))}_executeOnStable(m){this._ngZone.isStable?m():this._ngZone.onStable.pipe((0,it.s)(1)).subscribe(m)}}let e=(()=>{class l{constructor(o,d,_){this._checker=o,this._ngZone=d,this._document=_}create(o,d=!1){return new w(o,this._checker,this._ngZone,this._document,d)}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(Lt),h.KVO(h.SKi),h.KVO(t.qQ))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})(),n=(()=>{class l{get enabled(){return this.focusTrap?.enabled||!1}set enabled(o){this.focusTrap&&(this.focusTrap.enabled=o)}constructor(o,d,_){this._elementRef=o,this._focusTrapFactory=d,this._previouslyFocusedElement=null,(0,h.WQX)(s.OD).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(o){const d=o.autoCapture;d&&!d.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=(0,s.vc)(),this.focusTrap?.focusInitialElementWhenReady()}static{this.\u0275fac=function(d){return new(d||l)(h.rXU(h.aKT),h.rXU(e),h.rXU(t.qQ))}}static{this.\u0275dir=h.FsC({type:l,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[2,"cdkTrapFocus","enabled",h.L39],autoCapture:[2,"cdkTrapFocusAutoCapture","autoCapture",h.L39]},exportAs:["cdkTrapFocus"],standalone:!0,features:[h.GFd,h.OA$]})}}return l})();function J(l){return 0===l.buttons||0===l.detail}function Y(l){const m=l.touches&&l.touches[0]||l.changedTouches&&l.changedTouches[0];return!(!m||-1!==m.identifier||null!=m.radiusX&&1!==m.radiusX||null!=m.radiusY&&1!==m.radiusY)}const H=new h.nKC("cdk-input-modality-detector-options"),ht={ignoreKeys:[W.A$,W.W3,W.eg,W.Ge,W.FX]},yt=(0,s.BQ)({passive:!0,capture:!0});let Dt=(()=>{class l{get mostRecentModality(){return this._modality.value}constructor(o,d,_,M){this._platform=o,this._mostRecentTarget=null,this._modality=new Z.t(null),this._lastTouchMs=0,this._onKeydown=G=>{this._options?.ignoreKeys?.some(wt=>wt===G.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=(0,s.Fb)(G))},this._onMousedown=G=>{Date.now()-this._lastTouchMs<650||(this._modality.next(J(G)?"keyboard":"mouse"),this._mostRecentTarget=(0,s.Fb)(G))},this._onTouchstart=G=>{Y(G)?this._modality.next("keyboard"):(this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=(0,s.Fb)(G))},this._options={...ht,...M},this.modalityDetected=this._modality.pipe((0,et.i)(1)),this.modalityChanged=this.modalityDetected.pipe((0,pt.F)()),o.isBrowser&&d.runOutsideAngular(()=>{_.addEventListener("keydown",this._onKeydown,yt),_.addEventListener("mousedown",this._onMousedown,yt),_.addEventListener("touchstart",this._onTouchstart,yt)})}ngOnDestroy(){this._modality.complete(),this._platform.isBrowser&&(document.removeEventListener("keydown",this._onKeydown,yt),document.removeEventListener("mousedown",this._onMousedown,yt),document.removeEventListener("touchstart",this._onTouchstart,yt))}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(s.OD),h.KVO(h.SKi),h.KVO(t.qQ),h.KVO(H,8))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})();const Ft=new h.nKC("liveAnnouncerElement",{providedIn:"root",factory:function Gt(){return null}}),E=new h.nKC("LIVE_ANNOUNCER_DEFAULT_OPTIONS");let mt=0,g=(()=>{class l{constructor(o,d,_,M){this._ngZone=d,this._defaultOptions=M,this._document=_,this._liveElement=o||this._createLiveElement()}announce(o,...d){const _=this._defaultOptions;let M,G;return 1===d.length&&"number"==typeof d[0]?G=d[0]:[M,G]=d,this.clear(),clearTimeout(this._previousTimeout),M||(M=_&&_.politeness?_.politeness:"polite"),null==G&&_&&(G=_.duration),this._liveElement.setAttribute("aria-live",M),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(wt=>this._currentResolve=wt)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=o,"number"==typeof G&&(this._previousTimeout=setTimeout(()=>this.clear(),G)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){const o="cdk-live-announcer-element",d=this._document.getElementsByClassName(o),_=this._document.createElement("div");for(let M=0;M<d.length;M++)d[M].remove();return _.classList.add(o),_.classList.add("cdk-visually-hidden"),_.setAttribute("aria-atomic","true"),_.setAttribute("aria-live","polite"),_.id="cdk-live-announcer-"+mt++,this._document.body.appendChild(_),_}_exposeAnnouncerToModals(o){const d=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let _=0;_<d.length;_++){const M=d[_],G=M.getAttribute("aria-owns");G?-1===G.indexOf(o)&&M.setAttribute("aria-owns",G+" "+o):M.setAttribute("aria-owns",o)}}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(Ft,8),h.KVO(h.SKi),h.KVO(t.qQ),h.KVO(E,8))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})();var F=function(l){return l[l.IMMEDIATE=0]="IMMEDIATE",l[l.EVENTUAL=1]="EVENTUAL",l}(F||{});const at=new h.nKC("cdk-focus-monitor-default-options"),gt=(0,s.BQ)({passive:!0,capture:!0});let Ot=(()=>{class l{constructor(o,d,_,M,G){this._ngZone=o,this._platform=d,this._inputModalityDetector=_,this._origin=null,this._windowFocused=!1,this._originFromTouchInteraction=!1,this._elementInfo=new Map,this._monitoredElementCount=0,this._rootNodeFocusListenerCount=new Map,this._windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=window.setTimeout(()=>this._windowFocused=!1)},this._stopInputModalityDetector=new B.B,this._rootNodeFocusAndBlurListener=wt=>{for(let Wt=(0,s.Fb)(wt);Wt;Wt=Wt.parentElement)"focus"===wt.type?this._onFocus(wt,Wt):this._onBlur(wt,Wt)},this._document=M,this._detectionMode=G?.detectionMode||F.IMMEDIATE}monitor(o,d=!1){const _=(0,L.i8)(o);if(!this._platform.isBrowser||1!==_.nodeType)return(0,z.of)();const M=(0,s.KT)(_)||this._getDocument(),G=this._elementInfo.get(_);if(G)return d&&(G.checkChildren=!0),G.subject;const wt={checkChildren:d,subject:new B.B,rootNode:M};return this._elementInfo.set(_,wt),this._registerGlobalListeners(wt),wt.subject}stopMonitoring(o){const d=(0,L.i8)(o),_=this._elementInfo.get(d);_&&(_.subject.complete(),this._setClasses(d),this._elementInfo.delete(d),this._removeGlobalListeners(_))}focusVia(o,d,_){const M=(0,L.i8)(o);M===this._getDocument().activeElement?this._getClosestElementsInfo(M).forEach(([wt,Ht])=>this._originChanged(wt,d,Ht)):(this._setOrigin(d),"function"==typeof M.focus&&M.focus(_))}ngOnDestroy(){this._elementInfo.forEach((o,d)=>this.stopMonitoring(d))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(o){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(o)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:o&&this._isLastInteractionFromInputLabel(o)?"mouse":"program"}_shouldBeAttributedToTouch(o){return this._detectionMode===F.EVENTUAL||!!o?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(o,d){o.classList.toggle("cdk-focused",!!d),o.classList.toggle("cdk-touch-focused","touch"===d),o.classList.toggle("cdk-keyboard-focused","keyboard"===d),o.classList.toggle("cdk-mouse-focused","mouse"===d),o.classList.toggle("cdk-program-focused","program"===d)}_setOrigin(o,d=!1){this._ngZone.runOutsideAngular(()=>{this._origin=o,this._originFromTouchInteraction="touch"===o&&d,this._detectionMode===F.IMMEDIATE&&(clearTimeout(this._originTimeoutId),this._originTimeoutId=setTimeout(()=>this._origin=null,this._originFromTouchInteraction?650:1))})}_onFocus(o,d){const _=this._elementInfo.get(d),M=(0,s.Fb)(o);!_||!_.checkChildren&&d!==M||this._originChanged(d,this._getFocusOrigin(M),_)}_onBlur(o,d){const _=this._elementInfo.get(d);!_||_.checkChildren&&o.relatedTarget instanceof Node&&d.contains(o.relatedTarget)||(this._setClasses(d),this._emitOrigin(_,null))}_emitOrigin(o,d){o.subject.observers.length&&this._ngZone.run(()=>o.subject.next(d))}_registerGlobalListeners(o){if(!this._platform.isBrowser)return;const d=o.rootNode,_=this._rootNodeFocusListenerCount.get(d)||0;_||this._ngZone.runOutsideAngular(()=>{d.addEventListener("focus",this._rootNodeFocusAndBlurListener,gt),d.addEventListener("blur",this._rootNodeFocusAndBlurListener,gt)}),this._rootNodeFocusListenerCount.set(d,_+1),1==++this._monitoredElementCount&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe((0,$.Q)(this._stopInputModalityDetector)).subscribe(M=>{this._setOrigin(M,!0)}))}_removeGlobalListeners(o){const d=o.rootNode;if(this._rootNodeFocusListenerCount.has(d)){const _=this._rootNodeFocusListenerCount.get(d);_>1?this._rootNodeFocusListenerCount.set(d,_-1):(d.removeEventListener("focus",this._rootNodeFocusAndBlurListener,gt),d.removeEventListener("blur",this._rootNodeFocusAndBlurListener,gt),this._rootNodeFocusListenerCount.delete(d))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(o,d,_){this._setClasses(o,d),this._emitOrigin(_,d),this._lastFocusOrigin=d}_getClosestElementsInfo(o){const d=[];return this._elementInfo.forEach((_,M)=>{(M===o||_.checkChildren&&M.contains(o))&&d.push([M,_])}),d}_isLastInteractionFromInputLabel(o){const{_mostRecentTarget:d,mostRecentModality:_}=this._inputModalityDetector;if("mouse"!==_||!d||d===o||"INPUT"!==o.nodeName&&"TEXTAREA"!==o.nodeName||o.disabled)return!1;const M=o.labels;if(M)for(let G=0;G<M.length;G++)if(M[G].contains(d))return!0;return!1}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(h.SKi),h.KVO(s.OD),h.KVO(Dt),h.KVO(t.qQ,8),h.KVO(at,8))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})(),Zt=(()=>{class l{constructor(o,d){this._elementRef=o,this._focusMonitor=d,this._focusOrigin=null,this.cdkFocusChange=new h.bkB}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){const o=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(o,1===o.nodeType&&o.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(d=>{this._focusOrigin=d,this.cdkFocusChange.emit(d)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static{this.\u0275fac=function(d){return new(d||l)(h.rXU(h.aKT),h.rXU(Ot))}}static{this.\u0275dir=h.FsC({type:l,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"],standalone:!0})}}return l})();var Kt=function(l){return l[l.NONE=0]="NONE",l[l.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",l[l.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",l}(Kt||{});const x="cdk-high-contrast-black-on-white",b="cdk-high-contrast-white-on-black",a="cdk-high-contrast-active";let c=(()=>{class l{constructor(o,d){this._platform=o,this._document=d,this._breakpointSubscription=(0,h.WQX)(V).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return Kt.NONE;const o=this._document.createElement("div");o.style.backgroundColor="rgb(1,2,3)",o.style.position="absolute",this._document.body.appendChild(o);const d=this._document.defaultView||window,_=d&&d.getComputedStyle?d.getComputedStyle(o):null,M=(_&&_.backgroundColor||"").replace(/ /g,"");switch(o.remove(),M){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return Kt.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return Kt.BLACK_ON_WHITE}return Kt.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){const o=this._document.body.classList;o.remove(a,x,b),this._hasCheckedHighContrastMode=!0;const d=this.getHighContrastMode();d===Kt.BLACK_ON_WHITE?o.add(a,x):d===Kt.WHITE_ON_BLACK&&o.add(a,b)}}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(s.OD),h.KVO(t.qQ))}}static{this.\u0275prov=h.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})(),C=(()=>{class l{constructor(o){o._applyBodyHighContrastModeCssClasses()}static{this.\u0275fac=function(d){return new(d||l)(h.KVO(c))}}static{this.\u0275mod=h.$C({type:l})}static{this.\u0275inj=h.G2t({imports:[S.w5]})}}return l})()},8203:(St,ft,f)=>{f.d(ft,{dS:()=>z,jI:()=>A});var t=f(3953),h=f(177);const s=new t.nKC("cdk-dir-doc",{providedIn:"root",factory:function B(){return(0,t.WQX)(h.qQ)}}),nt=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;let z=(()=>{class P{constructor(tt){this.value="ltr",this.change=new t.bkB,tt&&(this.value=function Z(P){const ct=P?.toLowerCase()||"";return"auto"===ct&&typeof navigator<"u"&&navigator?.language?nt.test(navigator.language)?"rtl":"ltr":"rtl"===ct?"rtl":"ltr"}((tt.body?tt.body.dir:null)||(tt.documentElement?tt.documentElement.dir:null)||"ltr"))}ngOnDestroy(){this.change.complete()}static{this.\u0275fac=function(it){return new(it||P)(t.KVO(s,8))}}static{this.\u0275prov=t.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})}}return P})(),A=(()=>{class P{static{this.\u0275fac=function(it){return new(it||P)}}static{this.\u0275mod=t.$C({type:P})}static{this.\u0275inj=t.G2t({})}}return P})()},4085:(St,ft,f)=>{f.d(ft,{FG:()=>nt,OE:()=>s,a1:()=>Z,cc:()=>W,he:()=>h,i8:()=>z,o1:()=>B});var t=f(3953);function h(A){return null!=A&&"false"!=`${A}`}function s(A,P=0){return B(A)?Number(A):P}function B(A){return!isNaN(parseFloat(A))&&!isNaN(Number(A))}function nt(A){return Array.isArray(A)?A:[A]}function Z(A){return null==A?"":"string"==typeof A?A:`${A}px`}function z(A){return A instanceof t.aKT?A.nativeElement:A}function W(A,P=/\s+/){const ct=[];if(null!=A){const tt=Array.isArray(A)?A:`${A}`.split(P);for(const it of tt){const et=`${it}`.trim();et&&ct.push(et)}}return ct}},5024:(St,ft,f)=>{f.d(ft,{CB:()=>it,DQ:()=>tt,Q3:()=>A,qS:()=>Z,sL:()=>P,xn:()=>ct,y4:()=>z,zP:()=>pt});var t=f(17),B=(f(4402),f(7673),f(1413)),nt=f(3953);class Z{}function z($){return $&&"function"==typeof $.connect&&!($ instanceof t.G)}var A=function($){return $[$.REPLACED=0]="REPLACED",$[$.INSERTED=1]="INSERTED",$[$.MOVED=2]="MOVED",$[$.REMOVED=3]="REMOVED",$}(A||{});const P=new nt.nKC("_ViewRepeater");class ct{applyChanges(S,L,X,dt,lt){S.forEachOperation((q,k,O)=>{let T,v;if(null==q.previousIndex){const rt=X(q,k,O);T=L.createEmbeddedView(rt.templateRef,rt.context,rt.index),v=A.INSERTED}else null==O?(L.remove(k),v=A.REMOVED):(T=L.get(k),L.move(T,O),v=A.MOVED);lt&&lt({context:T?.context,operation:v,record:q})})}detach(){}}class tt{constructor(){this.viewCacheSize=20,this._viewCache=[]}applyChanges(S,L,X,dt,lt){S.forEachOperation((q,k,O)=>{let T,v;null==q.previousIndex?(T=this._insertView(()=>X(q,k,O),O,L,dt(q)),v=T?A.INSERTED:A.REPLACED):null==O?(this._detachAndCacheView(k,L),v=A.REMOVED):(T=this._moveView(k,O,L,dt(q)),v=A.MOVED),lt&&lt({context:T?.context,operation:v,record:q})})}detach(){for(const S of this._viewCache)S.destroy();this._viewCache=[]}_insertView(S,L,X,dt){const lt=this._insertViewFromCache(L,X);if(lt)return void(lt.context.$implicit=dt);const q=S();return X.createEmbeddedView(q.templateRef,q.context,q.index)}_detachAndCacheView(S,L){const X=L.detach(S);this._maybeCacheView(X,L)}_moveView(S,L,X,dt){const lt=X.get(S);return X.move(lt,L),lt.context.$implicit=dt,lt}_maybeCacheView(S,L){if(this._viewCache.length<this.viewCacheSize)this._viewCache.push(S);else{const X=L.indexOf(S);-1===X?S.destroy():L.remove(X)}}_insertViewFromCache(S,L){const X=this._viewCache.pop();return X&&L.insert(X,S),X||null}}class it{get selected(){return this._selected||(this._selected=Array.from(this._selection.values())),this._selected}constructor(S=!1,L,X=!0,dt){this._multiple=S,this._emitChanges=X,this.compareWith=dt,this._selection=new Set,this._deselectedToEmit=[],this._selectedToEmit=[],this.changed=new B.B,L&&L.length&&(S?L.forEach(lt=>this._markSelected(lt)):this._markSelected(L[0]),this._selectedToEmit.length=0)}select(...S){this._verifyValueAssignment(S),S.forEach(X=>this._markSelected(X));const L=this._hasQueuedChanges();return this._emitChangeEvent(),L}deselect(...S){this._verifyValueAssignment(S),S.forEach(X=>this._unmarkSelected(X));const L=this._hasQueuedChanges();return this._emitChangeEvent(),L}setSelection(...S){this._verifyValueAssignment(S);const L=this.selected,X=new Set(S);S.forEach(lt=>this._markSelected(lt)),L.filter(lt=>!X.has(this._getConcreteValue(lt,X))).forEach(lt=>this._unmarkSelected(lt));const dt=this._hasQueuedChanges();return this._emitChangeEvent(),dt}toggle(S){return this.isSelected(S)?this.deselect(S):this.select(S)}clear(S=!0){this._unmarkAll();const L=this._hasQueuedChanges();return S&&this._emitChangeEvent(),L}isSelected(S){return this._selection.has(this._getConcreteValue(S))}isEmpty(){return 0===this._selection.size}hasValue(){return!this.isEmpty()}sort(S){this._multiple&&this.selected&&this._selected.sort(S)}isMultipleSelection(){return this._multiple}_emitChangeEvent(){this._selected=null,(this._selectedToEmit.length||this._deselectedToEmit.length)&&(this.changed.next({source:this,added:this._selectedToEmit,removed:this._deselectedToEmit}),this._deselectedToEmit=[],this._selectedToEmit=[])}_markSelected(S){S=this._getConcreteValue(S),this.isSelected(S)||(this._multiple||this._unmarkAll(),this.isSelected(S)||this._selection.add(S),this._emitChanges&&this._selectedToEmit.push(S))}_unmarkSelected(S){S=this._getConcreteValue(S),this.isSelected(S)&&(this._selection.delete(S),this._emitChanges&&this._deselectedToEmit.push(S))}_unmarkAll(){this.isEmpty()||this._selection.forEach(S=>this._unmarkSelected(S))}_verifyValueAssignment(S){}_hasQueuedChanges(){return!(!this._deselectedToEmit.length&&!this._selectedToEmit.length)}_getConcreteValue(S,L){if(this.compareWith){L=L??this._selection;for(let X of L)if(this.compareWith(S,X))return X;return S}return S}}let pt=(()=>{class ${constructor(){this._listeners=[]}notify(L,X){for(let dt of this._listeners)dt(L,X)}listen(L){return this._listeners.push(L),()=>{this._listeners=this._listeners.filter(X=>L!==X)}}ngOnDestroy(){this._listeners=[]}static{this.\u0275fac=function(X){return new(X||$)}}static{this.\u0275prov=nt.jDH({token:$,factory:$.\u0275fac,providedIn:"root"})}}return $})()},7336:(St,ft,f)=>{f.d(ft,{A:()=>vt,A$:()=>W,FX:()=>Z,Fm:()=>nt,G_:()=>h,Ge:()=>u,Kp:()=>pt,LE:()=>X,SJ:()=>O,UQ:()=>S,W3:()=>z,Z:()=>r,_f:()=>ct,bn:()=>ut,dB:()=>et,eg:()=>ie,f2:()=>T,i7:()=>L,n6:()=>dt,rp:()=>ne,t6:()=>tt,w_:()=>it,wn:()=>s,yZ:()=>$});const h=8,s=9,nt=13,Z=16,z=17,W=18,ct=27,tt=32,it=33,et=34,pt=35,$=36,S=37,L=38,X=39,dt=40,O=46,T=48,ut=57,vt=65,r=90,u=91,ie=224;function ne(Qt,...ee){return ee.length?ee.some(oe=>Qt[oe]):Qt.altKey||Qt.shiftKey||Qt.ctrlKey||Qt.metaKey}},2318:(St,ft,f)=>{f.d(ft,{w5:()=>Z});var t=f(3953);let s=(()=>{class z{create(A){return typeof MutationObserver>"u"?null:new MutationObserver(A)}static{this.\u0275fac=function(P){return new(P||z)}}static{this.\u0275prov=t.jDH({token:z,factory:z.\u0275fac,providedIn:"root"})}}return z})(),Z=(()=>{class z{static{this.\u0275fac=function(P){return new(P||z)}}static{this.\u0275mod=t.$C({type:z})}static{this.\u0275inj=t.G2t({providers:[s]})}}return z})()},6969:(St,ft,f)=>{f.d(ft,{WB:()=>zt,$Q:()=>Et,rW:()=>kt,hJ:()=>Q,rR:()=>rt,z_:()=>Xt});var t=f(3980),h=f(177),s=f(3953),B=f(4085),nt=f(6860),Z=f(5964),z=f(6697),W=f(6977),A=f(9974),P=f(4360),tt=f(8203),it=f(6939),et=f(1413),pt=f(8359),$=f(7786),S=f(7336);const L=(0,nt.CZ)();class X{constructor(e,n){this._viewportRuler=e,this._previousHTMLStyles={top:"",left:""},this._isEnabled=!1,this._document=n}attach(){}enable(){if(this._canBeEnabled()){const e=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=e.style.left||"",this._previousHTMLStyles.top=e.style.top||"",e.style.left=(0,B.a1)(-this._previousScrollPosition.left),e.style.top=(0,B.a1)(-this._previousScrollPosition.top),e.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){const e=this._document.documentElement,i=e.style,r=this._document.body.style,u=i.scrollBehavior||"",y=r.scrollBehavior||"";this._isEnabled=!1,i.left=this._previousHTMLStyles.left,i.top=this._previousHTMLStyles.top,e.classList.remove("cdk-global-scrollblock"),L&&(i.scrollBehavior=r.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),L&&(i.scrollBehavior=u,r.scrollBehavior=y)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;const n=this._document.body,i=this._viewportRuler.getViewportSize();return n.scrollHeight>i.height||n.scrollWidth>i.width}}class lt{constructor(e,n,i,r){this._scrollDispatcher=e,this._ngZone=n,this._viewportRuler=i,this._config=r,this._scrollSubscription=null,this._detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}}attach(e){this._overlayRef=e}enable(){if(this._scrollSubscription)return;const e=this._scrollDispatcher.scrolled(0).pipe((0,Z.p)(n=>!n||!this._overlayRef.overlayElement.contains(n.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=e.subscribe(()=>{const n=this._viewportRuler.getViewportScrollPosition().top;Math.abs(n-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=e.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}class q{enable(){}disable(){}attach(){}}function k(w,e){return e.some(n=>w.bottom<n.top||w.top>n.bottom||w.right<n.left||w.left>n.right)}function O(w,e){return e.some(n=>w.top<n.top||w.bottom>n.bottom||w.left<n.left||w.right>n.right)}class T{constructor(e,n,i,r){this._scrollDispatcher=e,this._viewportRuler=n,this._ngZone=i,this._config=r,this._scrollSubscription=null}attach(e){this._overlayRef=e}enable(){this._scrollSubscription||(this._scrollSubscription=this._scrollDispatcher.scrolled(this._config?this._config.scrollThrottle:0).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){const n=this._overlayRef.overlayElement.getBoundingClientRect(),{width:i,height:r}=this._viewportRuler.getViewportSize();k(n,[{width:i,height:r,bottom:r,right:i,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}}))}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}let v=(()=>{class w{constructor(n,i,r,u){this._scrollDispatcher=n,this._viewportRuler=i,this._ngZone=r,this.noop=()=>new q,this.close=y=>new lt(this._scrollDispatcher,this._ngZone,this._viewportRuler,y),this.block=()=>new X(this._viewportRuler,this._document),this.reposition=y=>new T(this._scrollDispatcher,this._viewportRuler,this._ngZone,y),this._document=u}static{this.\u0275fac=function(i){return new(i||w)(s.KVO(t.R),s.KVO(t.Xj),s.KVO(s.SKi),s.KVO(h.qQ))}}static{this.\u0275prov=s.jDH({token:w,factory:w.\u0275fac,providedIn:"root"})}}return w})();class rt{constructor(e){if(this.scrollStrategy=new q,this.panelClass="",this.hasBackdrop=!1,this.backdropClass="cdk-overlay-dark-backdrop",this.disposeOnNavigation=!1,e){const n=Object.keys(e);for(const i of n)void 0!==e[i]&&(this[i]=e[i])}}}class bt{constructor(e,n){this.connectionPair=e,this.scrollableViewProperties=n}}let Rt=(()=>{class w{constructor(n){this._attachedOverlays=[],this._document=n}ngOnDestroy(){this.detach()}add(n){this.remove(n),this._attachedOverlays.push(n)}remove(n){const i=this._attachedOverlays.indexOf(n);i>-1&&this._attachedOverlays.splice(i,1),0===this._attachedOverlays.length&&this.detach()}static{this.\u0275fac=function(i){return new(i||w)(s.KVO(h.qQ))}}static{this.\u0275prov=s.jDH({token:w,factory:w.\u0275fac,providedIn:"root"})}}return w})(),ut=(()=>{class w extends Rt{constructor(n,i){super(n),this._ngZone=i,this._keydownListener=r=>{const u=this._attachedOverlays;for(let y=u.length-1;y>-1;y--)if(u[y]._keydownEvents.observers.length>0){const N=u[y]._keydownEvents;this._ngZone?this._ngZone.run(()=>N.next(r)):N.next(r);break}}}add(n){super.add(n),this._isAttached||(this._ngZone?this._ngZone.runOutsideAngular(()=>this._document.body.addEventListener("keydown",this._keydownListener)):this._document.body.addEventListener("keydown",this._keydownListener),this._isAttached=!0)}detach(){this._isAttached&&(this._document.body.removeEventListener("keydown",this._keydownListener),this._isAttached=!1)}static{this.\u0275fac=function(i){return new(i||w)(s.KVO(h.qQ),s.KVO(s.SKi,8))}}static{this.\u0275prov=s.jDH({token:w,factory:w.\u0275fac,providedIn:"root"})}}return w})(),Mt=(()=>{class w extends Rt{constructor(n,i,r){super(n),this._platform=i,this._ngZone=r,this._cursorStyleIsSet=!1,this._pointerDownListener=u=>{this._pointerDownEventTarget=(0,nt.Fb)(u)},this._clickListener=u=>{const y=(0,nt.Fb)(u),N="click"===u.type&&this._pointerDownEventTarget?this._pointerDownEventTarget:y;this._pointerDownEventTarget=null;const J=this._attachedOverlays.slice();for(let Y=J.length-1;Y>-1;Y--){const H=J[Y];if(H._outsidePointerEvents.observers.length<1||!H.hasAttached())continue;if(H.overlayElement.contains(y)||H.overlayElement.contains(N))break;const ht=H._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>ht.next(u)):ht.next(u)}}}add(n){if(super.add(n),!this._isAttached){const i=this._document.body;this._ngZone?this._ngZone.runOutsideAngular(()=>this._addEventListeners(i)):this._addEventListeners(i),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=i.style.cursor,i.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){if(this._isAttached){const n=this._document.body;n.removeEventListener("pointerdown",this._pointerDownListener,!0),n.removeEventListener("click",this._clickListener,!0),n.removeEventListener("auxclick",this._clickListener,!0),n.removeEventListener("contextmenu",this._clickListener,!0),this._platform.IOS&&this._cursorStyleIsSet&&(n.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1}}_addEventListeners(n){n.addEventListener("pointerdown",this._pointerDownListener,!0),n.addEventListener("click",this._clickListener,!0),n.addEventListener("auxclick",this._clickListener,!0),n.addEventListener("contextmenu",this._clickListener,!0)}static{this.\u0275fac=function(i){return new(i||w)(s.KVO(h.qQ),s.KVO(nt.OD),s.KVO(s.SKi,8))}}static{this.\u0275prov=s.jDH({token:w,factory:w.\u0275fac,providedIn:"root"})}}return w})(),j=(()=>{class w{constructor(n,i){this._platform=i,this._document=n}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._containerElement||this._createContainer(),this._containerElement}_createContainer(){const n="cdk-overlay-container";if(this._platform.isBrowser||(0,nt.v8)()){const r=this._document.querySelectorAll(`.${n}[platform="server"], .${n}[platform="test"]`);for(let u=0;u<r.length;u++)r[u].remove()}const i=this._document.createElement("div");i.classList.add(n),(0,nt.v8)()?i.setAttribute("platform","test"):this._platform.isBrowser||i.setAttribute("platform","server"),this._document.body.appendChild(i),this._containerElement=i}static{this.\u0275fac=function(i){return new(i||w)(s.KVO(h.qQ),s.KVO(nt.OD))}}static{this.\u0275prov=s.jDH({token:w,factory:w.\u0275fac,providedIn:"root"})}}return w})();class U{constructor(e,n,i,r,u,y,N,J,Y,H=!1){this._portalOutlet=e,this._host=n,this._pane=i,this._config=r,this._ngZone=u,this._keyboardDispatcher=y,this._document=N,this._location=J,this._outsideClickDispatcher=Y,this._animationsDisabled=H,this._backdropElement=null,this._backdropClick=new et.B,this._attachments=new et.B,this._detachments=new et.B,this._locationChanges=pt.yU.EMPTY,this._backdropClickHandler=ht=>this._backdropClick.next(ht),this._backdropTransitionendHandler=ht=>{this._disposeBackdrop(ht.target)},this._keydownEvents=new et.B,this._outsidePointerEvents=new et.B,r.scrollStrategy&&(this._scrollStrategy=r.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=r.positionStrategy}get overlayElement(){return this._pane}get backdropElement(){return this._backdropElement}get hostElement(){return this._host}attach(e){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);const n=this._portalOutlet.attach(e);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._ngZone.onStable.pipe((0,z.s)(1)).subscribe(()=>{this.hasAttached()&&this.updatePosition()}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),"function"==typeof n?.onDestroy&&n.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),n}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();const e=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenStable(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),e}dispose(){const e=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._disposeBackdrop(this._backdropElement),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._previousHostParent=this._pane=this._host=null,e&&this._detachments.next(),this._detachments.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(e){e!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=e,this.hasAttached()&&(e.attach(this),this.updatePosition()))}updateSize(e){this._config={...this._config,...e},this._updateElementSize()}setDirection(e){this._config={...this._config,direction:e},this._updateElementDirection()}addPanelClass(e){this._pane&&this._toggleClasses(this._pane,e,!0)}removePanelClass(e){this._pane&&this._toggleClasses(this._pane,e,!1)}getDirection(){const e=this._config.direction;return e?"string"==typeof e?e:e.value:"ltr"}updateScrollStrategy(e){e!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=e,this.hasAttached()&&(e.attach(this),e.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;const e=this._pane.style;e.width=(0,B.a1)(this._config.width),e.height=(0,B.a1)(this._config.height),e.minWidth=(0,B.a1)(this._config.minWidth),e.minHeight=(0,B.a1)(this._config.minHeight),e.maxWidth=(0,B.a1)(this._config.maxWidth),e.maxHeight=(0,B.a1)(this._config.maxHeight)}_togglePointerEvents(e){this._pane.style.pointerEvents=e?"":"none"}_attachBackdrop(){const e="cdk-overlay-backdrop-showing";this._backdropElement=this._document.createElement("div"),this._backdropElement.classList.add("cdk-overlay-backdrop"),this._animationsDisabled&&this._backdropElement.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropElement,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropElement,this._host),this._backdropElement.addEventListener("click",this._backdropClickHandler),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>{this._backdropElement&&this._backdropElement.classList.add(e)})}):this._backdropElement.classList.add(e)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){const e=this._backdropElement;if(e){if(this._animationsDisabled)return void this._disposeBackdrop(e);e.classList.remove("cdk-overlay-backdrop-showing"),this._ngZone.runOutsideAngular(()=>{e.addEventListener("transitionend",this._backdropTransitionendHandler)}),e.style.pointerEvents="none",this._backdropTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(()=>{this._disposeBackdrop(e)},500))}}_toggleClasses(e,n,i){const r=(0,B.FG)(n||[]).filter(u=>!!u);r.length&&(i?e.classList.add(...r):e.classList.remove(...r))}_detachContentWhenStable(){this._ngZone.runOutsideAngular(()=>{const e=this._ngZone.onStable.pipe((0,W.Q)((0,$.h)(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||0===this._pane.children.length)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),e.unsubscribe())})})}_disposeScrollStrategy(){const e=this._scrollStrategy;e&&(e.disable(),e.detach&&e.detach())}_disposeBackdrop(e){e&&(e.removeEventListener("click",this._backdropClickHandler),e.removeEventListener("transitionend",this._backdropTransitionendHandler),e.remove(),this._backdropElement===e&&(this._backdropElement=null)),this._backdropTimeout&&(clearTimeout(this._backdropTimeout),this._backdropTimeout=void 0)}}const K="cdk-overlay-connected-position-bounding-box",vt=/([A-Za-z%]+)$/;class kt{get positions(){return this._preferredPositions}constructor(e,n,i,r,u){this._viewportRuler=n,this._document=i,this._platform=r,this._overlayContainer=u,this._lastBoundingBoxSize={width:0,height:0},this._isPushed=!1,this._canPush=!0,this._growAfterOpen=!1,this._hasFlexibleDimensions=!0,this._positionLocked=!1,this._viewportMargin=0,this._scrollables=[],this._preferredPositions=[],this._positionChanges=new et.B,this._resizeSubscription=pt.yU.EMPTY,this._offsetX=0,this._offsetY=0,this._appliedPanelClasses=[],this.positionChanges=this._positionChanges,this.setOrigin(e)}attach(e){this._validatePositions(),e.hostElement.classList.add(K),this._overlayRef=e,this._boundingBox=e.hostElement,this._pane=e.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition)return void this.reapplyLastPosition();this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const e=this._originRect,n=this._overlayRect,i=this._viewportRect,r=this._containerRect,u=[];let y;for(let N of this._preferredPositions){let J=this._getOriginPoint(e,r,N),Y=this._getOverlayPoint(J,n,N),H=this._getOverlayFit(Y,n,i,N);if(H.isCompletelyWithinViewport)return this._isPushed=!1,void this._applyPosition(N,J);this._canFitWithFlexibleDimensions(H,Y,i)?u.push({position:N,origin:J,overlayRect:n,boundingBoxRect:this._calculateBoundingBoxRect(J,N)}):(!y||y.overlayFit.visibleArea<H.visibleArea)&&(y={overlayFit:H,overlayPoint:Y,originPoint:J,position:N,overlayRect:n})}if(u.length){let N=null,J=-1;for(const Y of u){const H=Y.boundingBoxRect.width*Y.boundingBoxRect.height*(Y.position.weight||1);H>J&&(J=H,N=Y)}return this._isPushed=!1,void this._applyPosition(N.position,N.origin)}if(this._canPush)return this._isPushed=!0,void this._applyPosition(y.position,y.originPoint);this._applyPosition(y.position,y.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&Ct(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(K),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;const e=this._lastPosition;if(e){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const n=this._getOriginPoint(this._originRect,this._containerRect,e);this._applyPosition(e,n)}else this.apply()}withScrollableContainers(e){return this._scrollables=e,this}withPositions(e){return this._preferredPositions=e,-1===e.indexOf(this._lastPosition)&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(e){return this._viewportMargin=e,this}withFlexibleDimensions(e=!0){return this._hasFlexibleDimensions=e,this}withGrowAfterOpen(e=!0){return this._growAfterOpen=e,this}withPush(e=!0){return this._canPush=e,this}withLockedPosition(e=!0){return this._positionLocked=e,this}setOrigin(e){return this._origin=e,this}withDefaultOffsetX(e){return this._offsetX=e,this}withDefaultOffsetY(e){return this._offsetY=e,this}withTransformOriginOn(e){return this._transformOriginSelector=e,this}_getOriginPoint(e,n,i){let r,u;if("center"==i.originX)r=e.left+e.width/2;else{const y=this._isRtl()?e.right:e.left,N=this._isRtl()?e.left:e.right;r="start"==i.originX?y:N}return n.left<0&&(r-=n.left),u="center"==i.originY?e.top+e.height/2:"top"==i.originY?e.top:e.bottom,n.top<0&&(u-=n.top),{x:r,y:u}}_getOverlayPoint(e,n,i){let r,u;return r="center"==i.overlayX?-n.width/2:"start"===i.overlayX?this._isRtl()?-n.width:0:this._isRtl()?0:-n.width,u="center"==i.overlayY?-n.height/2:"top"==i.overlayY?0:-n.height,{x:e.x+r,y:e.y+u}}_getOverlayFit(e,n,i,r){const u=Pt(n);let{x:y,y:N}=e,J=this._getOffset(r,"x"),Y=this._getOffset(r,"y");J&&(y+=J),Y&&(N+=Y);let Tt=0-N,yt=N+u.height-i.height,Dt=this._subtractOverflows(u.width,0-y,y+u.width-i.width),Ft=this._subtractOverflows(u.height,Tt,yt),Gt=Dt*Ft;return{visibleArea:Gt,isCompletelyWithinViewport:u.width*u.height===Gt,fitsInViewportVertically:Ft===u.height,fitsInViewportHorizontally:Dt==u.width}}_canFitWithFlexibleDimensions(e,n,i){if(this._hasFlexibleDimensions){const r=i.bottom-n.y,u=i.right-n.x,y=It(this._overlayRef.getConfig().minHeight),N=It(this._overlayRef.getConfig().minWidth);return(e.fitsInViewportVertically||null!=y&&y<=r)&&(e.fitsInViewportHorizontally||null!=N&&N<=u)}return!1}_pushOverlayOnScreen(e,n,i){if(this._previousPushAmount&&this._positionLocked)return{x:e.x+this._previousPushAmount.x,y:e.y+this._previousPushAmount.y};const r=Pt(n),u=this._viewportRect,y=Math.max(e.x+r.width-u.width,0),N=Math.max(e.y+r.height-u.height,0),J=Math.max(u.top-i.top-e.y,0),Y=Math.max(u.left-i.left-e.x,0);let H=0,ht=0;return H=r.width<=u.width?Y||-y:e.x<this._viewportMargin?u.left-i.left-e.x:0,ht=r.height<=u.height?J||-N:e.y<this._viewportMargin?u.top-i.top-e.y:0,this._previousPushAmount={x:H,y:ht},{x:e.x+H,y:e.y+ht}}_applyPosition(e,n){if(this._setTransformOrigin(e),this._setOverlayElementStyles(n,e),this._setBoundingBoxStyles(n,e),e.panelClass&&this._addPanelClasses(e.panelClass),this._positionChanges.observers.length){const i=this._getScrollVisibility();if(e!==this._lastPosition||!this._lastScrollVisibility||!function Ut(w,e){return w===e||w.isOriginClipped===e.isOriginClipped&&w.isOriginOutsideView===e.isOriginOutsideView&&w.isOverlayClipped===e.isOverlayClipped&&w.isOverlayOutsideView===e.isOverlayOutsideView}(this._lastScrollVisibility,i)){const r=new bt(e,i);this._positionChanges.next(r)}this._lastScrollVisibility=i}this._lastPosition=e,this._isInitialRender=!1}_setTransformOrigin(e){if(!this._transformOriginSelector)return;const n=this._boundingBox.querySelectorAll(this._transformOriginSelector);let i,r=e.overlayY;i="center"===e.overlayX?"center":this._isRtl()?"start"===e.overlayX?"right":"left":"start"===e.overlayX?"left":"right";for(let u=0;u<n.length;u++)n[u].style.transformOrigin=`${i} ${r}`}_calculateBoundingBoxRect(e,n){const i=this._viewportRect,r=this._isRtl();let u,y,N,H,ht,Tt;if("top"===n.overlayY)y=e.y,u=i.height-y+this._viewportMargin;else if("bottom"===n.overlayY)N=i.height-e.y+2*this._viewportMargin,u=i.height-N+this._viewportMargin;else{const yt=Math.min(i.bottom-e.y+i.top,e.y),Dt=this._lastBoundingBoxSize.height;u=2*yt,y=e.y-yt,u>Dt&&!this._isInitialRender&&!this._growAfterOpen&&(y=e.y-Dt/2)}if("end"===n.overlayX&&!r||"start"===n.overlayX&&r)Tt=i.width-e.x+2*this._viewportMargin,H=e.x-this._viewportMargin;else if("start"===n.overlayX&&!r||"end"===n.overlayX&&r)ht=e.x,H=i.right-e.x;else{const yt=Math.min(i.right-e.x+i.left,e.x),Dt=this._lastBoundingBoxSize.width;H=2*yt,ht=e.x-yt,H>Dt&&!this._isInitialRender&&!this._growAfterOpen&&(ht=e.x-Dt/2)}return{top:y,left:ht,bottom:N,right:Tt,width:H,height:u}}_setBoundingBoxStyles(e,n){const i=this._calculateBoundingBoxRect(e,n);!this._isInitialRender&&!this._growAfterOpen&&(i.height=Math.min(i.height,this._lastBoundingBoxSize.height),i.width=Math.min(i.width,this._lastBoundingBoxSize.width));const r={};if(this._hasExactPosition())r.top=r.left="0",r.bottom=r.right=r.maxHeight=r.maxWidth="",r.width=r.height="100%";else{const u=this._overlayRef.getConfig().maxHeight,y=this._overlayRef.getConfig().maxWidth;r.height=(0,B.a1)(i.height),r.top=(0,B.a1)(i.top),r.bottom=(0,B.a1)(i.bottom),r.width=(0,B.a1)(i.width),r.left=(0,B.a1)(i.left),r.right=(0,B.a1)(i.right),r.alignItems="center"===n.overlayX?"center":"end"===n.overlayX?"flex-end":"flex-start",r.justifyContent="center"===n.overlayY?"center":"bottom"===n.overlayY?"flex-end":"flex-start",u&&(r.maxHeight=(0,B.a1)(u)),y&&(r.maxWidth=(0,B.a1)(y))}this._lastBoundingBoxSize=i,Ct(this._boundingBox.style,r)}_resetBoundingBoxStyles(){Ct(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){Ct(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(e,n){const i={},r=this._hasExactPosition(),u=this._hasFlexibleDimensions,y=this._overlayRef.getConfig();if(r){const H=this._viewportRuler.getViewportScrollPosition();Ct(i,this._getExactOverlayY(n,e,H)),Ct(i,this._getExactOverlayX(n,e,H))}else i.position="static";let N="",J=this._getOffset(n,"x"),Y=this._getOffset(n,"y");J&&(N+=`translateX(${J}px) `),Y&&(N+=`translateY(${Y}px)`),i.transform=N.trim(),y.maxHeight&&(r?i.maxHeight=(0,B.a1)(y.maxHeight):u&&(i.maxHeight="")),y.maxWidth&&(r?i.maxWidth=(0,B.a1)(y.maxWidth):u&&(i.maxWidth="")),Ct(this._pane.style,i)}_getExactOverlayY(e,n,i){let r={top:"",bottom:""},u=this._getOverlayPoint(n,this._overlayRect,e);return this._isPushed&&(u=this._pushOverlayOnScreen(u,this._overlayRect,i)),"bottom"===e.overlayY?r.bottom=this._document.documentElement.clientHeight-(u.y+this._overlayRect.height)+"px":r.top=(0,B.a1)(u.y),r}_getExactOverlayX(e,n,i){let y,r={left:"",right:""},u=this._getOverlayPoint(n,this._overlayRect,e);return this._isPushed&&(u=this._pushOverlayOnScreen(u,this._overlayRect,i)),y=this._isRtl()?"end"===e.overlayX?"left":"right":"end"===e.overlayX?"right":"left","right"===y?r.right=this._document.documentElement.clientWidth-(u.x+this._overlayRect.width)+"px":r.left=(0,B.a1)(u.x),r}_getScrollVisibility(){const e=this._getOriginRect(),n=this._pane.getBoundingClientRect(),i=this._scrollables.map(r=>r.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:O(e,i),isOriginOutsideView:k(e,i),isOverlayClipped:O(n,i),isOverlayOutsideView:k(n,i)}}_subtractOverflows(e,...n){return n.reduce((i,r)=>i-Math.max(r,0),e)}_getNarrowedViewportRect(){const e=this._document.documentElement.clientWidth,n=this._document.documentElement.clientHeight,i=this._viewportRuler.getViewportScrollPosition();return{top:i.top+this._viewportMargin,left:i.left+this._viewportMargin,right:i.left+e-this._viewportMargin,bottom:i.top+n-this._viewportMargin,width:e-2*this._viewportMargin,height:n-2*this._viewportMargin}}_isRtl(){return"rtl"===this._overlayRef.getDirection()}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(e,n){return"x"===n?null==e.offsetX?this._offsetX:e.offsetX:null==e.offsetY?this._offsetY:e.offsetY}_validatePositions(){}_addPanelClasses(e){this._pane&&(0,B.FG)(e).forEach(n=>{""!==n&&-1===this._appliedPanelClasses.indexOf(n)&&(this._appliedPanelClasses.push(n),this._pane.classList.add(n))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(e=>{this._pane.classList.remove(e)}),this._appliedPanelClasses=[])}_getOriginRect(){const e=this._origin;if(e instanceof s.aKT)return e.nativeElement.getBoundingClientRect();if(e instanceof Element)return e.getBoundingClientRect();const n=e.width||0,i=e.height||0;return{top:e.y,bottom:e.y+i,left:e.x,right:e.x+n,height:i,width:n}}}function Ct(w,e){for(let n in e)e.hasOwnProperty(n)&&(w[n]=e[n]);return w}function It(w){if("number"!=typeof w&&null!=w){const[e,n]=w.split(vt);return n&&"px"!==n?null:parseFloat(e)}return w||null}function Pt(w){return{top:Math.floor(w.top),right:Math.floor(w.right),bottom:Math.floor(w.bottom),left:Math.floor(w.left),width:Math.floor(w.width),height:Math.floor(w.height)}}const Lt="cdk-global-overlay-wrapper";class p{constructor(){this._cssPosition="static",this._topOffset="",this._bottomOffset="",this._alignItems="",this._xPosition="",this._xOffset="",this._width="",this._height="",this._isDisposed=!1}attach(e){const n=e.getConfig();this._overlayRef=e,this._width&&!n.width&&e.updateSize({width:this._width}),this._height&&!n.height&&e.updateSize({height:this._height}),e.hostElement.classList.add(Lt),this._isDisposed=!1}top(e=""){return this._bottomOffset="",this._topOffset=e,this._alignItems="flex-start",this}left(e=""){return this._xOffset=e,this._xPosition="left",this}bottom(e=""){return this._topOffset="",this._bottomOffset=e,this._alignItems="flex-end",this}right(e=""){return this._xOffset=e,this._xPosition="right",this}start(e=""){return this._xOffset=e,this._xPosition="start",this}end(e=""){return this._xOffset=e,this._xPosition="end",this}width(e=""){return this._overlayRef?this._overlayRef.updateSize({width:e}):this._width=e,this}height(e=""){return this._overlayRef?this._overlayRef.updateSize({height:e}):this._height=e,this}centerHorizontally(e=""){return this.left(e),this._xPosition="center",this}centerVertically(e=""){return this.top(e),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;const e=this._overlayRef.overlayElement.style,n=this._overlayRef.hostElement.style,i=this._overlayRef.getConfig(),{width:r,height:u,maxWidth:y,maxHeight:N}=i,J=!("100%"!==r&&"100vw"!==r||y&&"100%"!==y&&"100vw"!==y),Y=!("100%"!==u&&"100vh"!==u||N&&"100%"!==N&&"100vh"!==N),H=this._xPosition,ht=this._xOffset,Tt="rtl"===this._overlayRef.getConfig().direction;let yt="",Dt="",Ft="";J?Ft="flex-start":"center"===H?(Ft="center",Tt?Dt=ht:yt=ht):Tt?"left"===H||"end"===H?(Ft="flex-end",yt=ht):("right"===H||"start"===H)&&(Ft="flex-start",Dt=ht):"left"===H||"start"===H?(Ft="flex-start",yt=ht):("right"===H||"end"===H)&&(Ft="flex-end",Dt=ht),e.position=this._cssPosition,e.marginLeft=J?"0":yt,e.marginTop=Y?"0":this._topOffset,e.marginBottom=this._bottomOffset,e.marginRight=J?"0":Dt,n.justifyContent=Ft,n.alignItems=Y?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;const e=this._overlayRef.overlayElement.style,n=this._overlayRef.hostElement,i=n.style;n.classList.remove(Lt),i.justifyContent=i.alignItems=e.marginTop=e.marginBottom=e.marginLeft=e.marginRight=e.position="",this._overlayRef=null,this._isDisposed=!0}}let D=(()=>{class w{constructor(n,i,r,u){this._viewportRuler=n,this._document=i,this._platform=r,this._overlayContainer=u}global(){return new p}flexibleConnectedTo(n){return new kt(n,this._viewportRuler,this._document,this._platform,this._overlayContainer)}static{this.\u0275fac=function(i){return new(i||w)(s.KVO(t.Xj),s.KVO(h.qQ),s.KVO(nt.OD),s.KVO(j))}}static{this.\u0275prov=s.jDH({token:w,factory:w.\u0275fac,providedIn:"root"})}}return w})(),I=0,Q=(()=>{class w{constructor(n,i,r,u,y,N,J,Y,H,ht,Tt,yt){this.scrollStrategies=n,this._overlayContainer=i,this._componentFactoryResolver=r,this._positionBuilder=u,this._keyboardDispatcher=y,this._injector=N,this._ngZone=J,this._document=Y,this._directionality=H,this._location=ht,this._outsideClickDispatcher=Tt,this._animationsModuleType=yt}create(n){const i=this._createHostElement(),r=this._createPaneElement(i),u=this._createPortalOutlet(r),y=new rt(n);return y.direction=y.direction||this._directionality.value,new U(u,i,r,y,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,"NoopAnimations"===this._animationsModuleType)}position(){return this._positionBuilder}_createPaneElement(n){const i=this._document.createElement("div");return i.id="cdk-overlay-"+I++,i.classList.add("cdk-overlay-pane"),n.appendChild(i),i}_createHostElement(){const n=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(n),n}_createPortalOutlet(n){return this._appRef||(this._appRef=this._injector.get(s.o8S)),new it.aI(n,this._componentFactoryResolver,this._appRef,this._injector,this._document)}static{this.\u0275fac=function(i){return new(i||w)(s.KVO(v),s.KVO(j),s.KVO(s.OM3),s.KVO(D),s.KVO(ut),s.KVO(s.zZn),s.KVO(s.SKi),s.KVO(h.qQ),s.KVO(tt.dS),s.KVO(h.aZ),s.KVO(Mt),s.KVO(s.bc$,8))}}static{this.\u0275prov=s.jDH({token:w,factory:w.\u0275fac,providedIn:"root"})}}return w})();const _t=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],xt=new s.nKC("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{const w=(0,s.WQX)(Q);return()=>w.scrollStrategies.reposition()}});let Et=(()=>{class w{constructor(n){this.elementRef=n}static{this.\u0275fac=function(i){return new(i||w)(s.rXU(s.aKT))}}static{this.\u0275dir=s.FsC({type:w,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"],standalone:!0})}}return w})(),zt=(()=>{class w{get offsetX(){return this._offsetX}set offsetX(n){this._offsetX=n,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(n){this._offsetY=n,this._position&&this._updatePositionStrategy(this._position)}get disposeOnNavigation(){return this._disposeOnNavigation}set disposeOnNavigation(n){this._disposeOnNavigation=n}constructor(n,i,r,u,y){this._overlay=n,this._dir=y,this._backdropSubscription=pt.yU.EMPTY,this._attachSubscription=pt.yU.EMPTY,this._detachSubscription=pt.yU.EMPTY,this._positionSubscription=pt.yU.EMPTY,this._disposeOnNavigation=!1,this._ngZone=(0,s.WQX)(s.SKi),this.viewportMargin=0,this.open=!1,this.disableClose=!1,this.hasBackdrop=!1,this.lockPosition=!1,this.flexibleDimensions=!1,this.growAfterOpen=!1,this.push=!1,this.backdropClick=new s.bkB,this.positionChange=new s.bkB,this.attach=new s.bkB,this.detach=new s.bkB,this.overlayKeydown=new s.bkB,this.overlayOutsideClick=new s.bkB,this._templatePortal=new it.VA(i,r),this._scrollStrategyFactory=u,this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef&&this._overlayRef.dispose()}ngOnChanges(n){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),n.origin&&this.open&&this._position.apply()),n.open&&(this.open?this._attachOverlay():this._detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=_t);const n=this._overlayRef=this._overlay.create(this._buildConfig());this._attachSubscription=n.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=n.detachments().subscribe(()=>this.detach.emit()),n.keydownEvents().subscribe(i=>{this.overlayKeydown.next(i),i.keyCode===S._f&&!this.disableClose&&!(0,S.rp)(i)&&(i.preventDefault(),this._detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(i=>{const r=this._getOriginElement(),u=(0,nt.Fb)(i);(!r||r!==u&&!r.contains(u))&&this.overlayOutsideClick.next(i)})}_buildConfig(){const n=this._position=this.positionStrategy||this._createPositionStrategy(),i=new rt({direction:this._dir,positionStrategy:n,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop,disposeOnNavigation:this.disposeOnNavigation});return(this.width||0===this.width)&&(i.width=this.width),(this.height||0===this.height)&&(i.height=this.height),(this.minWidth||0===this.minWidth)&&(i.minWidth=this.minWidth),(this.minHeight||0===this.minHeight)&&(i.minHeight=this.minHeight),this.backdropClass&&(i.backdropClass=this.backdropClass),this.panelClass&&(i.panelClass=this.panelClass),i}_updatePositionStrategy(n){const i=this.positions.map(r=>({originX:r.originX,originY:r.originY,overlayX:r.overlayX,overlayY:r.overlayY,offsetX:r.offsetX||this.offsetX,offsetY:r.offsetY||this.offsetY,panelClass:r.panelClass||void 0}));return n.setOrigin(this._getOrigin()).withPositions(i).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){const n=this._overlay.position().flexibleConnectedTo(this._getOrigin());return this._updatePositionStrategy(n),n}_getOrigin(){return this.origin instanceof Et?this.origin.elementRef:this.origin}_getOriginElement(){return this.origin instanceof Et?this.origin.elementRef.nativeElement:this.origin instanceof s.aKT?this.origin.nativeElement:typeof Element<"u"&&this.origin instanceof Element?this.origin:null}_attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(n=>{this.backdropClick.emit(n)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe(function ct(w,e=!1){return(0,A.N)((n,i)=>{let r=0;n.subscribe((0,P._)(i,u=>{const y=w(u,r++);(y||e)&&i.next(u),!y&&i.complete()}))})}(()=>this.positionChange.observers.length>0)).subscribe(n=>{this._ngZone.run(()=>this.positionChange.emit(n)),0===this.positionChange.observers.length&&this._positionSubscription.unsubscribe()}))}_detachOverlay(){this._overlayRef&&this._overlayRef.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe()}static{this.\u0275fac=function(i){return new(i||w)(s.rXU(Q),s.rXU(s.C4Q),s.rXU(s.c1b),s.rXU(xt),s.rXU(tt.dS,8))}}static{this.\u0275dir=s.FsC({type:w,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:[0,"cdkConnectedOverlayOrigin","origin"],positions:[0,"cdkConnectedOverlayPositions","positions"],positionStrategy:[0,"cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:[0,"cdkConnectedOverlayOffsetX","offsetX"],offsetY:[0,"cdkConnectedOverlayOffsetY","offsetY"],width:[0,"cdkConnectedOverlayWidth","width"],height:[0,"cdkConnectedOverlayHeight","height"],minWidth:[0,"cdkConnectedOverlayMinWidth","minWidth"],minHeight:[0,"cdkConnectedOverlayMinHeight","minHeight"],backdropClass:[0,"cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:[0,"cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:[0,"cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:[0,"cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:[0,"cdkConnectedOverlayOpen","open"],disableClose:[0,"cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:[0,"cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:[2,"cdkConnectedOverlayHasBackdrop","hasBackdrop",s.L39],lockPosition:[2,"cdkConnectedOverlayLockPosition","lockPosition",s.L39],flexibleDimensions:[2,"cdkConnectedOverlayFlexibleDimensions","flexibleDimensions",s.L39],growAfterOpen:[2,"cdkConnectedOverlayGrowAfterOpen","growAfterOpen",s.L39],push:[2,"cdkConnectedOverlayPush","push",s.L39],disposeOnNavigation:[2,"cdkConnectedOverlayDisposeOnNavigation","disposeOnNavigation",s.L39]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],standalone:!0,features:[s.GFd,s.OA$]})}}return w})();const Nt={provide:xt,deps:[Q],useFactory:function Vt(w){return()=>w.scrollStrategies.reposition()}};let Xt=(()=>{class w{static{this.\u0275fac=function(i){return new(i||w)}}static{this.\u0275mod=s.$C({type:w})}static{this.\u0275inj=s.G2t({providers:[Q,Nt],imports:[tt.jI,it.jc,t.E9,t.E9]})}}return w})()},6860:(St,ft,f)=>{f.d(ft,{BQ:()=>ct,CZ:()=>pt,Fb:()=>lt,KT:()=>X,OD:()=>B,v8:()=>q,vc:()=>dt});var t=f(3953),h=f(177);let s;try{s=typeof Intl<"u"&&Intl.v8BreakIterator}catch{s=!1}let A,et,S,B=(()=>{class k{constructor(T){this._platformId=T,this.isBrowser=this._platformId?(0,h.UE)(this._platformId):"object"==typeof document&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!(!window.chrome&&!s)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window),this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}static{this.\u0275fac=function(v){return new(v||k)(t.KVO(t.Agw))}}static{this.\u0275prov=t.jDH({token:k,factory:k.\u0275fac,providedIn:"root"})}}return k})();function ct(k){return function P(){if(null==A&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>A=!0}))}finally{A=A||!1}return A}()?k:!!k.capture}function pt(){if(null==et){if("object"!=typeof document||!document||"function"!=typeof Element||!Element)return et=!1,et;if("scrollBehavior"in document.documentElement.style)et=!0;else{const k=Element.prototype.scrollTo;et=!!k&&!/\{\s*\[native code\]\s*\}/.test(k.toString())}}return et}function X(k){if(function L(){if(null==S){const k=typeof document<"u"?document.head:null;S=!(!k||!k.createShadowRoot&&!k.attachShadow)}return S}()){const O=k.getRootNode?k.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&O instanceof ShadowRoot)return O}return null}function dt(){let k=typeof document<"u"&&document?document.activeElement:null;for(;k&&k.shadowRoot;){const O=k.shadowRoot.activeElement;if(O===k)break;k=O}return k}function lt(k){return k.composedPath?k.composedPath()[0]:k.target}function q(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}},6939:(St,ft,f)=>{f.d(ft,{A8:()=>P,I3:()=>X,VA:()=>ct,aI:()=>pt,jc:()=>lt});var t=f(3953),h=f(177);class A{attach(O){return this._attachedHost=O,O.attach(this)}detach(){let O=this._attachedHost;null!=O&&(this._attachedHost=null,O.detach())}get isAttached(){return null!=this._attachedHost}setAttachedHost(O){this._attachedHost=O}}class P extends A{constructor(O,T,v,rt,st){super(),this.component=O,this.viewContainerRef=T,this.injector=v,this.componentFactoryResolver=rt,this.projectableNodes=st}}class ct extends A{constructor(O,T,v,rt){super(),this.templateRef=O,this.viewContainerRef=T,this.context=v,this.injector=rt}get origin(){return this.templateRef.elementRef}attach(O,T=this.context){return this.context=T,super.attach(O)}detach(){return this.context=void 0,super.detach()}}class tt extends A{constructor(O){super(),this.element=O instanceof t.aKT?O.nativeElement:O}}class it{constructor(){this._isDisposed=!1,this.attachDomPortal=null}hasAttached(){return!!this._attachedPortal}attach(O){return O instanceof P?(this._attachedPortal=O,this.attachComponentPortal(O)):O instanceof ct?(this._attachedPortal=O,this.attachTemplatePortal(O)):this.attachDomPortal&&O instanceof tt?(this._attachedPortal=O,this.attachDomPortal(O)):void 0}detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(O){this._disposeFn=O}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}}class pt extends it{constructor(O,T,v,rt,st){super(),this.outletElement=O,this._componentFactoryResolver=T,this._appRef=v,this._defaultInjector=rt,this.attachDomPortal=V=>{const bt=V.element,At=this._document.createComment("dom-portal");bt.parentNode.insertBefore(At,bt),this.outletElement.appendChild(bt),this._attachedPortal=V,super.setDisposeFn(()=>{At.parentNode&&At.parentNode.replaceChild(bt,At)})},this._document=st}attachComponentPortal(O){const v=(O.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(O.component);let rt;return O.viewContainerRef?(rt=O.viewContainerRef.createComponent(v,O.viewContainerRef.length,O.injector||O.viewContainerRef.injector,O.projectableNodes||void 0),this.setDisposeFn(()=>rt.destroy())):(rt=v.create(O.injector||this._defaultInjector||t.zZn.NULL),this._appRef.attachView(rt.hostView),this.setDisposeFn(()=>{this._appRef.viewCount>0&&this._appRef.detachView(rt.hostView),rt.destroy()})),this.outletElement.appendChild(this._getComponentRootNode(rt)),this._attachedPortal=O,rt}attachTemplatePortal(O){let T=O.viewContainerRef,v=T.createEmbeddedView(O.templateRef,O.context,{injector:O.injector});return v.rootNodes.forEach(rt=>this.outletElement.appendChild(rt)),v.detectChanges(),this.setDisposeFn(()=>{let rt=T.indexOf(v);-1!==rt&&T.remove(rt)}),this._attachedPortal=O,v}dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(O){return O.hostView.rootNodes[0]}}let X=(()=>{class k extends it{constructor(T,v,rt){super(),this._componentFactoryResolver=T,this._viewContainerRef=v,this._isInitialized=!1,this.attached=new t.bkB,this.attachDomPortal=st=>{const V=st.element,bt=this._document.createComment("dom-portal");st.setAttachedHost(this),V.parentNode.insertBefore(bt,V),this._getRootNode().appendChild(V),this._attachedPortal=st,super.setDisposeFn(()=>{bt.parentNode&&bt.parentNode.replaceChild(V,bt)})},this._document=rt}get portal(){return this._attachedPortal}set portal(T){this.hasAttached()&&!T&&!this._isInitialized||(this.hasAttached()&&super.detach(),T&&super.attach(T),this._attachedPortal=T||null)}get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(T){T.setAttachedHost(this);const v=null!=T.viewContainerRef?T.viewContainerRef:this._viewContainerRef,st=(T.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(T.component),V=v.createComponent(st,v.length,T.injector||v.injector,T.projectableNodes||void 0);return v!==this._viewContainerRef&&this._getRootNode().appendChild(V.hostView.rootNodes[0]),super.setDisposeFn(()=>V.destroy()),this._attachedPortal=T,this._attachedRef=V,this.attached.emit(V),V}attachTemplatePortal(T){T.setAttachedHost(this);const v=this._viewContainerRef.createEmbeddedView(T.templateRef,T.context,{injector:T.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=T,this._attachedRef=v,this.attached.emit(v),v}_getRootNode(){const T=this._viewContainerRef.element.nativeElement;return T.nodeType===T.ELEMENT_NODE?T:T.parentNode}static{this.\u0275fac=function(v){return new(v||k)(t.rXU(t.OM3),t.rXU(t.c1b),t.rXU(h.qQ))}}static{this.\u0275dir=t.FsC({type:k,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:[0,"cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],standalone:!0,features:[t.Vt3]})}}return k})(),lt=(()=>{class k{static{this.\u0275fac=function(v){return new(v||k)}}static{this.\u0275mod=t.$C({type:k})}static{this.\u0275inj=t.G2t({})}}return k})()},3980:(St,ft,f)=>{f.d(ft,{Gj:()=>Bt,R:()=>ot,E9:()=>w,Xj:()=>D});var t=f(4085),h=f(3953),s=f(1413),B=f(7673),nt=f(1985),Z=f(3726),z=f(6780),W=f(8359);const A={schedule(e){let n=requestAnimationFrame,i=cancelAnimationFrame;const{delegate:r}=A;r&&(n=r.requestAnimationFrame,i=r.cancelAnimationFrame);const u=n(y=>{i=void 0,e(y)});return new W.yU(()=>i?.(u))},requestAnimationFrame(...e){const{delegate:n}=A;return(n?.requestAnimationFrame||requestAnimationFrame)(...e)},cancelAnimationFrame(...e){const{delegate:n}=A;return(n?.cancelAnimationFrame||cancelAnimationFrame)(...e)},delegate:void 0};var ct=f(9687);new class tt extends ct.q{flush(n){let i;this._active=!0,n?i=n.id:(i=this._scheduled,this._scheduled=void 0);const{actions:r}=this;let u;n=n||r.shift();do{if(u=n.execute(n.state,n.delay))break}while((n=r[0])&&n.id===i&&r.shift());if(this._active=!1,u){for(;(n=r[0])&&n.id===i&&r.shift();)n.unsubscribe();throw u}}}(class P extends z.R{constructor(n,i){super(n,i),this.scheduler=n,this.work=i}requestAsyncId(n,i,r=0){return null!==r&&r>0?super.requestAsyncId(n,i,r):(n.actions.push(this),n._scheduled||(n._scheduled=A.requestAnimationFrame(()=>n.flush(void 0))))}recycleAsyncId(n,i,r=0){var u;if(null!=r?r>0:this.delay>0)return super.recycleAsyncId(n,i,r);const{actions:y}=n;null!=i&&i===n._scheduled&&(null===(u=y[y.length-1])||void 0===u?void 0:u.id)!==i&&(A.cancelAnimationFrame(i),n._scheduled=void 0)}});let $,pt=1;const S={};function L(e){return e in S&&(delete S[e],!0)}const X={setImmediate(e){const n=pt++;return S[n]=!0,$||($=Promise.resolve()),$.then(()=>L(n)&&e()),n},clearImmediate(e){L(e)}},{setImmediate:lt,clearImmediate:q}=X,k={setImmediate(...e){const{delegate:n}=k;return(n?.setImmediate||lt)(...e)},clearImmediate(e){const{delegate:n}=k;return(n?.clearImmediate||q)(e)},delegate:void 0};new class T extends ct.q{flush(n){this._active=!0;const i=this._scheduled;this._scheduled=void 0;const{actions:r}=this;let u;n=n||r.shift();do{if(u=n.execute(n.state,n.delay))break}while((n=r[0])&&n.id===i&&r.shift());if(this._active=!1,u){for(;(n=r[0])&&n.id===i&&r.shift();)n.unsubscribe();throw u}}}(class O extends z.R{constructor(n,i){super(n,i),this.scheduler=n,this.work=i}requestAsyncId(n,i,r=0){return null!==r&&r>0?super.requestAsyncId(n,i,r):(n.actions.push(this),n._scheduled||(n._scheduled=k.setImmediate(n.flush.bind(n,void 0))))}recycleAsyncId(n,i,r=0){var u;if(null!=r?r>0:this.delay>0)return super.recycleAsyncId(n,i,r);const{actions:y}=n;null!=i&&(null===(u=y[y.length-1])||void 0===u?void 0:u.id)!==i&&(k.clearImmediate(i),n._scheduled===i&&(n._scheduled=void 0))}});var st=f(3236),V=f(9974),bt=f(8750),At=f(4360),Rt=f(1807);function ut(e,n=st.E){return function jt(e){return(0,V.N)((n,i)=>{let r=!1,u=null,y=null,N=!1;const J=()=>{if(y?.unsubscribe(),y=null,r){r=!1;const H=u;u=null,i.next(H)}N&&i.complete()},Y=()=>{y=null,N&&i.complete()};n.subscribe((0,At._)(i,H=>{r=!0,u=H,y||(0,bt.Tg)(e(H)).subscribe(y=(0,At._)(i,J,Y))},()=>{N=!0,(!r||!y||y.closed)&&i.complete()}))})}(()=>(0,Rt.O)(e,n))}var Mt=f(5964),j=f(6860),U=f(177),K=f(8203);let ot=(()=>{class e{constructor(i,r,u){this._ngZone=i,this._platform=r,this._scrolled=new s.B,this._globalSubscription=null,this._scrolledCount=0,this.scrollContainers=new Map,this._document=u}register(i){this.scrollContainers.has(i)||this.scrollContainers.set(i,i.elementScrolled().subscribe(()=>this._scrolled.next(i)))}deregister(i){const r=this.scrollContainers.get(i);r&&(r.unsubscribe(),this.scrollContainers.delete(i))}scrolled(i=20){return this._platform.isBrowser?new nt.c(r=>{this._globalSubscription||this._addGlobalListener();const u=i>0?this._scrolled.pipe(ut(i)).subscribe(r):this._scrolled.subscribe(r);return this._scrolledCount++,()=>{u.unsubscribe(),this._scrolledCount--,this._scrolledCount||this._removeGlobalListener()}}):(0,B.of)()}ngOnDestroy(){this._removeGlobalListener(),this.scrollContainers.forEach((i,r)=>this.deregister(r)),this._scrolled.complete()}ancestorScrolled(i,r){const u=this.getAncestorScrollContainers(i);return this.scrolled(r).pipe((0,Mt.p)(y=>!y||u.indexOf(y)>-1))}getAncestorScrollContainers(i){const r=[];return this.scrollContainers.forEach((u,y)=>{this._scrollableContainsElement(y,i)&&r.push(y)}),r}_getWindow(){return this._document.defaultView||window}_scrollableContainsElement(i,r){let u=(0,t.i8)(r),y=i.getElementRef().nativeElement;do{if(u==y)return!0}while(u=u.parentElement);return!1}_addGlobalListener(){this._globalSubscription=this._ngZone.runOutsideAngular(()=>{const i=this._getWindow();return(0,Z.R)(i.document,"scroll").subscribe(()=>this._scrolled.next())})}_removeGlobalListener(){this._globalSubscription&&(this._globalSubscription.unsubscribe(),this._globalSubscription=null)}static{this.\u0275fac=function(r){return new(r||e)(h.KVO(h.SKi),h.KVO(j.OD),h.KVO(U.qQ,8))}}static{this.\u0275prov=h.jDH({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),D=(()=>{class e{constructor(i,r,u){this._platform=i,this._change=new s.B,this._changeListener=y=>{this._change.next(y)},this._document=u,r.runOutsideAngular(()=>{if(i.isBrowser){const y=this._getWindow();y.addEventListener("resize",this._changeListener),y.addEventListener("orientationchange",this._changeListener)}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){if(this._platform.isBrowser){const i=this._getWindow();i.removeEventListener("resize",this._changeListener),i.removeEventListener("orientationchange",this._changeListener)}this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();const i={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),i}getViewportRect(){const i=this.getViewportScrollPosition(),{width:r,height:u}=this.getViewportSize();return{top:i.top,left:i.left,bottom:i.top+u,right:i.left+r,height:u,width:r}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};const i=this._document,r=this._getWindow(),u=i.documentElement,y=u.getBoundingClientRect();return{top:-y.top||i.body.scrollTop||r.scrollY||u.scrollTop||0,left:-y.left||i.body.scrollLeft||r.scrollX||u.scrollLeft||0}}change(i=20){return i>0?this._change.pipe(ut(i)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){const i=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:i.innerWidth,height:i.innerHeight}:{width:0,height:0}}static{this.\u0275fac=function(r){return new(r||e)(h.KVO(j.OD),h.KVO(h.SKi),h.KVO(U.qQ,8))}}static{this.\u0275prov=h.jDH({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Bt=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=h.$C({type:e})}static{this.\u0275inj=h.G2t({})}}return e})(),w=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=h.$C({type:e})}static{this.\u0275inj=h.G2t({imports:[K.jI,Bt,K.jI,Bt]})}}return e})()},8834:(St,ft,f)=>{f.d(ft,{$z:()=>q,Hl:()=>Rt,iY:()=>At});var t=f(6860),h=f(3953),s=f(6039),B=f(6600);const nt=["mat-button",""],Z=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],z=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"],it=["mat-icon-button",""],et=["*"],$=new h.nKC("MAT_BUTTON_CONFIG"),L=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}];let X=(()=>{class ut{get ripple(){return this._rippleLoader?.getRipple(this._elementRef.nativeElement)}set ripple(j){this._rippleLoader?.attachRipple(this._elementRef.nativeElement,j)}get disableRipple(){return this._disableRipple}set disableRipple(j){this._disableRipple=j,this._updateRippleDisabled()}get disabled(){return this._disabled}set disabled(j){this._disabled=j,this._updateRippleDisabled()}constructor(j,U,K,vt){this._elementRef=j,this._platform=U,this._ngZone=K,this._animationMode=vt,this._focusMonitor=(0,h.WQX)(s.FN),this._rippleLoader=(0,h.WQX)(B.Ej),this._isFab=!1,this._disableRipple=!1,this._disabled=!1;const kt=(0,h.WQX)($,{optional:!0}),Ct=j.nativeElement,It=Ct.classList;this.disabledInteractive=kt?.disabledInteractive??!1,this._rippleLoader?.configureRipple(Ct,{className:"mat-mdc-button-ripple"});for(const{attribute:Pt,mdcClasses:Ut}of L)Ct.hasAttribute(Pt)&&It.add(...Ut)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(j="program",U){j?this._focusMonitor.focusVia(this._elementRef.nativeElement,j,U):this._elementRef.nativeElement.focus(U)}_getAriaDisabled(){return null!=this.ariaDisabled?this.ariaDisabled:!(!this.disabled||!this.disabledInteractive)||null}_getDisabledAttribute(){return!(this.disabledInteractive||!this.disabled)||null}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static{this.\u0275fac=function(U){h.QTQ()}}static{this.\u0275dir=h.FsC({type:ut,inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",h.L39],disabled:[2,"disabled","disabled",h.L39],ariaDisabled:[2,"aria-disabled","ariaDisabled",h.L39],disabledInteractive:[2,"disabledInteractive","disabledInteractive",h.L39]},features:[h.GFd]})}}return ut})(),q=(()=>{class ut extends X{constructor(j,U,K,vt){super(j,U,K,vt)}static{this.\u0275fac=function(U){return new(U||ut)(h.rXU(h.aKT),h.rXU(t.OD),h.rXU(h.SKi),h.rXU(h.bc$,8))}}static{this.\u0275cmp=h.VBU({type:ut,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(U,K){2&U&&(h.BMQ("disabled",K._getDisabledAttribute())("aria-disabled",K._getAriaDisabled()),h.HbH(K.color?"mat-"+K.color:""),h.AVh("mat-mdc-button-disabled",K.disabled)("mat-mdc-button-disabled-interactive",K.disabledInteractive)("_mat-animation-noopable","NoopAnimations"===K._animationMode)("mat-unthemed",!K.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],standalone:!0,features:[h.Vt3,h.aNF],attrs:nt,ngContentSelectors:z,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(U,K){1&U&&(h.NAR(Z),h.nrm(0,"span",0),h.SdG(1),h.j41(2,"span",1),h.SdG(3,1),h.k0s(),h.SdG(4,2),h.nrm(5,"span",2)(6,"span",3)),2&U&&h.AVh("mdc-button__ripple",!K._isFab)("mdc-fab__ripple",K._isFab)},styles:['.mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button{position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;user-select:none;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0)}.mdc-button .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top}[dir=rtl] .mdc-button .mdc-button__icon,.mdc-button .mdc-button__icon[dir=rtl]{margin-left:8px;margin-right:0}.mdc-button .mdc-button__progress-indicator{font-size:0;position:absolute;transform:translate(-50%, -50%);top:50%;left:50%;line-height:initial}.mdc-button .mdc-button__label{position:relative}.mdc-button .mdc-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px);display:none}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring{border-color:CanvasText}}.mdc-button .mdc-button__focus-ring::after{content:"";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring::after{border-color:CanvasText}}@media screen and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{display:block}}.mdc-button .mdc-button__touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:0}[dir=rtl] .mdc-button__label+.mdc-button__icon,.mdc-button__label+.mdc-button__icon[dir=rtl]{margin-left:0;margin-right:8px}svg.mdc-button__icon{fill:currentColor}.mdc-button--touch{margin-top:6px;margin-bottom:6px}.mdc-button{padding:0 8px 0 8px}.mdc-button--unelevated{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--unelevated.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--unelevated.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--raised{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--raised.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--raised.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--outlined{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button--outlined .mdc-button__ripple{border-style:solid;border-color:rgba(0,0,0,0)}.mat-mdc-button{font-family:var(--mdc-text-button-label-text-font);font-size:var(--mdc-text-button-label-text-size);letter-spacing:var(--mdc-text-button-label-text-tracking);font-weight:var(--mdc-text-button-label-text-weight);text-transform:var(--mdc-text-button-label-text-transform);height:var(--mdc-text-button-container-height);border-radius:var(--mdc-text-button-container-shape);padding:0 var(--mat-text-button-horizontal-padding, 8px)}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color)}.mat-mdc-button:disabled{color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape)}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 8px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color)}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color)}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color)}.mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity)}.mat-mdc-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity)}.mat-mdc-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity)}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display)}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-unelevated-button{font-family:var(--mdc-filled-button-label-text-font);font-size:var(--mdc-filled-button-label-text-size);letter-spacing:var(--mdc-filled-button-label-text-tracking);font-weight:var(--mdc-filled-button-label-text-weight);text-transform:var(--mdc-filled-button-label-text-transform);height:var(--mdc-filled-button-container-height);border-radius:var(--mdc-filled-button-container-shape);padding:0 var(--mat-filled-button-horizontal-padding, 16px)}.mat-mdc-unelevated-button:not(:disabled){background-color:var(--mdc-filled-button-container-color)}.mat-mdc-unelevated-button:disabled{background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color)}.mat-mdc-unelevated-button:disabled{color:var(--mdc-filled-button-disabled-label-text-color)}.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color)}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color)}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color)}.mat-mdc-unelevated-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity)}.mat-mdc-unelevated-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity)}.mat-mdc-unelevated-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity)}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display)}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color);background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{font-family:var(--mdc-protected-button-label-text-font);font-size:var(--mdc-protected-button-label-text-size);letter-spacing:var(--mdc-protected-button-label-text-tracking);font-weight:var(--mdc-protected-button-label-text-weight);text-transform:var(--mdc-protected-button-label-text-transform);height:var(--mdc-protected-button-container-height);border-radius:var(--mdc-protected-button-container-shape);padding:0 var(--mat-protected-button-horizontal-padding, 16px);box-shadow:var(--mdc-protected-button-container-elevation-shadow)}.mat-mdc-raised-button:not(:disabled){background-color:var(--mdc-protected-button-container-color)}.mat-mdc-raised-button:disabled{background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color)}.mat-mdc-raised-button:disabled{color:var(--mdc-protected-button-disabled-label-text-color)}.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color)}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color)}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color)}.mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity)}.mat-mdc-raised-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity)}.mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity)}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display)}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow)}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow)}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow)}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color);background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow)}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{font-family:var(--mdc-outlined-button-label-text-font);font-size:var(--mdc-outlined-button-label-text-size);letter-spacing:var(--mdc-outlined-button-label-text-tracking);font-weight:var(--mdc-outlined-button-label-text-weight);text-transform:var(--mdc-outlined-button-label-text-transform);height:var(--mdc-outlined-button-container-height);border-radius:var(--mdc-outlined-button-container-shape);padding:0 15px 0 15px;border-width:var(--mdc-outlined-button-outline-width);padding:0 var(--mat-outlined-button-horizontal-padding, 15px)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color)}.mat-mdc-outlined-button:disabled{color:var(--mdc-outlined-button-disabled-label-text-color)}.mat-mdc-outlined-button .mdc-button__ripple{border-radius:var(--mdc-outlined-button-container-shape)}.mat-mdc-outlined-button:not(:disabled){border-color:var(--mdc-outlined-button-outline-color)}.mat-mdc-outlined-button:disabled{border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-outlined-button.mdc-button--icon-trailing{padding:0 11px 0 15px}.mat-mdc-outlined-button.mdc-button--icon-leading{padding:0 15px 0 11px}.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:var(--mdc-outlined-button-outline-width)}.mat-mdc-outlined-button .mdc-button__touch{left:calc(-1 * var(--mdc-outlined-button-outline-width));width:calc(100% + 2 * var(--mdc-outlined-button-outline-width))}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color)}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color)}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color)}.mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity)}.mat-mdc-outlined-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity)}.mat-mdc-outlined-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity)}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display)}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color);border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button-base{text-decoration:none}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-outlined-button .mdc-button__label{z-index:1}.mat-mdc-button .mat-mdc-focus-indicator,.mat-mdc-unelevated-button .mat-mdc-focus-indicator,.mat-mdc-raised-button .mat-mdc-focus-indicator,.mat-mdc-outlined-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:-1px}.mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,.mat-mdc-raised-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px)*-1)}',".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}"],encapsulation:2,changeDetection:0})}}return ut})(),At=(()=>{class ut extends X{constructor(j,U,K,vt){super(j,U,K,vt),this._rippleLoader.configureRipple(this._elementRef.nativeElement,{centered:!0})}static{this.\u0275fac=function(U){return new(U||ut)(h.rXU(h.aKT),h.rXU(t.OD),h.rXU(h.SKi),h.rXU(h.bc$,8))}}static{this.\u0275cmp=h.VBU({type:ut,selectors:[["button","mat-icon-button",""]],hostVars:14,hostBindings:function(U,K){2&U&&(h.BMQ("disabled",K._getDisabledAttribute())("aria-disabled",K._getAriaDisabled()),h.HbH(K.color?"mat-"+K.color:""),h.AVh("mat-mdc-button-disabled",K.disabled)("mat-mdc-button-disabled-interactive",K.disabledInteractive)("_mat-animation-noopable","NoopAnimations"===K._animationMode)("mat-unthemed",!K.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],standalone:!0,features:[h.Vt3,h.aNF],attrs:it,ngContentSelectors:et,decls:4,vars:0,consts:[[1,"mat-mdc-button-persistent-ripple","mdc-icon-button__ripple"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(U,K){1&U&&(h.NAR(),h.nrm(0,"span",0),h.SdG(1),h.nrm(2,"span",1)(3,"span",2))},styles:['.mdc-icon-button{display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;user-select:none;z-index:0;overflow:visible}.mdc-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}@media screen and (forced-colors: active){.mdc-icon-button.mdc-ripple-upgraded--background-focused .mdc-icon-button__focus-ring,.mdc-icon-button:not(.mdc-ripple-upgraded):focus .mdc-icon-button__focus-ring{display:block}}.mdc-icon-button:disabled{cursor:default;pointer-events:none}.mdc-icon-button[hidden]{display:none}.mdc-icon-button--display-flex{align-items:center;display:inline-flex;justify-content:center}.mdc-icon-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%;display:none}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring{border-color:CanvasText}}.mdc-icon-button__focus-ring::after{content:"";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring::after{border-color:CanvasText}}.mdc-icon-button__icon{display:inline-block}.mdc-icon-button__icon.mdc-icon-button__icon--on{display:none}.mdc-icon-button--on .mdc-icon-button__icon{display:none}.mdc-icon-button--on .mdc-icon-button__icon.mdc-icon-button__icon--on{display:inline-block}.mdc-icon-button__link{height:100%;left:0;outline:none;position:absolute;top:0;width:100%}.mat-mdc-icon-button{color:var(--mdc-icon-button-icon-color)}.mat-mdc-icon-button .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button svg,.mat-mdc-icon-button img{width:var(--mdc-icon-button-icon-size);height:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button:disabled{color:var(--mdc-icon-button-disabled-icon-color)}.mat-mdc-icon-button{border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 48px);height:var(--mdc-icon-button-state-layer-size, 48px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 48px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size);-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button svg{vertical-align:baseline}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color)}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-icon-button .mdc-button__label{z-index:1}.mat-mdc-icon-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-icon-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color)}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color)}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color)}.mat-mdc-icon-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity)}.mat-mdc-icon-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity)}.mat-mdc-icon-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity)}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}',".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}"],encapsulation:2,changeDetection:0})}}return ut})(),Rt=(()=>{class ut{static{this.\u0275fac=function(U){return new(U||ut)}}static{this.\u0275mod=h.$C({type:ut})}static{this.\u0275inj=h.G2t({imports:[B.yE,B.pZ,B.yE]})}}return ut})()},5596:(St,ft,f)=>{f.d(ft,{Hu:()=>T,MM:()=>$,RN:()=>P,WQ:()=>S,dh:()=>ct,m2:()=>it});var t=f(3953),h=f(177),s=f(6600);const B=["*"],z=[[["","mat-card-avatar",""],["","matCardAvatar",""]],[["mat-card-title"],["mat-card-subtitle"],["","mat-card-title",""],["","mat-card-subtitle",""],["","matCardTitle",""],["","matCardSubtitle",""]],"*"],W=["[mat-card-avatar], [matCardAvatar]","mat-card-title, mat-card-subtitle,\n      [mat-card-title], [mat-card-subtitle],\n      [matCardTitle], [matCardSubtitle]","*"],A=new t.nKC("MAT_CARD_CONFIG");let P=(()=>{class v{constructor(st){this.appearance=st?.appearance||"raised"}static{this.\u0275fac=function(V){return new(V||v)(t.rXU(A,8))}}static{this.\u0275cmp=t.VBU({type:v,selectors:[["mat-card"]],hostAttrs:[1,"mat-mdc-card","mdc-card"],hostVars:4,hostBindings:function(V,bt){2&V&&t.AVh("mat-mdc-card-outlined","outlined"===bt.appearance)("mdc-card--outlined","outlined"===bt.appearance)},inputs:{appearance:"appearance"},exportAs:["matCard"],standalone:!0,features:[t.aNF],ngContentSelectors:B,decls:1,vars:0,template:function(V,bt){1&V&&(t.NAR(),t.SdG(0))},styles:['.mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:""}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}'],encapsulation:2,changeDetection:0})}}return v})(),ct=(()=>{class v{static{this.\u0275fac=function(V){return new(V||v)}}static{this.\u0275dir=t.FsC({type:v,selectors:[["mat-card-title"],["","mat-card-title",""],["","matCardTitle",""]],hostAttrs:[1,"mat-mdc-card-title"],standalone:!0})}}return v})(),it=(()=>{class v{static{this.\u0275fac=function(V){return new(V||v)}}static{this.\u0275dir=t.FsC({type:v,selectors:[["mat-card-content"]],hostAttrs:[1,"mat-mdc-card-content"],standalone:!0})}}return v})(),$=(()=>{class v{static{this.\u0275fac=function(V){return new(V||v)}}static{this.\u0275cmp=t.VBU({type:v,selectors:[["mat-card-header"]],hostAttrs:[1,"mat-mdc-card-header"],standalone:!0,features:[t.aNF],ngContentSelectors:W,decls:4,vars:0,consts:[[1,"mat-mdc-card-header-text"]],template:function(V,bt){1&V&&(t.NAR(z),t.SdG(0),t.j41(1,"div",0),t.SdG(2,1),t.k0s(),t.SdG(3,2))},encapsulation:2,changeDetection:0})}}return v})(),S=(()=>{class v{static{this.\u0275fac=function(V){return new(V||v)}}static{this.\u0275dir=t.FsC({type:v,selectors:[["mat-card-footer"]],hostAttrs:[1,"mat-mdc-card-footer"],standalone:!0})}}return v})(),T=(()=>{class v{static{this.\u0275fac=function(V){return new(V||v)}}static{this.\u0275mod=t.$C({type:v})}static{this.\u0275inj=t.G2t({imports:[s.yE,h.MD,s.yE]})}}return v})()},6600:(St,ft,f)=>{f.d(ft,{r5:()=>lt,ed:()=>q,MJ:()=>K,es:()=>p,de:()=>vt,QC:()=>ht,is:()=>Y,$E:()=>r,yE:()=>T,wT:()=>Ft,Sy:()=>mt,r6:()=>u,Ej:()=>Zt,pZ:()=>y,X0:()=>Rt,tO:()=>Kt,jb:()=>Gt,TL:()=>E,aw:()=>ot});var t=f(3953),h=f(6039),s=f(8203),nt=f(177),Z=f(6860),z=f(4085),W=f(1413),A=f(7336);const tt=["text"],it=[[["mat-icon"]],"*"],et=["mat-icon","*"];function pt(x,b){if(1&x&&t.nrm(0,"mat-pseudo-checkbox",1),2&x){const a=t.XpG();t.Y8G("disabled",a.disabled)("state",a.selected?"checked":"unchecked")}}function $(x,b){if(1&x&&t.nrm(0,"mat-pseudo-checkbox",3),2&x){const a=t.XpG();t.Y8G("disabled",a.disabled)}}function S(x,b){if(1&x&&(t.j41(0,"span",4),t.EFF(1),t.k0s()),2&x){const a=t.XpG();t.R7$(),t.SpI("(",a.group.label,")")}}const L=["mat-internal-form-field",""],X=["*"];let lt=(()=>{class x{static{this.STANDARD_CURVE="cubic-bezier(0.4,0.0,0.2,1)"}static{this.DECELERATION_CURVE="cubic-bezier(0.0,0.0,0.2,1)"}static{this.ACCELERATION_CURVE="cubic-bezier(0.4,0.0,1,1)"}static{this.SHARP_CURVE="cubic-bezier(0.4,0.0,0.6,1)"}}return x})(),q=(()=>{class x{static{this.COMPLEX="375ms"}static{this.ENTERING="225ms"}static{this.EXITING="195ms"}}return x})();const O=new t.nKC("mat-sanity-checks",{providedIn:"root",factory:function k(){return!0}});let T=(()=>{class x{constructor(a,c,C){this._sanityChecks=c,this._document=C,this._hasDoneGlobalChecks=!1,a._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(a){return!(0,Z.v8)()&&("boolean"==typeof this._sanityChecks?this._sanityChecks:!!this._sanityChecks[a])}static{this.\u0275fac=function(c){return new(c||x)(t.KVO(h.Q_),t.KVO(O,8),t.KVO(nt.qQ))}}static{this.\u0275mod=t.$C({type:x})}static{this.\u0275inj=t.G2t({imports:[s.jI,s.jI]})}}return x})();class Rt{constructor(b,a,c,C,l){this._defaultMatcher=b,this.ngControl=a,this._parentFormGroup=c,this._parentForm=C,this._stateChanges=l,this.errorState=!1}updateErrorState(){const b=this.errorState,a=this._parentFormGroup||this._parentForm,c=this.matcher||this._defaultMatcher,C=this.ngControl?this.ngControl.control:null,l=c?.isErrorState(C,a)??!1;l!==b&&(this.errorState=l,this._stateChanges.next())}}const j=new t.nKC("MAT_DATE_LOCALE",{providedIn:"root",factory:function U(){return(0,t.WQX)(t.xe9)}});class K{constructor(){this._localeChanges=new W.B,this.localeChanges=this._localeChanges}getValidDateOrNull(b){return this.isDateInstance(b)&&this.isValid(b)?b:null}deserialize(b){return null==b||this.isDateInstance(b)&&this.isValid(b)?b:this.invalid()}setLocale(b){this.locale=b,this._localeChanges.next()}compareDate(b,a){return this.getYear(b)-this.getYear(a)||this.getMonth(b)-this.getMonth(a)||this.getDate(b)-this.getDate(a)}sameDate(b,a){if(b&&a){let c=this.isValid(b),C=this.isValid(a);return c&&C?!this.compareDate(b,a):c==C}return b==a}clampDate(b,a,c){return a&&this.compareDate(b,a)<0?a:c&&this.compareDate(b,c)>0?c:b}}const vt=new t.nKC("mat-date-formats"),kt=/^\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|(?:(?:\+|-)\d{2}:\d{2}))?)?$/;function Ct(x,b){const a=Array(x);for(let c=0;c<x;c++)a[c]=b(c);return a}let It=(()=>{class x extends K{constructor(a){super(),this.useUtcForDisplay=!1,this._matDateLocale=(0,t.WQX)(j,{optional:!0}),void 0!==a&&(this._matDateLocale=a),super.setLocale(this._matDateLocale)}getYear(a){return a.getFullYear()}getMonth(a){return a.getMonth()}getDate(a){return a.getDate()}getDayOfWeek(a){return a.getDay()}getMonthNames(a){const c=new Intl.DateTimeFormat(this.locale,{month:a,timeZone:"utc"});return Ct(12,C=>this._format(c,new Date(2017,C,1)))}getDateNames(){const a=new Intl.DateTimeFormat(this.locale,{day:"numeric",timeZone:"utc"});return Ct(31,c=>this._format(a,new Date(2017,0,c+1)))}getDayOfWeekNames(a){const c=new Intl.DateTimeFormat(this.locale,{weekday:a,timeZone:"utc"});return Ct(7,C=>this._format(c,new Date(2017,0,C+1)))}getYearName(a){const c=new Intl.DateTimeFormat(this.locale,{year:"numeric",timeZone:"utc"});return this._format(c,a)}getFirstDayOfWeek(){return 0}getNumDaysInMonth(a){return this.getDate(this._createDateWithOverflow(this.getYear(a),this.getMonth(a)+1,0))}clone(a){return new Date(a.getTime())}createDate(a,c,C){let l=this._createDateWithOverflow(a,c,C);return l.getMonth(),l}today(){return new Date}parse(a,c){return"number"==typeof a?new Date(a):a?new Date(Date.parse(a)):null}format(a,c){if(!this.isValid(a))throw Error("NativeDateAdapter: Cannot format invalid date.");const C=new Intl.DateTimeFormat(this.locale,{...c,timeZone:"utc"});return this._format(C,a)}addCalendarYears(a,c){return this.addCalendarMonths(a,12*c)}addCalendarMonths(a,c){let C=this._createDateWithOverflow(this.getYear(a),this.getMonth(a)+c,this.getDate(a));return this.getMonth(C)!=((this.getMonth(a)+c)%12+12)%12&&(C=this._createDateWithOverflow(this.getYear(C),this.getMonth(C),0)),C}addCalendarDays(a,c){return this._createDateWithOverflow(this.getYear(a),this.getMonth(a),this.getDate(a)+c)}toIso8601(a){return[a.getUTCFullYear(),this._2digit(a.getUTCMonth()+1),this._2digit(a.getUTCDate())].join("-")}deserialize(a){if("string"==typeof a){if(!a)return null;if(kt.test(a)){let c=new Date(a);if(this.isValid(c))return c}}return super.deserialize(a)}isDateInstance(a){return a instanceof Date}isValid(a){return!isNaN(a.getTime())}invalid(){return new Date(NaN)}_createDateWithOverflow(a,c,C){const l=new Date;return l.setFullYear(a,c,C),l.setHours(0,0,0,0),l}_2digit(a){return("00"+a).slice(-2)}_format(a,c){const C=new Date;return C.setUTCFullYear(c.getFullYear(),c.getMonth(),c.getDate()),C.setUTCHours(c.getHours(),c.getMinutes(),c.getSeconds(),c.getMilliseconds()),a.format(C)}static{this.\u0275fac=function(c){return new(c||x)(t.KVO(j,8))}}static{this.\u0275prov=t.jDH({token:x,factory:x.\u0275fac})}}return x})();const Pt={parse:{dateInput:null},display:{dateInput:{year:"numeric",month:"numeric",day:"numeric"},monthYearLabel:{year:"numeric",month:"short"},dateA11yLabel:{year:"numeric",month:"long",day:"numeric"},monthYearA11yLabel:{year:"numeric",month:"long"}}};function ot(x=Pt){return[{provide:K,useClass:It},{provide:vt,useValue:x}]}let p=(()=>{class x{isErrorState(a,c){return!!(a&&a.invalid&&(a.touched||c&&c.submitted))}static{this.\u0275fac=function(c){return new(c||x)}}static{this.\u0275prov=t.jDH({token:x,factory:x.\u0275fac,providedIn:"root"})}}return x})();var xt=function(x){return x[x.FADING_IN=0]="FADING_IN",x[x.VISIBLE=1]="VISIBLE",x[x.FADING_OUT=2]="FADING_OUT",x[x.HIDDEN=3]="HIDDEN",x}(xt||{});class Et{constructor(b,a,c,C=!1){this._renderer=b,this.element=a,this.config=c,this._animationForciblyDisabledThroughCss=C,this.state=xt.HIDDEN}fadeOut(){this._renderer.fadeOutRipple(this)}}const zt=(0,Z.BQ)({passive:!0,capture:!0});class Vt{constructor(){this._events=new Map,this._delegateEventHandler=b=>{const a=(0,Z.Fb)(b);a&&this._events.get(b.type)?.forEach((c,C)=>{(C===a||C.contains(a))&&c.forEach(l=>l.handleEvent(b))})}}addHandler(b,a,c,C){const l=this._events.get(a);if(l){const m=l.get(c);m?m.add(C):l.set(c,new Set([C]))}else this._events.set(a,new Map([[c,new Set([C])]])),b.runOutsideAngular(()=>{document.addEventListener(a,this._delegateEventHandler,zt)})}removeHandler(b,a,c){const C=this._events.get(b);if(!C)return;const l=C.get(a);l&&(l.delete(c),0===l.size&&C.delete(a),0===C.size&&(this._events.delete(b),document.removeEventListener(b,this._delegateEventHandler,zt)))}}const Nt={enterDuration:225,exitDuration:150},Bt=(0,Z.BQ)({passive:!0,capture:!0}),w=["mousedown","touchstart"],e=["mouseup","mouseleave","touchend","touchcancel"];class n{static{this._eventManager=new Vt}constructor(b,a,c,C){this._target=b,this._ngZone=a,this._platform=C,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,C.isBrowser&&(this._containerElement=(0,z.i8)(c))}fadeInRipple(b,a,c={}){const C=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),l={...Nt,...c.animation};c.centered&&(b=C.left+C.width/2,a=C.top+C.height/2);const m=c.radius||function i(x,b,a){const c=Math.max(Math.abs(x-a.left),Math.abs(x-a.right)),C=Math.max(Math.abs(b-a.top),Math.abs(b-a.bottom));return Math.sqrt(c*c+C*C)}(b,a,C),o=b-C.left,d=a-C.top,_=l.enterDuration,M=document.createElement("div");M.classList.add("mat-ripple-element"),M.style.left=o-m+"px",M.style.top=d-m+"px",M.style.height=2*m+"px",M.style.width=2*m+"px",null!=c.color&&(M.style.backgroundColor=c.color),M.style.transitionDuration=`${_}ms`,this._containerElement.appendChild(M);const G=window.getComputedStyle(M),Ht=G.transitionDuration,Wt="none"===G.transitionProperty||"0s"===Ht||"0s, 0s"===Ht||0===C.width&&0===C.height,$t=new Et(this,M,c,Wt);M.style.transform="scale3d(1, 1, 1)",$t.state=xt.FADING_IN,c.persistent||(this._mostRecentTransientRipple=$t);let Jt=null;return!Wt&&(_||l.exitDuration)&&this._ngZone.runOutsideAngular(()=>{const qt=()=>this._finishRippleTransition($t),te=()=>this._destroyRipple($t);M.addEventListener("transitionend",qt),M.addEventListener("transitioncancel",te),Jt={onTransitionEnd:qt,onTransitionCancel:te}}),this._activeRipples.set($t,Jt),(Wt||!_)&&this._finishRippleTransition($t),$t}fadeOutRipple(b){if(b.state===xt.FADING_OUT||b.state===xt.HIDDEN)return;const a=b.element,c={...Nt,...b.config.animation};a.style.transitionDuration=`${c.exitDuration}ms`,a.style.opacity="0",b.state=xt.FADING_OUT,(b._animationForciblyDisabledThroughCss||!c.exitDuration)&&this._finishRippleTransition(b)}fadeOutAll(){this._getActiveRipples().forEach(b=>b.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(b=>{b.config.persistent||b.fadeOut()})}setupTriggerEvents(b){const a=(0,z.i8)(b);!this._platform.isBrowser||!a||a===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=a,w.forEach(c=>{n._eventManager.addHandler(this._ngZone,c,a,this)}))}handleEvent(b){"mousedown"===b.type?this._onMousedown(b):"touchstart"===b.type?this._onTouchStart(b):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{e.forEach(a=>{this._triggerElement.addEventListener(a,this,Bt)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(b){b.state===xt.FADING_IN?this._startFadeOutTransition(b):b.state===xt.FADING_OUT&&this._destroyRipple(b)}_startFadeOutTransition(b){const a=b===this._mostRecentTransientRipple,{persistent:c}=b.config;b.state=xt.VISIBLE,!c&&(!a||!this._isPointerDown)&&b.fadeOut()}_destroyRipple(b){const a=this._activeRipples.get(b)??null;this._activeRipples.delete(b),this._activeRipples.size||(this._containerRect=null),b===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),b.state=xt.HIDDEN,null!==a&&(b.element.removeEventListener("transitionend",a.onTransitionEnd),b.element.removeEventListener("transitioncancel",a.onTransitionCancel)),b.element.remove()}_onMousedown(b){const a=(0,h._G)(b),c=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+800;!this._target.rippleDisabled&&!a&&!c&&(this._isPointerDown=!0,this.fadeInRipple(b.clientX,b.clientY,this._target.rippleConfig))}_onTouchStart(b){if(!this._target.rippleDisabled&&!(0,h.w6)(b)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;const a=b.changedTouches;if(a)for(let c=0;c<a.length;c++)this.fadeInRipple(a[c].clientX,a[c].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(b=>{!b.config.persistent&&(b.state===xt.VISIBLE||b.config.terminateOnPointerUp&&b.state===xt.FADING_IN)&&b.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){const b=this._triggerElement;b&&(w.forEach(a=>n._eventManager.removeHandler(a,b,this)),this._pointerUpEventsRegistered&&(e.forEach(a=>b.removeEventListener(a,this,Bt)),this._pointerUpEventsRegistered=!1))}}const r=new t.nKC("mat-ripple-global-options");let u=(()=>{class x{get disabled(){return this._disabled}set disabled(a){a&&this.fadeOutAllNonPersistent(),this._disabled=a,this._setupTriggerEventsIfEnabled()}get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(a){this._trigger=a,this._setupTriggerEventsIfEnabled()}constructor(a,c,C,l,m){this._elementRef=a,this._animationMode=m,this.radius=0,this._disabled=!1,this._isInitialized=!1,this._globalOptions=l||{},this._rippleRenderer=new n(this,c,a,C)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:{...this._globalOptions.animation,..."NoopAnimations"===this._animationMode?{enterDuration:0,exitDuration:0}:{},...this.animation},terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(a,c=0,C){return"number"==typeof a?this._rippleRenderer.fadeInRipple(a,c,{...this.rippleConfig,...C}):this._rippleRenderer.fadeInRipple(0,0,{...this.rippleConfig,...a})}static{this.\u0275fac=function(c){return new(c||x)(t.rXU(t.aKT),t.rXU(t.SKi),t.rXU(Z.OD),t.rXU(r,8),t.rXU(t.bc$,8))}}static{this.\u0275dir=t.FsC({type:x,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(c,C){2&c&&t.AVh("mat-ripple-unbounded",C.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"],standalone:!0})}}return x})(),y=(()=>{class x{static{this.\u0275fac=function(c){return new(c||x)}}static{this.\u0275mod=t.$C({type:x})}static{this.\u0275inj=t.G2t({imports:[T,T]})}}return x})(),N=(()=>{class x{constructor(a){this._animationMode=a,this.state="unchecked",this.disabled=!1,this.appearance="full"}static{this.\u0275fac=function(c){return new(c||x)(t.rXU(t.bc$,8))}}static{this.\u0275cmp=t.VBU({type:x,selectors:[["mat-pseudo-checkbox"]],hostAttrs:[1,"mat-pseudo-checkbox"],hostVars:12,hostBindings:function(c,C){2&c&&t.AVh("mat-pseudo-checkbox-indeterminate","indeterminate"===C.state)("mat-pseudo-checkbox-checked","checked"===C.state)("mat-pseudo-checkbox-disabled",C.disabled)("mat-pseudo-checkbox-minimal","minimal"===C.appearance)("mat-pseudo-checkbox-full","full"===C.appearance)("_mat-animation-noopable","NoopAnimations"===C._animationMode)},inputs:{state:"state",disabled:"disabled",appearance:"appearance"},standalone:!0,features:[t.aNF],decls:0,vars:0,template:function(c,C){},styles:['.mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:"";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}'],encapsulation:2,changeDetection:0})}}return x})(),J=(()=>{class x{static{this.\u0275fac=function(c){return new(c||x)}}static{this.\u0275mod=t.$C({type:x})}static{this.\u0275inj=t.G2t({imports:[T]})}}return x})();const Y=new t.nKC("MAT_OPTION_PARENT_COMPONENT"),ht=new t.nKC("MatOptgroup");let yt=0;class Dt{constructor(b,a=!1){this.source=b,this.isUserInput=a}}let Ft=(()=>{class x{get multiple(){return this._parent&&this._parent.multiple}get selected(){return this._selected}get disabled(){return this.group&&this.group.disabled||this._disabled}set disabled(a){this._disabled=a}get disableRipple(){return!(!this._parent||!this._parent.disableRipple)}get hideSingleSelectionIndicator(){return!(!this._parent||!this._parent.hideSingleSelectionIndicator)}constructor(a,c,C,l){this._element=a,this._changeDetectorRef=c,this._parent=C,this.group=l,this._selected=!1,this._active=!1,this._disabled=!1,this._mostRecentViewValue="",this.id="mat-option-"+yt++,this.onSelectionChange=new t.bkB,this._stateChanges=new W.B}get active(){return this._active}get viewValue(){return(this._text?.nativeElement.textContent||"").trim()}select(a=!0){this._selected||(this._selected=!0,this._changeDetectorRef.markForCheck(),a&&this._emitSelectionChangeEvent())}deselect(a=!0){this._selected&&(this._selected=!1,this._changeDetectorRef.markForCheck(),a&&this._emitSelectionChangeEvent())}focus(a,c){const C=this._getHostElement();"function"==typeof C.focus&&C.focus(c)}setActiveStyles(){this._active||(this._active=!0,this._changeDetectorRef.markForCheck())}setInactiveStyles(){this._active&&(this._active=!1,this._changeDetectorRef.markForCheck())}getLabel(){return this.viewValue}_handleKeydown(a){(a.keyCode===A.Fm||a.keyCode===A.t6)&&!(0,A.rp)(a)&&(this._selectViaInteraction(),a.preventDefault())}_selectViaInteraction(){this.disabled||(this._selected=!this.multiple||!this._selected,this._changeDetectorRef.markForCheck(),this._emitSelectionChangeEvent(!0))}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._element.nativeElement}ngAfterViewChecked(){if(this._selected){const a=this.viewValue;a!==this._mostRecentViewValue&&(this._mostRecentViewValue&&this._stateChanges.next(),this._mostRecentViewValue=a)}}ngOnDestroy(){this._stateChanges.complete()}_emitSelectionChangeEvent(a=!1){this.onSelectionChange.emit(new Dt(this,a))}static{this.\u0275fac=function(c){return new(c||x)(t.rXU(t.aKT),t.rXU(t.gRc),t.rXU(Y,8),t.rXU(ht,8))}}static{this.\u0275cmp=t.VBU({type:x,selectors:[["mat-option"]],viewQuery:function(c,C){if(1&c&&t.GBs(tt,7),2&c){let l;t.mGM(l=t.lsd())&&(C._text=l.first)}},hostAttrs:["role","option",1,"mat-mdc-option","mdc-list-item"],hostVars:11,hostBindings:function(c,C){1&c&&t.bIt("click",function(){return C._selectViaInteraction()})("keydown",function(m){return C._handleKeydown(m)}),2&c&&(t.Mr5("id",C.id),t.BMQ("aria-selected",C.selected)("aria-disabled",C.disabled.toString()),t.AVh("mdc-list-item--selected",C.selected)("mat-mdc-option-multiple",C.multiple)("mat-mdc-option-active",C.active)("mdc-list-item--disabled",C.disabled))},inputs:{value:"value",id:"id",disabled:[2,"disabled","disabled",t.L39]},outputs:{onSelectionChange:"onSelectionChange"},exportAs:["matOption"],standalone:!0,features:[t.GFd,t.aNF],ngContentSelectors:et,decls:8,vars:5,consts:[["text",""],["aria-hidden","true",1,"mat-mdc-option-pseudo-checkbox",3,"disabled","state"],[1,"mdc-list-item__primary-text"],["state","checked","aria-hidden","true","appearance","minimal",1,"mat-mdc-option-pseudo-checkbox",3,"disabled"],[1,"cdk-visually-hidden"],["aria-hidden","true","mat-ripple","",1,"mat-mdc-option-ripple","mat-mdc-focus-indicator",3,"matRippleTrigger","matRippleDisabled"]],template:function(c,C){1&c&&(t.NAR(it),t.DNE(0,pt,1,2,"mat-pseudo-checkbox",1),t.SdG(1),t.j41(2,"span",2,0),t.SdG(4,1),t.k0s(),t.DNE(5,$,1,1,"mat-pseudo-checkbox",3)(6,S,2,1,"span",4),t.nrm(7,"div",5)),2&c&&(t.vxM(C.multiple?0:-1),t.R7$(5),t.vxM(C.multiple||!C.selected||C.hideSingleSelectionIndicator?-1:5),t.R7$(),t.vxM(C.group&&C.group._inert?6:-1),t.R7$(),t.Y8G("matRippleTrigger",C._getHostElement())("matRippleDisabled",C.disabled||C.disableRipple))},dependencies:[N,u],styles:['.mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:"";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:""}'],encapsulation:2,changeDetection:0})}}return x})();function Gt(x,b,a){if(a.length){let c=b.toArray(),C=a.toArray(),l=0;for(let m=0;m<x+1;m++)c[m].group&&c[m].group===C[l]&&l++;return l}return 0}function E(x,b,a,c){return x<a?x:x+b>a+c?Math.max(0,x-c+b):a}let mt=(()=>{class x{static{this.\u0275fac=function(c){return new(c||x)}}static{this.\u0275mod=t.$C({type:x})}static{this.\u0275inj=t.G2t({imports:[y,T,J]})}}return x})();const g={capture:!0},R=["focus","click","mouseenter","touchstart"],F="mat-ripple-loader-uninitialized",at="mat-ripple-loader-class-name",gt="mat-ripple-loader-centered",Ot="mat-ripple-loader-disabled";let Zt=(()=>{class x{constructor(){this._document=(0,t.WQX)(nt.qQ,{optional:!0}),this._animationMode=(0,t.WQX)(t.bc$,{optional:!0}),this._globalRippleOptions=(0,t.WQX)(r,{optional:!0}),this._platform=(0,t.WQX)(Z.OD),this._ngZone=(0,t.WQX)(t.SKi),this._hosts=new Map,this._onInteraction=a=>{if(!(a.target instanceof HTMLElement))return;const C=a.target.closest(`[${F}]`);C&&this._createRipple(C)},this._ngZone.runOutsideAngular(()=>{for(const a of R)this._document?.addEventListener(a,this._onInteraction,g)})}ngOnDestroy(){const a=this._hosts.keys();for(const c of a)this.destroyRipple(c);for(const c of R)this._document?.removeEventListener(c,this._onInteraction,g)}configureRipple(a,c){a.setAttribute(F,""),(c.className||!a.hasAttribute(at))&&a.setAttribute(at,c.className||""),c.centered&&a.setAttribute(gt,""),c.disabled&&a.setAttribute(Ot,"")}getRipple(a){return this._hosts.get(a)||this._createRipple(a)}setDisabled(a,c){const C=this._hosts.get(a);C?C.disabled=c:c?a.setAttribute(Ot,""):a.removeAttribute(Ot)}_createRipple(a){if(!this._document)return;const c=this._hosts.get(a);if(c)return c;a.querySelector(".mat-ripple")?.remove();const C=this._document.createElement("span");C.classList.add("mat-ripple",a.getAttribute(at)),a.append(C);const l=new u(new t.aKT(C),this._ngZone,this._platform,this._globalRippleOptions?this._globalRippleOptions:void 0,this._animationMode?this._animationMode:void 0);return l._isInitialized=!0,l.trigger=a,l.centered=a.hasAttribute(gt),l.disabled=a.hasAttribute(Ot),this.attachRipple(a,l),l}attachRipple(a,c){a.removeAttribute(F),this._hosts.set(a,c)}destroyRipple(a){const c=this._hosts.get(a);c&&(c.ngOnDestroy(),this._hosts.delete(a))}static{this.\u0275fac=function(c){return new(c||x)}}static{this.\u0275prov=t.jDH({token:x,factory:x.\u0275fac,providedIn:"root"})}}return x})(),Kt=(()=>{class x{static{this.\u0275fac=function(c){return new(c||x)}}static{this.\u0275cmp=t.VBU({type:x,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(c,C){2&c&&t.AVh("mdc-form-field--align-end","before"===C.labelPosition)},inputs:{labelPosition:"labelPosition"},standalone:!0,features:[t.aNF],attrs:L,ngContentSelectors:X,decls:1,vars:0,template:function(c,C){1&c&&(t.NAR(),t.SdG(0))},styles:[".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-form-field{font-family:var(--mdc-form-field-label-text-font);line-height:var(--mdc-form-field-label-text-line-height);font-size:var(--mdc-form-field-label-text-size);font-weight:var(--mdc-form-field-label-text-weight);letter-spacing:var(--mdc-form-field-label-text-tracking);color:var(--mdc-form-field-label-text-color)}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}"],encapsulation:2,changeDetection:0})}}return x})()},1997:(St,ft,f)=>{f.d(ft,{q:()=>B,w:()=>nt});var t=f(3953),h=f(4085),s=f(6600);let B=(()=>{class Z{constructor(){this._vertical=!1,this._inset=!1}get vertical(){return this._vertical}set vertical(W){this._vertical=(0,h.he)(W)}get inset(){return this._inset}set inset(W){this._inset=(0,h.he)(W)}static{this.\u0275fac=function(A){return new(A||Z)}}static{this.\u0275cmp=t.VBU({type:Z,selectors:[["mat-divider"]],hostAttrs:["role","separator",1,"mat-divider"],hostVars:7,hostBindings:function(A,P){2&A&&(t.BMQ("aria-orientation",P.vertical?"vertical":"horizontal"),t.AVh("mat-divider-vertical",P.vertical)("mat-divider-horizontal",!P.vertical)("mat-divider-inset",P.inset))},inputs:{vertical:"vertical",inset:"inset"},standalone:!0,features:[t.aNF],decls:0,vars:0,template:function(A,P){},styles:[".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color);border-top-width:var(--mat-divider-width)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color);border-right-width:var(--mat-divider-width)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}"],encapsulation:2,changeDetection:0})}}return Z})(),nt=(()=>{class Z{static{this.\u0275fac=function(A){return new(A||Z)}}static{this.\u0275mod=t.$C({type:Z})}static{this.\u0275inj=t.G2t({imports:[s.yE,s.yE]})}}return Z})()},6467:(St,ft,f)=>{f.d(ft,{xb:()=>J,rl:()=>Ft,qT:()=>r,RG:()=>Gt,nJ:()=>ot,yw:()=>zt});var t=f(3953),h=f(8203),s=f(6860),B=f(8359),nt=f(1413),Z=f(7786),z=f(6977),W=f(1985),A=f(5964),P=f(2771),ct=f(7647);class et{constructor(mt){this._box=mt,this._destroyed=new nt.B,this._resizeSubject=new nt.B,this._elementObservables=new Map,typeof ResizeObserver<"u"&&(this._resizeObserver=new ResizeObserver(g=>this._resizeSubject.next(g)))}observe(mt){return this._elementObservables.has(mt)||this._elementObservables.set(mt,new W.c(g=>{const R=this._resizeSubject.subscribe(g);return this._resizeObserver?.observe(mt,{box:this._box}),()=>{this._resizeObserver?.unobserve(mt),R.unsubscribe(),this._elementObservables.delete(mt)}}).pipe((0,A.p)(g=>g.some(R=>R.target===mt)),function tt(E,mt,g){let R,F=!1;return E&&"object"==typeof E?({bufferSize:R=1/0,windowTime:mt=1/0,refCount:F=!1,scheduler:g}=E):R=E??1/0,(0,ct.u)({connector:()=>new P.m(R,mt,g),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:F})}({bufferSize:1,refCount:!0}),(0,z.Q)(this._destroyed))),this._elementObservables.get(mt)}destroy(){this._destroyed.next(),this._destroyed.complete(),this._resizeSubject.complete(),this._elementObservables.clear()}}let pt=(()=>{class E{constructor(){this._observers=new Map,this._ngZone=(0,t.WQX)(t.SKi)}ngOnDestroy(){for(const[,g]of this._observers)g.destroy();this._observers.clear()}observe(g,R){const F=R?.box||"content-box";return this._observers.has(F)||this._observers.set(F,new et(F)),this._observers.get(F).observe(g)}static{this.\u0275fac=function(R){return new(R||E)}}static{this.\u0275prov=t.jDH({token:E,factory:E.\u0275fac,providedIn:"root"})}}return E})();var $=f(4085),S=f(9969),L=f(177),X=f(2318),dt=f(6600);const lt=["notch"],q=["matFormFieldNotchedOutline",""],k=["*"],O=["textField"],T=["iconPrefixContainer"],v=["textPrefixContainer"],rt=["*",[["mat-label"]],[["","matPrefix",""],["","matIconPrefix",""]],[["","matTextPrefix",""]],[["","matTextSuffix",""]],[["","matSuffix",""],["","matIconSuffix",""]],[["mat-error"],["","matError",""]],[["mat-hint",3,"align","end"]],[["mat-hint","align","end"]]],st=["*","mat-label","[matPrefix], [matIconPrefix]","[matTextPrefix]","[matTextSuffix]","[matSuffix], [matIconSuffix]","mat-error, [matError]","mat-hint:not([align='end'])","mat-hint[align='end']"];function V(E,mt){1&E&&t.nrm(0,"span",19)}function bt(E,mt){if(1&E&&(t.j41(0,"label",18),t.SdG(1,1),t.DNE(2,V,1,0,"span",19),t.k0s()),2&E){const g=t.XpG(2);t.Y8G("floating",g._shouldLabelFloat())("monitorResize",g._hasOutline())("id",g._labelId),t.BMQ("for",g._control.disableAutomaticLabeling?null:g._control.id),t.R7$(2),t.vxM(!g.hideRequiredMarker&&g._control.required?2:-1)}}function At(E,mt){if(1&E&&t.DNE(0,bt,3,5,"label",18),2&E){const g=t.XpG();t.vxM(g._hasFloatingLabel()?0:-1)}}function jt(E,mt){1&E&&t.nrm(0,"div",5)}function Rt(E,mt){}function ut(E,mt){if(1&E&&t.DNE(0,Rt,0,0,"ng-template",11),2&E){t.XpG(2);const g=t.sdS(1);t.Y8G("ngTemplateOutlet",g)}}function Mt(E,mt){if(1&E&&(t.j41(0,"div",7),t.DNE(1,ut,1,1,null,11),t.k0s()),2&E){const g=t.XpG();t.Y8G("matFormFieldNotchedOutlineOpen",g._shouldLabelFloat()),t.R7$(),t.vxM(g._forceDisplayInfixLabel()?-1:1)}}function j(E,mt){1&E&&(t.j41(0,"div",8,2),t.SdG(2,2),t.k0s())}function U(E,mt){1&E&&(t.j41(0,"div",9,3),t.SdG(2,3),t.k0s())}function K(E,mt){}function vt(E,mt){if(1&E&&t.DNE(0,K,0,0,"ng-template",11),2&E){t.XpG();const g=t.sdS(1);t.Y8G("ngTemplateOutlet",g)}}function kt(E,mt){1&E&&(t.j41(0,"div",12),t.SdG(1,4),t.k0s())}function Ct(E,mt){1&E&&(t.j41(0,"div",13),t.SdG(1,5),t.k0s())}function It(E,mt){1&E&&t.nrm(0,"div",14)}function Pt(E,mt){if(1&E&&(t.j41(0,"div",16),t.SdG(1,6),t.k0s()),2&E){const g=t.XpG();t.Y8G("@transitionMessages",g._subscriptAnimationState)}}function Ut(E,mt){if(1&E&&(t.j41(0,"mat-hint",20),t.EFF(1),t.k0s()),2&E){const g=t.XpG(2);t.Y8G("id",g._hintLabelId),t.R7$(),t.JRh(g.hintLabel)}}function Yt(E,mt){if(1&E&&(t.j41(0,"div",17),t.DNE(1,Ut,2,2,"mat-hint",20),t.SdG(2,7),t.nrm(3,"div",21),t.SdG(4,8),t.k0s()),2&E){const g=t.XpG();t.Y8G("@transitionMessages",g._subscriptAnimationState),t.R7$(),t.vxM(g.hintLabel?1:-1)}}let ot=(()=>{class E{static{this.\u0275fac=function(R){return new(R||E)}}static{this.\u0275dir=t.FsC({type:E,selectors:[["mat-label"]],standalone:!0})}}return E})();const p=new t.nKC("MatError");let I=0,Q=(()=>{class E{constructor(){this.align="start",this.id="mat-mdc-hint-"+I++}static{this.\u0275fac=function(R){return new(R||E)}}static{this.\u0275dir=t.FsC({type:E,selectors:[["mat-hint"]],hostAttrs:[1,"mat-mdc-form-field-hint","mat-mdc-form-field-bottom-align"],hostVars:4,hostBindings:function(R,F){2&R&&(t.Mr5("id",F.id),t.BMQ("align",null),t.AVh("mat-mdc-form-field-hint-end","end"===F.align))},inputs:{align:"align",id:"id"},standalone:!0})}}return E})();const _t=new t.nKC("MatPrefix"),Et=new t.nKC("MatSuffix");let zt=(()=>{class E{constructor(){this._isText=!1}set _isTextSelector(g){this._isText=!0}static{this.\u0275fac=function(R){return new(R||E)}}static{this.\u0275dir=t.FsC({type:E,selectors:[["","matSuffix",""],["","matIconSuffix",""],["","matTextSuffix",""]],inputs:{_isTextSelector:[0,"matTextSuffix","_isTextSelector"]},standalone:!0,features:[t.Jv_([{provide:Et,useExisting:E}])]})}}return E})();const Vt=new t.nKC("FloatingLabelParent");let Nt=(()=>{class E{get floating(){return this._floating}set floating(g){this._floating=g,this.monitorResize&&this._handleResize()}get monitorResize(){return this._monitorResize}set monitorResize(g){this._monitorResize=g,this._monitorResize?this._subscribeToResize():this._resizeSubscription.unsubscribe()}constructor(g){this._elementRef=g,this._floating=!1,this._monitorResize=!1,this._resizeObserver=(0,t.WQX)(pt),this._ngZone=(0,t.WQX)(t.SKi),this._parent=(0,t.WQX)(Vt),this._resizeSubscription=new B.yU}ngOnDestroy(){this._resizeSubscription.unsubscribe()}getWidth(){return function Xt(E){if(null!==E.offsetParent)return E.scrollWidth;const g=E.cloneNode(!0);g.style.setProperty("position","absolute"),g.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(g);const R=g.scrollWidth;return g.remove(),R}(this._elementRef.nativeElement)}get element(){return this._elementRef.nativeElement}_handleResize(){setTimeout(()=>this._parent._handleLabelResized())}_subscribeToResize(){this._resizeSubscription.unsubscribe(),this._ngZone.runOutsideAngular(()=>{this._resizeSubscription=this._resizeObserver.observe(this._elementRef.nativeElement,{box:"border-box"}).subscribe(()=>this._handleResize())})}static{this.\u0275fac=function(R){return new(R||E)(t.rXU(t.aKT))}}static{this.\u0275dir=t.FsC({type:E,selectors:[["label","matFormFieldFloatingLabel",""]],hostAttrs:[1,"mdc-floating-label","mat-mdc-floating-label"],hostVars:2,hostBindings:function(R,F){2&R&&t.AVh("mdc-floating-label--float-above",F.floating)},inputs:{floating:"floating",monitorResize:"monitorResize"},standalone:!0})}}return E})();const Bt="mdc-line-ripple--active",w="mdc-line-ripple--deactivating";let e=(()=>{class E{constructor(g,R){this._elementRef=g,this._handleTransitionEnd=F=>{const at=this._elementRef.nativeElement.classList,gt=at.contains(w);"opacity"===F.propertyName&&gt&&at.remove(Bt,w)},R.runOutsideAngular(()=>{g.nativeElement.addEventListener("transitionend",this._handleTransitionEnd)})}activate(){const g=this._elementRef.nativeElement.classList;g.remove(w),g.add(Bt)}deactivate(){this._elementRef.nativeElement.classList.add(w)}ngOnDestroy(){this._elementRef.nativeElement.removeEventListener("transitionend",this._handleTransitionEnd)}static{this.\u0275fac=function(R){return new(R||E)(t.rXU(t.aKT),t.rXU(t.SKi))}}static{this.\u0275dir=t.FsC({type:E,selectors:[["div","matFormFieldLineRipple",""]],hostAttrs:[1,"mdc-line-ripple"],standalone:!0})}}return E})(),n=(()=>{class E{constructor(g,R){this._elementRef=g,this._ngZone=R,this.open=!1}ngAfterViewInit(){const g=this._elementRef.nativeElement.querySelector(".mdc-floating-label");g?(this._elementRef.nativeElement.classList.add("mdc-notched-outline--upgraded"),"function"==typeof requestAnimationFrame&&(g.style.transitionDuration="0s",this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>g.style.transitionDuration="")}))):this._elementRef.nativeElement.classList.add("mdc-notched-outline--no-label")}_setNotchWidth(g){this._notch.nativeElement.style.width=this.open&&g?`calc(${g}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + 9px)`:""}static{this.\u0275fac=function(R){return new(R||E)(t.rXU(t.aKT),t.rXU(t.SKi))}}static{this.\u0275cmp=t.VBU({type:E,selectors:[["div","matFormFieldNotchedOutline",""]],viewQuery:function(R,F){if(1&R&&t.GBs(lt,5),2&R){let at;t.mGM(at=t.lsd())&&(F._notch=at.first)}},hostAttrs:[1,"mdc-notched-outline"],hostVars:2,hostBindings:function(R,F){2&R&&t.AVh("mdc-notched-outline--notched",F.open)},inputs:{open:[0,"matFormFieldNotchedOutlineOpen","open"]},standalone:!0,features:[t.aNF],attrs:q,ngContentSelectors:k,decls:5,vars:0,consts:[["notch",""],[1,"mdc-notched-outline__leading"],[1,"mdc-notched-outline__notch"],[1,"mdc-notched-outline__trailing"]],template:function(R,F){1&R&&(t.NAR(),t.nrm(0,"div",1),t.j41(1,"div",2,0),t.SdG(3),t.k0s(),t.nrm(4,"div",3))},encapsulation:2,changeDetection:0})}}return E})();const i={transitionMessages:(0,S.hZ)("transitionMessages",[(0,S.wk)("enter",(0,S.iF)({opacity:1,transform:"translateY(0%)"})),(0,S.kY)("void => enter",[(0,S.iF)({opacity:0,transform:"translateY(-5px)"}),(0,S.i0)("300ms cubic-bezier(0.55, 0, 0.55, 0.2)")])])};let r=(()=>{class E{static{this.\u0275fac=function(R){return new(R||E)}}static{this.\u0275dir=t.FsC({type:E})}}return E})();const J=new t.nKC("MatFormField"),Y=new t.nKC("MAT_FORM_FIELD_DEFAULT_OPTIONS");let H=0,Ft=(()=>{class E{get hideRequiredMarker(){return this._hideRequiredMarker}set hideRequiredMarker(g){this._hideRequiredMarker=(0,$.he)(g)}get floatLabel(){return this._floatLabel||this._defaults?.floatLabel||"auto"}set floatLabel(g){g!==this._floatLabel&&(this._floatLabel=g,this._changeDetectorRef.markForCheck())}get appearance(){return this._appearance}set appearance(g){const R=this._appearance;this._appearance=g||this._defaults?.appearance||"fill","outline"===this._appearance&&this._appearance!==R&&(this._needsOutlineLabelOffsetUpdateOnStable=!0)}get subscriptSizing(){return this._subscriptSizing||this._defaults?.subscriptSizing||"fixed"}set subscriptSizing(g){this._subscriptSizing=g||this._defaults?.subscriptSizing||"fixed"}get hintLabel(){return this._hintLabel}set hintLabel(g){this._hintLabel=g,this._processHints()}get _control(){return this._explicitFormFieldControl||this._formFieldControl}set _control(g){this._explicitFormFieldControl=g}constructor(g,R,F,at,gt,Ot,Zt,Kt){this._elementRef=g,this._changeDetectorRef=R,this._ngZone=F,this._dir=at,this._platform=gt,this._defaults=Ot,this._animationMode=Zt,this._hideRequiredMarker=!1,this.color="primary",this._appearance="fill",this._subscriptSizing=null,this._hintLabel="",this._hasIconPrefix=!1,this._hasTextPrefix=!1,this._hasIconSuffix=!1,this._hasTextSuffix=!1,this._labelId="mat-mdc-form-field-label-"+H++,this._hintLabelId="mat-mdc-hint-"+H++,this._subscriptAnimationState="",this._destroyed=new nt.B,this._isFocused=null,this._needsOutlineLabelOffsetUpdateOnStable=!1,Ot&&(Ot.appearance&&(this.appearance=Ot.appearance),this._hideRequiredMarker=!!Ot?.hideRequiredMarker,Ot.color&&(this.color=Ot.color))}ngAfterViewInit(){this._updateFocusState(),this._subscriptAnimationState="enter",this._changeDetectorRef.detectChanges()}ngAfterContentInit(){this._assertFormFieldControl(),this._initializeControl(),this._initializeSubscript(),this._initializePrefixAndSuffix(),this._initializeOutlineLabelOffsetSubscriptions()}ngAfterContentChecked(){this._assertFormFieldControl()}ngOnDestroy(){this._destroyed.next(),this._destroyed.complete()}getLabelId(){return this._hasFloatingLabel()?this._labelId:null}getConnectedOverlayOrigin(){return this._textField||this._elementRef}_animateAndLockLabel(){this._hasFloatingLabel()&&(this.floatLabel="always")}_initializeControl(){const g=this._control;g.controlType&&this._elementRef.nativeElement.classList.add(`mat-mdc-form-field-type-${g.controlType}`),g.stateChanges.subscribe(()=>{this._updateFocusState(),this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),g.ngControl&&g.ngControl.valueChanges&&g.ngControl.valueChanges.pipe((0,z.Q)(this._destroyed)).subscribe(()=>this._changeDetectorRef.markForCheck())}_checkPrefixAndSuffixTypes(){this._hasIconPrefix=!!this._prefixChildren.find(g=>!g._isText),this._hasTextPrefix=!!this._prefixChildren.find(g=>g._isText),this._hasIconSuffix=!!this._suffixChildren.find(g=>!g._isText),this._hasTextSuffix=!!this._suffixChildren.find(g=>g._isText)}_initializePrefixAndSuffix(){this._checkPrefixAndSuffixTypes(),(0,Z.h)(this._prefixChildren.changes,this._suffixChildren.changes).subscribe(()=>{this._checkPrefixAndSuffixTypes(),this._changeDetectorRef.markForCheck()})}_initializeSubscript(){this._hintChildren.changes.subscribe(()=>{this._processHints(),this._changeDetectorRef.markForCheck()}),this._errorChildren.changes.subscribe(()=>{this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),this._validateHints(),this._syncDescribedByIds()}_assertFormFieldControl(){}_updateFocusState(){this._control.focused&&!this._isFocused?(this._isFocused=!0,this._lineRipple?.activate()):!this._control.focused&&(this._isFocused||null===this._isFocused)&&(this._isFocused=!1,this._lineRipple?.deactivate()),this._textField?.nativeElement.classList.toggle("mdc-text-field--focused",this._control.focused)}_initializeOutlineLabelOffsetSubscriptions(){this._prefixChildren.changes.subscribe(()=>this._needsOutlineLabelOffsetUpdateOnStable=!0),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.pipe((0,z.Q)(this._destroyed)).subscribe(()=>{this._needsOutlineLabelOffsetUpdateOnStable&&(this._needsOutlineLabelOffsetUpdateOnStable=!1,this._updateOutlineLabelOffset())})}),this._dir.change.pipe((0,z.Q)(this._destroyed)).subscribe(()=>this._needsOutlineLabelOffsetUpdateOnStable=!0)}_shouldAlwaysFloat(){return"always"===this.floatLabel}_hasOutline(){return"outline"===this.appearance}_forceDisplayInfixLabel(){return!this._platform.isBrowser&&this._prefixChildren.length&&!this._shouldLabelFloat()}_hasFloatingLabel(){return!!this._labelChildNonStatic||!!this._labelChildStatic}_shouldLabelFloat(){return this._control.shouldLabelFloat||this._shouldAlwaysFloat()}_shouldForward(g){const R=this._control?this._control.ngControl:null;return R&&R[g]}_getDisplayedMessages(){return this._errorChildren&&this._errorChildren.length>0&&this._control.errorState?"error":"hint"}_handleLabelResized(){this._refreshOutlineNotchWidth()}_refreshOutlineNotchWidth(){this._hasOutline()&&this._floatingLabel&&this._shouldLabelFloat()?this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth()):this._notchedOutline?._setNotchWidth(0)}_processHints(){this._validateHints(),this._syncDescribedByIds()}_validateHints(){}_syncDescribedByIds(){if(this._control){let g=[];if(this._control.userAriaDescribedBy&&"string"==typeof this._control.userAriaDescribedBy&&g.push(...this._control.userAriaDescribedBy.split(" ")),"hint"===this._getDisplayedMessages()){const R=this._hintChildren?this._hintChildren.find(at=>"start"===at.align):null,F=this._hintChildren?this._hintChildren.find(at=>"end"===at.align):null;R?g.push(R.id):this._hintLabel&&g.push(this._hintLabelId),F&&g.push(F.id)}else this._errorChildren&&g.push(...this._errorChildren.map(R=>R.id));this._control.setDescribedByIds(g)}}_updateOutlineLabelOffset(){if(!this._platform.isBrowser||!this._hasOutline()||!this._floatingLabel)return;const g=this._floatingLabel.element;if(!this._iconPrefixContainer&&!this._textPrefixContainer)return void(g.style.transform="");if(!this._isAttachedToDom())return void(this._needsOutlineLabelOffsetUpdateOnStable=!0);const R=this._iconPrefixContainer?.nativeElement,F=this._textPrefixContainer?.nativeElement,at=R?.getBoundingClientRect().width??0,gt=F?.getBoundingClientRect().width??0;g.style.transform=`var(\n        --mat-mdc-form-field-label-transform,\n        translateY(-50%) translateX(calc(${"rtl"===this._dir.value?"-1":"1"} * (${at+gt}px + var(--mat-mdc-form-field-label-offset-x, 0px))))\n    )`}_isAttachedToDom(){const g=this._elementRef.nativeElement;if(g.getRootNode){const R=g.getRootNode();return R&&R!==g}return document.documentElement.contains(g)}static{this.\u0275fac=function(R){return new(R||E)(t.rXU(t.aKT),t.rXU(t.gRc),t.rXU(t.SKi),t.rXU(h.dS),t.rXU(s.OD),t.rXU(Y,8),t.rXU(t.bc$,8),t.rXU(L.qQ))}}static{this.\u0275cmp=t.VBU({type:E,selectors:[["mat-form-field"]],contentQueries:function(R,F,at){if(1&R&&(t.wni(at,ot,5),t.wni(at,ot,7),t.wni(at,r,5),t.wni(at,_t,5),t.wni(at,Et,5),t.wni(at,p,5),t.wni(at,Q,5)),2&R){let gt;t.mGM(gt=t.lsd())&&(F._labelChildNonStatic=gt.first),t.mGM(gt=t.lsd())&&(F._labelChildStatic=gt.first),t.mGM(gt=t.lsd())&&(F._formFieldControl=gt.first),t.mGM(gt=t.lsd())&&(F._prefixChildren=gt),t.mGM(gt=t.lsd())&&(F._suffixChildren=gt),t.mGM(gt=t.lsd())&&(F._errorChildren=gt),t.mGM(gt=t.lsd())&&(F._hintChildren=gt)}},viewQuery:function(R,F){if(1&R&&(t.GBs(O,5),t.GBs(T,5),t.GBs(v,5),t.GBs(Nt,5),t.GBs(n,5),t.GBs(e,5)),2&R){let at;t.mGM(at=t.lsd())&&(F._textField=at.first),t.mGM(at=t.lsd())&&(F._iconPrefixContainer=at.first),t.mGM(at=t.lsd())&&(F._textPrefixContainer=at.first),t.mGM(at=t.lsd())&&(F._floatingLabel=at.first),t.mGM(at=t.lsd())&&(F._notchedOutline=at.first),t.mGM(at=t.lsd())&&(F._lineRipple=at.first)}},hostAttrs:[1,"mat-mdc-form-field"],hostVars:42,hostBindings:function(R,F){2&R&&t.AVh("mat-mdc-form-field-label-always-float",F._shouldAlwaysFloat())("mat-mdc-form-field-has-icon-prefix",F._hasIconPrefix)("mat-mdc-form-field-has-icon-suffix",F._hasIconSuffix)("mat-form-field-invalid",F._control.errorState)("mat-form-field-disabled",F._control.disabled)("mat-form-field-autofilled",F._control.autofilled)("mat-form-field-no-animations","NoopAnimations"===F._animationMode)("mat-form-field-appearance-fill","fill"==F.appearance)("mat-form-field-appearance-outline","outline"==F.appearance)("mat-form-field-hide-placeholder",F._hasFloatingLabel()&&!F._shouldLabelFloat())("mat-focused",F._control.focused)("mat-primary","accent"!==F.color&&"warn"!==F.color)("mat-accent","accent"===F.color)("mat-warn","warn"===F.color)("ng-untouched",F._shouldForward("untouched"))("ng-touched",F._shouldForward("touched"))("ng-pristine",F._shouldForward("pristine"))("ng-dirty",F._shouldForward("dirty"))("ng-valid",F._shouldForward("valid"))("ng-invalid",F._shouldForward("invalid"))("ng-pending",F._shouldForward("pending"))},inputs:{hideRequiredMarker:"hideRequiredMarker",color:"color",floatLabel:"floatLabel",appearance:"appearance",subscriptSizing:"subscriptSizing",hintLabel:"hintLabel"},exportAs:["matFormField"],standalone:!0,features:[t.Jv_([{provide:J,useExisting:E},{provide:Vt,useExisting:E}]),t.aNF],ngContentSelectors:st,decls:18,vars:21,consts:[["labelTemplate",""],["textField",""],["iconPrefixContainer",""],["textPrefixContainer",""],[1,"mat-mdc-text-field-wrapper","mdc-text-field",3,"click"],[1,"mat-mdc-form-field-focus-overlay"],[1,"mat-mdc-form-field-flex"],["matFormFieldNotchedOutline","",3,"matFormFieldNotchedOutlineOpen"],[1,"mat-mdc-form-field-icon-prefix"],[1,"mat-mdc-form-field-text-prefix"],[1,"mat-mdc-form-field-infix"],[3,"ngTemplateOutlet"],[1,"mat-mdc-form-field-text-suffix"],[1,"mat-mdc-form-field-icon-suffix"],["matFormFieldLineRipple",""],[1,"mat-mdc-form-field-subscript-wrapper","mat-mdc-form-field-bottom-align"],[1,"mat-mdc-form-field-error-wrapper"],[1,"mat-mdc-form-field-hint-wrapper"],["matFormFieldFloatingLabel","",3,"floating","monitorResize","id"],["aria-hidden","true",1,"mat-mdc-form-field-required-marker","mdc-floating-label--required"],[3,"id"],[1,"mat-mdc-form-field-hint-spacer"]],template:function(R,F){if(1&R){const at=t.RV6();t.NAR(rt),t.DNE(0,At,1,1,"ng-template",null,0,t.C5r),t.j41(2,"div",4,1),t.bIt("click",function(Ot){return t.eBV(at),t.Njj(F._control.onContainerClick(Ot))}),t.DNE(4,jt,1,0,"div",5),t.j41(5,"div",6),t.DNE(6,Mt,2,2,"div",7)(7,j,3,0,"div",8)(8,U,3,0,"div",9),t.j41(9,"div",10),t.DNE(10,vt,1,1,null,11),t.SdG(11),t.k0s(),t.DNE(12,kt,2,0,"div",12)(13,Ct,2,0,"div",13),t.k0s(),t.DNE(14,It,1,0,"div",14),t.k0s(),t.j41(15,"div",15),t.DNE(16,Pt,2,1,"div",16)(17,Yt,5,2,"div",17),t.k0s()}if(2&R){let at;t.R7$(2),t.AVh("mdc-text-field--filled",!F._hasOutline())("mdc-text-field--outlined",F._hasOutline())("mdc-text-field--no-label",!F._hasFloatingLabel())("mdc-text-field--disabled",F._control.disabled)("mdc-text-field--invalid",F._control.errorState),t.R7$(2),t.vxM(F._hasOutline()||F._control.disabled?-1:4),t.R7$(2),t.vxM(F._hasOutline()?6:-1),t.R7$(),t.vxM(F._hasIconPrefix?7:-1),t.R7$(),t.vxM(F._hasTextPrefix?8:-1),t.R7$(2),t.vxM(!F._hasOutline()||F._forceDisplayInfixLabel()?10:-1),t.R7$(2),t.vxM(F._hasTextSuffix?12:-1),t.R7$(),t.vxM(F._hasIconSuffix?13:-1),t.R7$(),t.vxM(F._hasOutline()?-1:14),t.R7$(),t.AVh("mat-mdc-form-field-subscript-dynamic-size","dynamic"===F.subscriptSizing),t.R7$(),t.vxM("error"===(at=F._getDisplayedMessages())?16:"hint"===at?17:-1)}},dependencies:[Nt,n,L.T3,e,Q],styles:['.mdc-text-field{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0;display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-text-field__input{height:28px;width:100%;min-width:0;border:none;border-radius:0;background:none;appearance:none;padding:0}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}@media all{.mdc-text-field__input::placeholder{opacity:0}}@media all{.mdc-text-field__input:-ms-input-placeholder{opacity:0}}@media all{.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}}@media all{.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}}.mdc-text-field__affix{height:28px;opacity:0;white-space:nowrap}.mdc-text-field--label-floating .mdc-text-field__affix,.mdc-text-field--no-label .mdc-text-field__affix{opacity:1}@supports(-webkit-hyphens: none){.mdc-text-field--outlined .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field__affix--prefix,.mdc-text-field__affix--prefix[dir=rtl]{padding-left:2px;padding-right:0}.mdc-text-field--end-aligned .mdc-text-field__affix--prefix{padding-left:0;padding-right:12px}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--end-aligned .mdc-text-field__affix--prefix[dir=rtl]{padding-left:12px;padding-right:0}.mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field__affix--suffix,.mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:12px}.mdc-text-field--end-aligned .mdc-text-field__affix--suffix{padding-left:2px;padding-right:0}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--end-aligned .mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:2px}.mdc-text-field--filled{height:56px}.mdc-text-field--filled::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}.mdc-text-field--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-text-field--filled .mdc-floating-label,.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled.mdc-text-field--no-label::before{display:none}@supports(-webkit-hyphens: none){.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field--outlined{height:56px;overflow:visible}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--outlined .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-shape-small, 4px))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-shape-small, 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:initial}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mdc-text-field--textarea{flex-direction:column;align-items:center;width:auto;height:auto;padding:0}.mdc-text-field--textarea .mdc-floating-label{top:19px}.mdc-text-field--textarea .mdc-floating-label:not(.mdc-floating-label--float-above){transform:none}.mdc-text-field--textarea .mdc-text-field__input{flex-grow:1;height:auto;min-height:1.5rem;overflow-x:hidden;overflow-y:auto;box-sizing:border-box;resize:none;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--filled::before{display:none}.mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-10.25px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--filled .mdc-text-field__input{margin-top:23px;margin-bottom:9px}.mdc-text-field--textarea.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label{top:18px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field__input{margin-bottom:2px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter{align-self:flex-end;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::after{display:inline-block;width:0;height:16px;content:"";vertical-align:-16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::before{display:none}.mdc-text-field__resizer{align-self:stretch;display:inline-flex;flex-direction:column;flex-grow:1;max-height:100%;max-width:100%;min-height:56px;min-width:fit-content;min-width:-moz-available;min-width:-webkit-fill-available;overflow:hidden;resize:both}.mdc-text-field--filled .mdc-text-field__resizer{transform:translateY(-1px)}.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateY(1px)}.mdc-text-field--outlined .mdc-text-field__resizer{transform:translateX(-1px) translateY(-1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer,.mdc-text-field--outlined .mdc-text-field__resizer[dir=rtl]{transform:translateX(1px) translateY(-1px)}.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateX(1px) translateY(1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input[dir=rtl],.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter[dir=rtl]{transform:translateX(-1px) translateY(1px)}.mdc-text-field--with-leading-icon{padding-left:0;padding-right:16px}[dir=rtl] .mdc-text-field--with-leading-icon,.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:16px;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 48px);left:48px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:48px}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:36px}.mdc-text-field--with-leading-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--with-trailing-icon{padding-left:16px;padding-right:0}[dir=rtl] .mdc-text-field--with-trailing-icon,.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0;padding-right:16px}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-trailing-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 96px/0.75)}.mdc-text-field-helper-line{display:flex;justify-content:space-between;box-sizing:border-box}.mdc-text-field+.mdc-text-field-helper-line{padding-right:16px;padding-left:16px}.mdc-form-field>.mdc-text-field+label{align-self:flex-start}.mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--focused .mdc-notched-outline__trailing{border-width:2px}.mdc-text-field--focused+.mdc-text-field-helper-line .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg){opacity:1}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-text-field--focused.mdc-text-field--outlined.mdc-text-field--textarea .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--invalid+.mdc-text-field-helper-line .mdc-text-field-helper-text--validation-msg{opacity:1}.mdc-text-field--disabled{pointer-events:none}@media screen and (forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--disabled .mdc-floating-label{cursor:default}.mdc-text-field--disabled.mdc-text-field--filled .mdc-text-field__ripple{display:none}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--end-aligned .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--end-aligned .mdc-text-field__input[dir=rtl]{text-align:left}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix{direction:ltr}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--leading,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--leading{order:1}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{order:2}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input{order:3}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{order:4}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--trailing,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--trailing{order:5}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--prefix{padding-right:12px}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--suffix{padding-left:2px}.mdc-floating-label{position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0px;content:"*"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after,.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;pointer-events:none}.mdc-notched-outline__trailing{flex-grow:1}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:""}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{border-top:1px solid;border-bottom:1px solid}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none}.mdc-notched-outline__notch{max-width:calc(100% - 12px*2)}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::after{border-bottom-width:2px}.mdc-text-field--filled{border-top-left-radius:var(--mdc-filled-text-field-container-shape);border-top-right-radius:var(--mdc-filled-text-field-container-shape);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-caret-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-focus-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-hover-label-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-disabled-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-focus-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-hover-label-text-color)}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font);font-size:var(--mdc-filled-text-field-label-text-size);font-weight:var(--mdc-filled-text-field-label-text-weight);letter-spacing:var(--mdc-filled-text-field-label-text-tracking)}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color)}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color)}.mdc-text-field--filled .mdc-line-ripple::before{border-bottom-width:var(--mdc-filled-text-field-active-indicator-height)}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-caret-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-focus-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-hover-label-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-disabled-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-focus-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-hover-label-text-color)}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font);font-size:var(--mdc-outlined-text-field-label-text-size);font-weight:var(--mdc-outlined-text-field-label-text-weight);letter-spacing:var(--mdc-outlined-text-field-label-text-tracking)}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(.75*var(--mdc-outlined-text-field-label-text-size))}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mdc-outlined-text-field-label-text-size)}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-outlined-text-field-container-shape))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-hover-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-focus-outline-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-disabled-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-hover-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-focus-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-outline-width)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-focus-outline-width)}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-text-field-wrapper::before{content:none}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height);padding-top:var(--mat-form-field-filled-with-label-container-padding-top);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding);padding-bottom:var(--mat-form-field-container-vertical-padding)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:"";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color)}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font);line-height:var(--mat-form-field-subscript-text-line-height);font-size:var(--mat-form-field-subscript-text-size);letter-spacing:var(--mat-form-field-subscript-text-tracking);font-weight:var(--mat-form-field-subscript-text-weight)}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color)}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity)}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color)}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:"";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color)}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}.cdk-high-contrast-active .mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font);line-height:var(--mat-form-field-container-text-line-height);font-size:var(--mat-form-field-container-text-size);letter-spacing:var(--mat-form-field-container-text-tracking);font-weight:var(--mat-form-field-container-text-weight)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color)}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color)}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color)}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color)}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color)}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__affix{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled.mdc-ripple-upgraded--background-focused .mdc-text-field__ripple::before,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea{transition:none}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-filled 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-filled{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon{0%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}[dir=rtl] .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl{0%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}'],encapsulation:2,data:{animation:[i.transitionMessages]},changeDetection:0})}}return E})(),Gt=(()=>{class E{static{this.\u0275fac=function(R){return new(R||E)}}static{this.\u0275mod=t.$C({type:E})}static{this.\u0275inj=t.G2t({imports:[dt.yE,L.MD,X.w5,dt.yE]})}}return E})()},2798:(St,ft,f)=>{f.d(ft,{$2:()=>Ut,VO:()=>Pt,Ve:()=>Yt});var t=f(6969),h=f(177),s=f(3953),B=f(6600),nt=f(6467),Z=f(3980),z=f(6039),W=f(8203),A=f(5024),P=f(7336),ct=f(9417),tt=f(1413),it=f(9030),et=f(7786),pt=f(9172),$=f(5558),S=f(5964),L=f(6354),X=f(3294),dt=f(6977),lt=f(6697),q=f(9969);const k=["trigger"],O=["panel"],T=[[["mat-select-trigger"]],"*"],v=["mat-select-trigger","*"];function rt(ot,Lt){if(1&ot&&(s.j41(0,"span",4),s.EFF(1),s.k0s()),2&ot){const p=s.XpG();s.R7$(),s.JRh(p.placeholder)}}function st(ot,Lt){1&ot&&s.SdG(0)}function V(ot,Lt){if(1&ot&&(s.j41(0,"span",11),s.EFF(1),s.k0s()),2&ot){const p=s.XpG(2);s.R7$(),s.JRh(p.triggerValue)}}function bt(ot,Lt){if(1&ot&&(s.j41(0,"span",5),s.DNE(1,st,1,0)(2,V,2,1,"span",11),s.k0s()),2&ot){const p=s.XpG();s.R7$(),s.vxM(p.customTrigger?1:2)}}function At(ot,Lt){if(1&ot){const p=s.RV6();s.j41(0,"div",12,1),s.bIt("@transformPanel.done",function(I){s.eBV(p);const Q=s.XpG();return s.Njj(Q._panelDoneAnimatingStream.next(I.toState))})("keydown",function(I){s.eBV(p);const Q=s.XpG();return s.Njj(Q._handleKeydown(I))}),s.SdG(2,1),s.k0s()}if(2&ot){const p=s.XpG();s.ZvI("mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open ",p._getPanelTheme(),""),s.Y8G("ngClass",p.panelClass)("@transformPanel","showing"),s.BMQ("id",p.id+"-panel")("aria-multiselectable",p.multiple)("aria-label",p.ariaLabel||null)("aria-labelledby",p._getPanelAriaLabelledby())}}const jt={transformPanelWrap:(0,q.hZ)("transformPanelWrap",[(0,q.kY)("* => void",(0,q.P)("@transformPanel",[(0,q.MA)()],{optional:!0}))]),transformPanel:(0,q.hZ)("transformPanel",[(0,q.wk)("void",(0,q.iF)({opacity:0,transform:"scale(1, 0.8)"})),(0,q.kY)("void => showing",(0,q.i0)("120ms cubic-bezier(0, 0, 0.2, 1)",(0,q.iF)({opacity:1,transform:"scale(1, 1)"}))),(0,q.kY)("* => void",(0,q.i0)("100ms linear",(0,q.iF)({opacity:0})))])};let j=0;const U=new s.nKC("mat-select-scroll-strategy",{providedIn:"root",factory:()=>{const ot=(0,s.WQX)(t.hJ);return()=>ot.scrollStrategies.reposition()}}),vt=new s.nKC("MAT_SELECT_CONFIG"),kt={provide:U,deps:[t.hJ],useFactory:function K(ot){return()=>ot.scrollStrategies.reposition()}},Ct=new s.nKC("MatSelectTrigger");class It{constructor(Lt,p){this.source=Lt,this.value=p}}let Pt=(()=>{class ot{_scrollOptionIntoView(p){const D=this.options.toArray()[p];if(D){const I=this.panel.nativeElement,Q=(0,B.jb)(p,this.options,this.optionGroups),_t=D._getHostElement();I.scrollTop=0===p&&1===Q?0:(0,B.TL)(_t.offsetTop,_t.offsetHeight,I.scrollTop,I.offsetHeight)}}_positioningSettled(){this._scrollOptionIntoView(this._keyManager.activeItemIndex||0)}_getChangeEvent(p){return new It(this,p)}get focused(){return this._focused||this._panelOpen}get hideSingleSelectionIndicator(){return this._hideSingleSelectionIndicator}set hideSingleSelectionIndicator(p){this._hideSingleSelectionIndicator=p,this._syncParentProperties()}get placeholder(){return this._placeholder}set placeholder(p){this._placeholder=p,this.stateChanges.next()}get required(){return this._required??this.ngControl?.control?.hasValidator(ct.k0.required)??!1}set required(p){this._required=p,this.stateChanges.next()}get multiple(){return this._multiple}set multiple(p){this._multiple=p}get compareWith(){return this._compareWith}set compareWith(p){this._compareWith=p,this._selectionModel&&this._initializeSelection()}get value(){return this._value}set value(p){this._assignValue(p)&&this._onChange(p)}get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(p){this._errorStateTracker.matcher=p}get id(){return this._id}set id(p){this._id=p||this._uid,this.stateChanges.next()}get errorState(){return this._errorStateTracker.errorState}set errorState(p){this._errorStateTracker.errorState=p}constructor(p,D,I,Q,_t,xt,Et,zt,Vt,Nt,Xt,Bt,w,e){this._viewportRuler=p,this._changeDetectorRef=D,this._elementRef=_t,this._dir=xt,this._parentFormField=Vt,this.ngControl=Nt,this._liveAnnouncer=w,this._defaultOptions=e,this._positions=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom",panelClass:"mat-mdc-select-panel-above"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom",panelClass:"mat-mdc-select-panel-above"}],this._panelOpen=!1,this._compareWith=(n,i)=>n===i,this._uid="mat-select-"+j++,this._triggerAriaLabelledBy=null,this._destroy=new tt.B,this.stateChanges=new tt.B,this.disableAutomaticLabeling=!0,this._onChange=()=>{},this._onTouched=()=>{},this._valueId="mat-select-value-"+j++,this._panelDoneAnimatingStream=new tt.B,this._overlayPanelClass=this._defaultOptions?.overlayPanelClass||"",this._focused=!1,this.controlType="mat-select",this.disabled=!1,this.disableRipple=!1,this.tabIndex=0,this._hideSingleSelectionIndicator=this._defaultOptions?.hideSingleSelectionIndicator??!1,this._multiple=!1,this.disableOptionCentering=this._defaultOptions?.disableOptionCentering??!1,this.ariaLabel="",this.panelWidth=this._defaultOptions&&typeof this._defaultOptions.panelWidth<"u"?this._defaultOptions.panelWidth:"auto",this._initialized=new tt.B,this.optionSelectionChanges=(0,it.v)(()=>{const n=this.options;return n?n.changes.pipe((0,pt.Z)(n),(0,$.n)(()=>(0,et.h)(...n.map(i=>i.onSelectionChange)))):this._initialized.pipe((0,$.n)(()=>this.optionSelectionChanges))}),this.openedChange=new s.bkB,this._openedStream=this.openedChange.pipe((0,S.p)(n=>n),(0,L.T)(()=>{})),this._closedStream=this.openedChange.pipe((0,S.p)(n=>!n),(0,L.T)(()=>{})),this.selectionChange=new s.bkB,this.valueChange=new s.bkB,this._trackedModal=null,this._skipPredicate=n=>!this.panelOpen&&n.disabled,this.ngControl&&(this.ngControl.valueAccessor=this),null!=e?.typeaheadDebounceInterval&&(this.typeaheadDebounceInterval=e.typeaheadDebounceInterval),this._errorStateTracker=new B.X0(Q,Nt,zt,Et,this.stateChanges),this._scrollStrategyFactory=Bt,this._scrollStrategy=this._scrollStrategyFactory(),this.tabIndex=parseInt(Xt)||0,this.id=this.id}ngOnInit(){this._selectionModel=new A.CB(this.multiple),this.stateChanges.next(),this._panelDoneAnimatingStream.pipe((0,X.F)(),(0,dt.Q)(this._destroy)).subscribe(()=>this._panelDoneAnimating(this.panelOpen)),this._viewportRuler.change().pipe((0,dt.Q)(this._destroy)).subscribe(()=>{this.panelOpen&&(this._overlayWidth=this._getOverlayWidth(this._preferredOverlayOrigin),this._changeDetectorRef.detectChanges())})}ngAfterContentInit(){this._initialized.next(),this._initialized.complete(),this._initKeyManager(),this._selectionModel.changed.pipe((0,dt.Q)(this._destroy)).subscribe(p=>{p.added.forEach(D=>D.select()),p.removed.forEach(D=>D.deselect())}),this.options.changes.pipe((0,pt.Z)(null),(0,dt.Q)(this._destroy)).subscribe(()=>{this._resetOptions(),this._initializeSelection()})}ngDoCheck(){const p=this._getTriggerAriaLabelledby(),D=this.ngControl;if(p!==this._triggerAriaLabelledBy){const I=this._elementRef.nativeElement;this._triggerAriaLabelledBy=p,p?I.setAttribute("aria-labelledby",p):I.removeAttribute("aria-labelledby")}D&&(this._previousControl!==D.control&&(void 0!==this._previousControl&&null!==D.disabled&&D.disabled!==this.disabled&&(this.disabled=D.disabled),this._previousControl=D.control),this.updateErrorState())}ngOnChanges(p){(p.disabled||p.userAriaDescribedBy)&&this.stateChanges.next(),p.typeaheadDebounceInterval&&this._keyManager&&this._keyManager.withTypeAhead(this.typeaheadDebounceInterval)}ngOnDestroy(){this._keyManager?.destroy(),this._destroy.next(),this._destroy.complete(),this.stateChanges.complete(),this._clearFromModal()}toggle(){this.panelOpen?this.close():this.open()}open(){this._canOpen()&&(this._parentFormField&&(this._preferredOverlayOrigin=this._parentFormField.getConnectedOverlayOrigin()),this._overlayWidth=this._getOverlayWidth(this._preferredOverlayOrigin),this._applyModalPanelOwnership(),this._panelOpen=!0,this._keyManager.withHorizontalOrientation(null),this._highlightCorrectOption(),this._changeDetectorRef.markForCheck(),this.stateChanges.next())}_applyModalPanelOwnership(){const p=this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal="true"]');if(!p)return;const D=`${this.id}-panel`;this._trackedModal&&(0,z.Ae)(this._trackedModal,"aria-owns",D),(0,z.px)(p,"aria-owns",D),this._trackedModal=p}_clearFromModal(){this._trackedModal&&((0,z.Ae)(this._trackedModal,"aria-owns",`${this.id}-panel`),this._trackedModal=null)}close(){this._panelOpen&&(this._panelOpen=!1,this._keyManager.withHorizontalOrientation(this._isRtl()?"rtl":"ltr"),this._changeDetectorRef.markForCheck(),this._onTouched(),this.stateChanges.next())}writeValue(p){this._assignValue(p)}registerOnChange(p){this._onChange=p}registerOnTouched(p){this._onTouched=p}setDisabledState(p){this.disabled=p,this._changeDetectorRef.markForCheck(),this.stateChanges.next()}get panelOpen(){return this._panelOpen}get selected(){return this.multiple?this._selectionModel?.selected||[]:this._selectionModel?.selected[0]}get triggerValue(){if(this.empty)return"";if(this._multiple){const p=this._selectionModel.selected.map(D=>D.viewValue);return this._isRtl()&&p.reverse(),p.join(", ")}return this._selectionModel.selected[0].viewValue}updateErrorState(){this._errorStateTracker.updateErrorState()}_isRtl(){return!!this._dir&&"rtl"===this._dir.value}_handleKeydown(p){this.disabled||(this.panelOpen?this._handleOpenKeydown(p):this._handleClosedKeydown(p))}_handleClosedKeydown(p){const D=p.keyCode,I=D===P.n6||D===P.i7||D===P.UQ||D===P.LE,Q=D===P.Fm||D===P.t6,_t=this._keyManager;if(!_t.isTyping()&&Q&&!(0,P.rp)(p)||(this.multiple||p.altKey)&&I)p.preventDefault(),this.open();else if(!this.multiple){const xt=this.selected;_t.onKeydown(p);const Et=this.selected;Et&&xt!==Et&&this._liveAnnouncer.announce(Et.viewValue,1e4)}}_handleOpenKeydown(p){const D=this._keyManager,I=p.keyCode,Q=I===P.n6||I===P.i7,_t=D.isTyping();if(Q&&p.altKey)p.preventDefault(),this.close();else if(_t||I!==P.Fm&&I!==P.t6||!D.activeItem||(0,P.rp)(p))if(!_t&&this._multiple&&I===P.A&&p.ctrlKey){p.preventDefault();const xt=this.options.some(Et=>!Et.disabled&&!Et.selected);this.options.forEach(Et=>{Et.disabled||(xt?Et.select():Et.deselect())})}else{const xt=D.activeItemIndex;D.onKeydown(p),this._multiple&&Q&&p.shiftKey&&D.activeItem&&D.activeItemIndex!==xt&&D.activeItem._selectViaInteraction()}else p.preventDefault(),D.activeItem._selectViaInteraction()}_onFocus(){this.disabled||(this._focused=!0,this.stateChanges.next())}_onBlur(){this._focused=!1,this._keyManager?.cancelTypeahead(),!this.disabled&&!this.panelOpen&&(this._onTouched(),this._changeDetectorRef.markForCheck(),this.stateChanges.next())}_onAttached(){this._overlayDir.positionChange.pipe((0,lt.s)(1)).subscribe(()=>{this._changeDetectorRef.detectChanges(),this._positioningSettled()})}_getPanelTheme(){return this._parentFormField?`mat-${this._parentFormField.color}`:""}get empty(){return!this._selectionModel||this._selectionModel.isEmpty()}_initializeSelection(){Promise.resolve().then(()=>{this.ngControl&&(this._value=this.ngControl.value),this._setSelectionByValue(this._value),this.stateChanges.next()})}_setSelectionByValue(p){if(this.options.forEach(D=>D.setInactiveStyles()),this._selectionModel.clear(),this.multiple&&p)Array.isArray(p),p.forEach(D=>this._selectOptionByValue(D)),this._sortValues();else{const D=this._selectOptionByValue(p);D?this._keyManager.updateActiveItem(D):this.panelOpen||this._keyManager.updateActiveItem(-1)}this._changeDetectorRef.markForCheck()}_selectOptionByValue(p){const D=this.options.find(I=>{if(this._selectionModel.isSelected(I))return!1;try{return null!=I.value&&this._compareWith(I.value,p)}catch{return!1}});return D&&this._selectionModel.select(D),D}_assignValue(p){return!!(p!==this._value||this._multiple&&Array.isArray(p))&&(this.options&&this._setSelectionByValue(p),this._value=p,!0)}_getOverlayWidth(p){return"auto"===this.panelWidth?(p instanceof t.$Q?p.elementRef:p||this._elementRef).nativeElement.getBoundingClientRect().width:null===this.panelWidth?"":this.panelWidth}_syncParentProperties(){if(this.options)for(const p of this.options)p._changeDetectorRef.markForCheck()}_initKeyManager(){this._keyManager=new z.Au(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl()?"rtl":"ltr").withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(["shiftKey"]).skipPredicate(this._skipPredicate),this._keyManager.tabOut.subscribe(()=>{this.panelOpen&&(!this.multiple&&this._keyManager.activeItem&&this._keyManager.activeItem._selectViaInteraction(),this.focus(),this.close())}),this._keyManager.change.subscribe(()=>{this._panelOpen&&this.panel?this._scrollOptionIntoView(this._keyManager.activeItemIndex||0):!this._panelOpen&&!this.multiple&&this._keyManager.activeItem&&this._keyManager.activeItem._selectViaInteraction()})}_resetOptions(){const p=(0,et.h)(this.options.changes,this._destroy);this.optionSelectionChanges.pipe((0,dt.Q)(p)).subscribe(D=>{this._onSelect(D.source,D.isUserInput),D.isUserInput&&!this.multiple&&this._panelOpen&&(this.close(),this.focus())}),(0,et.h)(...this.options.map(D=>D._stateChanges)).pipe((0,dt.Q)(p)).subscribe(()=>{this._changeDetectorRef.detectChanges(),this.stateChanges.next()})}_onSelect(p,D){const I=this._selectionModel.isSelected(p);null!=p.value||this._multiple?(I!==p.selected&&(p.selected?this._selectionModel.select(p):this._selectionModel.deselect(p)),D&&this._keyManager.setActiveItem(p),this.multiple&&(this._sortValues(),D&&this.focus())):(p.deselect(),this._selectionModel.clear(),null!=this.value&&this._propagateChanges(p.value)),I!==this._selectionModel.isSelected(p)&&this._propagateChanges(),this.stateChanges.next()}_sortValues(){if(this.multiple){const p=this.options.toArray();this._selectionModel.sort((D,I)=>this.sortComparator?this.sortComparator(D,I,p):p.indexOf(D)-p.indexOf(I)),this.stateChanges.next()}}_propagateChanges(p){let D;D=this.multiple?this.selected.map(I=>I.value):this.selected?this.selected.value:p,this._value=D,this.valueChange.emit(D),this._onChange(D),this.selectionChange.emit(this._getChangeEvent(D)),this._changeDetectorRef.markForCheck()}_highlightCorrectOption(){if(this._keyManager)if(this.empty){let p=-1;for(let D=0;D<this.options.length;D++)if(!this.options.get(D).disabled){p=D;break}this._keyManager.setActiveItem(p)}else this._keyManager.setActiveItem(this._selectionModel.selected[0])}_canOpen(){return!this._panelOpen&&!this.disabled&&this.options?.length>0}focus(p){this._elementRef.nativeElement.focus(p)}_getPanelAriaLabelledby(){if(this.ariaLabel)return null;const p=this._parentFormField?.getLabelId();return this.ariaLabelledby?(p?p+" ":"")+this.ariaLabelledby:p}_getAriaActiveDescendant(){return this.panelOpen&&this._keyManager&&this._keyManager.activeItem?this._keyManager.activeItem.id:null}_getTriggerAriaLabelledby(){if(this.ariaLabel)return null;const p=this._parentFormField?.getLabelId();let D=(p?p+" ":"")+this._valueId;return this.ariaLabelledby&&(D+=" "+this.ariaLabelledby),D}_panelDoneAnimating(p){this.openedChange.emit(p)}setDescribedByIds(p){p.length?this._elementRef.nativeElement.setAttribute("aria-describedby",p.join(" ")):this._elementRef.nativeElement.removeAttribute("aria-describedby")}onContainerClick(){this.focus(),this.open()}get shouldLabelFloat(){return this.panelOpen||!this.empty||this.focused&&!!this.placeholder}static{this.\u0275fac=function(D){return new(D||ot)(s.rXU(Z.Xj),s.rXU(s.gRc),s.rXU(s.SKi),s.rXU(B.es),s.rXU(s.aKT),s.rXU(W.dS,8),s.rXU(ct.cV,8),s.rXU(ct.j4,8),s.rXU(nt.xb,8),s.rXU(ct.vO,10),s.kS0("tabindex"),s.rXU(U),s.rXU(z.Ai),s.rXU(vt,8))}}static{this.\u0275cmp=s.VBU({type:ot,selectors:[["mat-select"]],contentQueries:function(D,I,Q){if(1&D&&(s.wni(Q,Ct,5),s.wni(Q,B.wT,5),s.wni(Q,B.QC,5)),2&D){let _t;s.mGM(_t=s.lsd())&&(I.customTrigger=_t.first),s.mGM(_t=s.lsd())&&(I.options=_t),s.mGM(_t=s.lsd())&&(I.optionGroups=_t)}},viewQuery:function(D,I){if(1&D&&(s.GBs(k,5),s.GBs(O,5),s.GBs(t.WB,5)),2&D){let Q;s.mGM(Q=s.lsd())&&(I.trigger=Q.first),s.mGM(Q=s.lsd())&&(I.panel=Q.first),s.mGM(Q=s.lsd())&&(I._overlayDir=Q.first)}},hostAttrs:["role","combobox","aria-autocomplete","none","aria-haspopup","listbox",1,"mat-mdc-select"],hostVars:19,hostBindings:function(D,I){1&D&&s.bIt("keydown",function(_t){return I._handleKeydown(_t)})("focus",function(){return I._onFocus()})("blur",function(){return I._onBlur()}),2&D&&(s.BMQ("id",I.id)("tabindex",I.disabled?-1:I.tabIndex)("aria-controls",I.panelOpen?I.id+"-panel":null)("aria-expanded",I.panelOpen)("aria-label",I.ariaLabel||null)("aria-required",I.required.toString())("aria-disabled",I.disabled.toString())("aria-invalid",I.errorState)("aria-activedescendant",I._getAriaActiveDescendant()),s.AVh("mat-mdc-select-disabled",I.disabled)("mat-mdc-select-invalid",I.errorState)("mat-mdc-select-required",I.required)("mat-mdc-select-empty",I.empty)("mat-mdc-select-multiple",I.multiple))},inputs:{userAriaDescribedBy:[0,"aria-describedby","userAriaDescribedBy"],panelClass:"panelClass",disabled:[2,"disabled","disabled",s.L39],disableRipple:[2,"disableRipple","disableRipple",s.L39],tabIndex:[2,"tabIndex","tabIndex",p=>null==p?0:(0,s.Udg)(p)],hideSingleSelectionIndicator:[2,"hideSingleSelectionIndicator","hideSingleSelectionIndicator",s.L39],placeholder:"placeholder",required:[2,"required","required",s.L39],multiple:[2,"multiple","multiple",s.L39],disableOptionCentering:[2,"disableOptionCentering","disableOptionCentering",s.L39],compareWith:"compareWith",value:"value",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],errorStateMatcher:"errorStateMatcher",typeaheadDebounceInterval:[2,"typeaheadDebounceInterval","typeaheadDebounceInterval",s.Udg],sortComparator:"sortComparator",id:"id",panelWidth:"panelWidth"},outputs:{openedChange:"openedChange",_openedStream:"opened",_closedStream:"closed",selectionChange:"selectionChange",valueChange:"valueChange"},exportAs:["matSelect"],standalone:!0,features:[s.Jv_([{provide:nt.qT,useExisting:ot},{provide:B.is,useExisting:ot}]),s.GFd,s.OA$,s.aNF],ngContentSelectors:v,decls:11,vars:8,consts:[["fallbackOverlayOrigin","cdkOverlayOrigin","trigger",""],["panel",""],["cdk-overlay-origin","",1,"mat-mdc-select-trigger",3,"click"],[1,"mat-mdc-select-value"],[1,"mat-mdc-select-placeholder","mat-mdc-select-min-line"],[1,"mat-mdc-select-value-text"],[1,"mat-mdc-select-arrow-wrapper"],[1,"mat-mdc-select-arrow"],["viewBox","0 0 24 24","width","24px","height","24px","focusable","false","aria-hidden","true"],["d","M7 10l5 5 5-5z"],["cdk-connected-overlay","","cdkConnectedOverlayLockPosition","","cdkConnectedOverlayHasBackdrop","","cdkConnectedOverlayBackdropClass","cdk-overlay-transparent-backdrop",3,"backdropClick","attach","detach","cdkConnectedOverlayPanelClass","cdkConnectedOverlayScrollStrategy","cdkConnectedOverlayOrigin","cdkConnectedOverlayOpen","cdkConnectedOverlayPositions","cdkConnectedOverlayWidth"],[1,"mat-mdc-select-min-line"],["role","listbox","tabindex","-1",3,"keydown","ngClass"]],template:function(D,I){if(1&D){const Q=s.RV6();s.NAR(T),s.j41(0,"div",2,0),s.bIt("click",function(){return s.eBV(Q),s.Njj(I.open())}),s.j41(3,"div",3),s.DNE(4,rt,2,1,"span",4)(5,bt,3,1,"span",5),s.k0s(),s.j41(6,"div",6)(7,"div",7),s.qSk(),s.j41(8,"svg",8),s.nrm(9,"path",9),s.k0s()()()(),s.DNE(10,At,3,9,"ng-template",10),s.bIt("backdropClick",function(){return s.eBV(Q),s.Njj(I.close())})("attach",function(){return s.eBV(Q),s.Njj(I._onAttached())})("detach",function(){return s.eBV(Q),s.Njj(I.close())})}if(2&D){const Q=s.sdS(1);s.R7$(3),s.BMQ("id",I._valueId),s.R7$(),s.vxM(I.empty?4:5),s.R7$(6),s.Y8G("cdkConnectedOverlayPanelClass",I._overlayPanelClass)("cdkConnectedOverlayScrollStrategy",I._scrollStrategy)("cdkConnectedOverlayOrigin",I._preferredOverlayOrigin||Q)("cdkConnectedOverlayOpen",I.panelOpen)("cdkConnectedOverlayPositions",I._positions)("cdkConnectedOverlayWidth",I._overlayWidth)}},dependencies:[t.$Q,t.WB,h.YU],styles:['.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:" ";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}'],encapsulation:2,data:{animation:[jt.transformPanel]},changeDetection:0})}}return ot})(),Ut=(()=>{class ot{static{this.\u0275fac=function(D){return new(D||ot)}}static{this.\u0275dir=s.FsC({type:ot,selectors:[["mat-select-trigger"]],standalone:!0,features:[s.Jv_([{provide:Ct,useExisting:ot}])]})}}return ot})(),Yt=(()=>{class ot{static{this.\u0275fac=function(D){return new(D||ot)}}static{this.\u0275mod=s.$C({type:ot})}static{this.\u0275inj=s.G2t({providers:[kt],imports:[h.MD,t.z_,B.Sy,B.yE,Z.Gj,nt.RG,B.Sy,B.yE]})}}return ot})()}}]);
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { GeolocationDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class GeolocationService {
  apiName = 'Default';
  

  create = (input: GeolocationDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeolocationDto>({
      method: 'POST',
      url: '/api/app/geolocation',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/geolocation/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeolocationDto>({
      method: 'GET',
      url: `/api/app/geolocation/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<GeolocationDto>>({
      method: 'GET',
      url: '/api/app/geolocation',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: GeolocationDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeolocationDto>({
      method: 'PUT',
      url: `/api/app/geolocation/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

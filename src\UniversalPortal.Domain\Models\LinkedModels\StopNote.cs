﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.DirectModels;
using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

public class StopNote : AuditedEntity
{
    [Key, Column(Order = 0)]
    public int StopId { get; set; }
    [Key, Column(Order = 1)]
    public int NoteId { get; set; }
    [InverseProperty(nameof(Stop.StopNotes))]
    public Stop? Stop { get; set; }
    [InverseProperty(nameof(Note.StopNotes))]
    public Note? Note { get; set; }

    public override object[] GetKeys()
    {
        return [StopId, NoteId];
    }
}
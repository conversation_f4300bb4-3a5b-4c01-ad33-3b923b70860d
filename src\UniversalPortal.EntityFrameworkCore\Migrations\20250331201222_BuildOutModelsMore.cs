﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class BuildOutModelsMore : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Legs_Drivers_DriverId",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropIndex(
                name: "IX_Legs_DriverId",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropColumn(
                name: "DriverId",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.AlterColumn<int>(
                name: "Driver2Id",
                schema: "ControlTower",
                table: "Legs",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "Driver1Id",
                schema: "ControlTower",
                table: "Legs",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "EquipmentGeolocationsEquipmentId",
                schema: "ControlTower",
                table: "Geolocations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "GeolocationDate",
                schema: "ControlTower",
                table: "Geolocations",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<double>(
                name: "Latitude",
                schema: "ControlTower",
                table: "Geolocations",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Longitude",
                schema: "ControlTower",
                table: "Geolocations",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "ControlTower",
                table: "EquipmentTypes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EquipmentTypeName",
                schema: "ControlTower",
                table: "EquipmentTypes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "ControlTower",
                table: "Equipment",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Email",
                schema: "ControlTower",
                table: "Drivers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                schema: "ControlTower",
                table: "Drivers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                schema: "ControlTower",
                table: "Drivers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "EquipmentEquipmentGeolocation",
                schema: "ControlTower",
                columns: table => new
                {
                    EquipmentId = table.Column<int>(type: "int", nullable: false),
                    EquipmentGeolocationsEquipmentId = table.Column<int>(type: "int", nullable: false),
                    EquipmentGeolocationsGeolocationId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentEquipmentGeolocation", x => new { x.EquipmentId, x.EquipmentGeolocationsEquipmentId, x.EquipmentGeolocationsGeolocationId });
                    table.ForeignKey(
                        name: "FK_EquipmentEquipmentGeolocation_EquipmentGeolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                        columns: x => new { x.EquipmentGeolocationsEquipmentId, x.EquipmentGeolocationsGeolocationId },
                        principalSchema: "ControlTower",
                        principalTable: "EquipmentGeolocations",
                        principalColumns: ["EquipmentId", "GeolocationId"],
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EquipmentEquipmentGeolocation_Equipment_EquipmentId",
                        column: x => x.EquipmentId,
                        principalSchema: "ControlTower",
                        principalTable: "Equipment",
                        principalColumn: "EquipmentId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PowerUnitGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                column: "GeolocationId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Legs_Driver1Id",
                schema: "ControlTower",
                table: "Legs",
                column: "Driver1Id");

            migrationBuilder.CreateIndex(
                name: "IX_Legs_Driver2Id",
                schema: "ControlTower",
                table: "Legs",
                column: "Driver2Id");

            migrationBuilder.CreateIndex(
                name: "IX_Geolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations",
                columns: ["EquipmentGeolocationsEquipmentId", "EquipmentGeolocationsGeolocationId"]);

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentEquipmentGeolocation_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "EquipmentEquipmentGeolocation",
                columns: ["EquipmentGeolocationsEquipmentId", "EquipmentGeolocationsGeolocationId"]);

            migrationBuilder.AddForeignKey(
                name: "FK_Geolocations_EquipmentGeolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations",
                columns: ["EquipmentGeolocationsEquipmentId", "EquipmentGeolocationsGeolocationId"],
                principalSchema: "ControlTower",
                principalTable: "EquipmentGeolocations",
                principalColumns: ["EquipmentId", "GeolocationId"]);

            migrationBuilder.AddForeignKey(
                name: "FK_Legs_Drivers_Driver1Id",
                schema: "ControlTower",
                table: "Legs",
                column: "Driver1Id",
                principalSchema: "ControlTower",
                principalTable: "Drivers",
                principalColumn: "DriverId");

            migrationBuilder.AddForeignKey(
                name: "FK_Legs_Drivers_Driver2Id",
                schema: "ControlTower",
                table: "Legs",
                column: "Driver2Id",
                principalSchema: "ControlTower",
                principalTable: "Drivers",
                principalColumn: "DriverId");

            migrationBuilder.AddForeignKey(
                name: "FK_PowerUnitGeolocations_Geolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                column: "GeolocationId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PowerUnitGeolocations_PowerUnits_PowerUnitId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                column: "PowerUnitId",
                principalSchema: "ControlTower",
                principalTable: "PowerUnits",
                principalColumn: "PowerUnitId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Geolocations_EquipmentGeolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropForeignKey(
                name: "FK_Legs_Drivers_Driver1Id",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropForeignKey(
                name: "FK_Legs_Drivers_Driver2Id",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropForeignKey(
                name: "FK_PowerUnitGeolocations_Geolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations");

            migrationBuilder.DropForeignKey(
                name: "FK_PowerUnitGeolocations_PowerUnits_PowerUnitId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations");

            migrationBuilder.DropTable(
                name: "EquipmentEquipmentGeolocation",
                schema: "ControlTower");

            migrationBuilder.DropIndex(
                name: "IX_PowerUnitGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations");

            migrationBuilder.DropIndex(
                name: "IX_Legs_Driver1Id",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropIndex(
                name: "IX_Legs_Driver2Id",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropIndex(
                name: "IX_Geolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "EquipmentGeolocationsEquipmentId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "GeolocationDate",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "Latitude",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "Longitude",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "Description",
                schema: "ControlTower",
                table: "EquipmentTypes");

            migrationBuilder.DropColumn(
                name: "EquipmentTypeName",
                schema: "ControlTower",
                table: "EquipmentTypes");

            migrationBuilder.DropColumn(
                name: "Description",
                schema: "ControlTower",
                table: "Equipment");

            migrationBuilder.DropColumn(
                name: "Email",
                schema: "ControlTower",
                table: "Drivers");

            migrationBuilder.DropColumn(
                name: "Name",
                schema: "ControlTower",
                table: "Drivers");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                schema: "ControlTower",
                table: "Drivers");

            migrationBuilder.AlterColumn<int>(
                name: "Driver2Id",
                schema: "ControlTower",
                table: "Legs",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Driver1Id",
                schema: "ControlTower",
                table: "Legs",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DriverId",
                schema: "ControlTower",
                table: "Legs",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Legs_DriverId",
                schema: "ControlTower",
                table: "Legs",
                column: "DriverId");

            migrationBuilder.AddForeignKey(
                name: "FK_Legs_Drivers_DriverId",
                schema: "ControlTower",
                table: "Legs",
                column: "DriverId",
                principalSchema: "ControlTower",
                principalTable: "Drivers",
                principalColumn: "DriverId");
        }
    }
}

import { Environment } from '@abp/ng.core';

const baseUrl = 'controltowerapi.ulhdev.com';

const oAuthConfig = {
  issuer: 'http://controltowerapi.ulhdev.com',
  redirectUri: baseUrl,
  clientId: 'UniversalPortal_App',
  responseType: 'code',
  scope: 'offline_access UniversalPortal',
  requireHttps: true,
};

export const environment = {
  production: false,
  debug: false,
  application: {
    baseUrl,
    name: 'UniversalPortal',
  },
  mapTiles: {
    styleUrl: 'https://tiles.openfreemap.org/styles/bright',
    ToS: 'https://openfreemap.org/tos/'
  },
  oAuthConfig,
  apis: {
    default: {
      url: 'http://controltowerapi.ulhdev.com',
      rootNamespace: 'UniversalPortal',
    },
    AbpAccountPublic: {
      url: oAuthConfig.issuer,
      rootNamespace: 'AbpAccountPublic',
    },
  },
} as Environment;

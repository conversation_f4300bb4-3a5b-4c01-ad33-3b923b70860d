﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class DriverService(IRepository<Driver> repository) : ApplicationService, IDriverService
{
    public async Task<DriverDto> CreateAsync(DriverDto input)
    {
        var driver = ObjectMapper.Map<DriverDto, Driver>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Driver, DriverDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.DriverId == id);
    }

    public async Task<DriverDto> GetAsync(int id)
    {
        var driver = await repository.GetAsync(x => x.DriverId == id);
        return ObjectMapper.Map<Driver, DriverDto>(driver);
    }

    public async Task<PagedResultDto<DriverDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var driver = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<DriverDto>(
            totalCount,
            ObjectMapper.Map<List<Driver>, List<DriverDto>>(driver)
        );
    }

    public Task<DriverDto> UpdateAsync(int id, DriverDto input)
    {
        throw new NotImplementedException();
    }
}
import { Injectable } from "@angular/core";
import { StopDto } from "../../proxy/dtos/direct-data";
import { StopService } from "../../proxy/services";
import { LocalCache } from "../local-cache";
import { StopRecord } from "../records/stop.record";
import { StopUpdateService } from "../update-services/stop-update.service";
import { OrderCache } from "./order.cache";


@Injectable({
  providedIn: 'root'
})
export class StopCache extends LocalCache<StopRecord, StopDto> {
  public processDto(dto: StopDto): StopRecord {
    return new StopRecord(dto, this.cacheType, this.orderCache);
  }
  public constructor(
   private stopService: StopService,
   private stopUpdateService: StopUpdateService,
   private orderCache: OrderCache
  ) {
    super("Stop", true, stopService, stopUpdateService);
  }
}

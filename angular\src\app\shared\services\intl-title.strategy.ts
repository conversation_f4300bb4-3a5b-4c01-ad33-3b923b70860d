
import { Title } from '@angular/platform-browser';
import { LocalizationService } from '@abp/ng.core';
import { Injectable, inject } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, TitleStrategy } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class IntlTitleStrategy extends TitleStrategy {

  private static appName: string = "AppName";

  constructor(
    private readonly title: Title,
    private readonly localizationService: LocalizationService
  ) {
    super();

  }

  override buildTitle(snapshot: RouterStateSnapshot): string {
    var pageKey = this.getLastRouteTitle(snapshot);
    console.log(`Building title from key '${pageKey}'`);

    var pageTitle = this.localizationService.instant({
      key: pageKey,
      defaultValue: `\{${snapshot.root.title}\}`
    });

    var appTitle = this.localizationService.instant({
      key: IntlTitleStrategy.appName,
      defaultValue: "Control Tower"
    })

    return pageTitle + ' | ' + appTitle;
  }

  override updateTitle(snapshot: RouterStateSnapshot): void {

    this.title.setTitle(this.buildTitle(snapshot));
    this.localizationService.languageChange$.subscribe(() => {
      this.title.setTitle(this.buildTitle(snapshot));
    })
  }

  //private setPageTitle(titleKey: string): void {

  //  console.log(`Setting title of page ${this.title.getTitle()}:`, localizedTitle);

  //  this.title.setTitle(localizedTitle + ' | ' + this.title.getTitle());
  //}

  // surely this can't be the most elegant way of doing this??
  private getLastRouteTitle(snapshot: RouterStateSnapshot): string {
    let currentRoute = snapshot.root;

    while (currentRoute.routeConfig == null) {
      currentRoute = currentRoute.firstChild;
    }

    return currentRoute.title;
  }

}

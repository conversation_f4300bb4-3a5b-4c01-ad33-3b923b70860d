﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Stop : TenantAuditedEntity
{
    public Stop()
    {
        StopContacts = [];
        StopNotes = [];
        StopEquipment = [];
    }

    public Stop(Stop stop)
    {
        StopId = stop.StopId;
        StopNumber = stop.StopNumber;
        OrderId = stop.OrderId;
        AddressId = stop.AddressId;
        MoveNumber = stop.MoveNumber;
        Type = stop.Type;
        Sequence = stop.Sequence;
        Mileage = stop.Mileage;
        ScheduledEarliest = stop.ScheduledEarliest;
        Arrival = stop.Arrival;
        ArrivalStatus = stop.ArrivalStatus;
        LateArrivalReason = stop.LateArrivalReason;
        UniversalLateArrival = stop.UniversalLateArrival;
        ScheduledLatest = stop.ScheduledLatest;
        Departure = stop.Departure;
        DepartureStatus = stop.DepartureStatus;
        LateDepartureReason = stop.LateDepartureReason;
        UniversalLateDeparture = stop.UniversalLateDeparture;
        
        StopContacts = [];
        StopNotes = [];
        StopEquipment = [];
    }

    [Key]
    public int StopId { get; set; }
    public int? AddressId { get; set; }
    public int StopNumber { get; set; }
    public int OrderId { get; set; }
    public int MoveNumber { get; set; }
    public string Type { get; set; } = string.Empty;
    public int Sequence { get; set; }
    public int Mileage { get; set; }
    public DateTime ScheduledEarliest { get; set; }
    public DateTime Arrival { get; set; }
    public int ArrivalStatus { get; set; }
    public string? LateArrivalReason { get; set; }
    public int UniversalLateArrival {  get; set; }
    public DateTime ScheduledLatest { get; set; }
    public DateTime Departure { get; set; }
    public int DepartureStatus { get; set; }
    public string? LateDepartureReason { get; set; }
    public int UniversalLateDeparture { get; set; }
    [InverseProperty(nameof(StopContact.Stop))]
    public ICollection<StopContact> StopContacts { get; set; }
    [InverseProperty(nameof(StopNote.Stop))]
    public ICollection<StopNote> StopNotes { get; set; }
    [InverseProperty(nameof(LinkedModels.StopEquipment.Stop))]
    public ICollection<StopEquipment> StopEquipment { get; set; }
    [ForeignKey(nameof(OrderId))]
    [InverseProperty(nameof(DirectModels.Order.Stops))]
    public Order? Order { get; set; }
    [InverseProperty(nameof(DirectModels.Leg.Stops))]
    public Leg? Leg { get; set; }
    [ForeignKey(nameof(AddressId))]
    [InverseProperty(nameof(DirectModels.Address.Stops))]
    public Address? Address { get; set; }

    public override object[] GetKeys()
    {
        return [StopId];
    }
}
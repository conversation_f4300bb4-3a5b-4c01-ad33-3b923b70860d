import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { LocationReportQueryDto } from '../dtos/queries/models';
import type { BinnedQuantityDto } from '../dtos/semantic-data/models';

@Injectable({
  providedIn: 'root',
})
export class ReportingService {
  apiName = 'Default';
  

  getAverageDwellTimeByLocation = (input: LocationReportQueryDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BinnedQuantityDto[]>({
      method: 'GET',
      url: '/api/app/reporting/average-dwell-time-by-location',
      params: { rangeBegin: input.rangeBegin, rangeEnd: input.rangeEnd, locationIds: input.locationIds },
    },
    { apiName: this.apiName,...config });
  

  getAverageHoursSinceMoveByLocation = (input: LocationReportQueryDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BinnedQuantityDto[]>({
      method: 'GET',
      url: '/api/app/reporting/average-hours-since-move-by-location',
      params: { rangeBegin: input.rangeBegin, rangeEnd: input.rangeEnd, locationIds: input.locationIds },
    },
    { apiName: this.apiName,...config });
  

  getInactiveAssetsByLocation = (input: LocationReportQueryDto, inactiveHours: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BinnedQuantityDto[]>({
      method: 'GET',
      url: '/api/app/reporting/inactive-assets-by-location',
      params: { rangeBegin: input.rangeBegin, rangeEnd: input.rangeEnd, locationIds: input.locationIds, inactiveHours },
    },
    { apiName: this.apiName,...config });
  

  getTrailerCountByLocation = (input: LocationReportQueryDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BinnedQuantityDto[]>({
      method: 'GET',
      url: '/api/app/reporting/trailer-count-by-location',
      params: { rangeBegin: input.rangeBegin, rangeEnd: input.rangeEnd, locationIds: input.locationIds },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

/// <summary>
/// Provides utilities for building dynamic LINQ expressions at runtime.
/// Supports common query operations including equality, comparison, contains, and in-list filtering.
/// </summary>
public static class ExpressionBuilder
{
    /// <summary>
    /// Builds a predicate expression for the specified property, operation, and value.
    /// Handles type conversion between string values and target property types.
    /// </summary>
    /// <typeparam name="T">The entity type to build the predicate for</typeparam>
    /// <param name="propertyName">The name of the property to filter on</param>
    /// <param name="operation">The comparison operation (Equal, GreaterThan, etc.)</param>
    /// <param name="value">The string value to compare against (will be parsed to appropriate type)</param>
    /// <returns>A compiled predicate expression</returns>
    /// <exception cref="ArgumentException">Thrown when propertyName doesn't exist on type T</exception>
    public static Expression<Func<T, bool>> BuildPredicate<T>(string propertyName, ExpressionType operation, string value)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyName);
        
        // Convert string value to the appropriate type (int, double, bool, DateTime, or string)
        ConstantExpression? constant = Expression.Constant(TryParseToDbQueryType(value));

        // Handle type mismatches between property and constant, especially nullable types
        Expression left = property;
        Expression right = constant;

        if (property.Type != constant.Type)
        {
            if (IsNullableType(property.Type) && !IsNullableType(constant.Type))
            {
                right = Expression.Convert(constant, property.Type);
            }
            else if (!IsNullableType(property.Type) && IsNullableType(constant.Type))
            {
                left = Expression.Convert(property, constant.Type);
            }
            else
            {
                right = Expression.Convert(constant, property.Type);
            }
        }

        var body = Expression.MakeBinary(operation, left, right);
        return Expression.Lambda<Func<T, bool>>(body, parameter);
    }

    /// <summary>
    /// Builds a string contains predicate for the specified property.
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    /// <param name="propertyName">The string property name to search within</param>
    /// <param name="value">The substring to search for</param>
    /// <returns>A predicate that checks if the property contains the specified value</returns>
    public static Expression<Func<T, bool>> BuildContainsPredicate<T>(string propertyName, string value)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyName);
        var method = typeof(string).GetMethod("Contains", [typeof(string)]);
        var constant = Expression.Constant(value);
        var body = Expression.Call(property, method!, constant);

        return Expression.Lambda<Func<T, bool>>(body, parameter);
    }

    /// <summary>
    /// Builds an "in list" predicate that checks if a property value exists in the provided collection.
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    /// <typeparam name="TValue">The type of values in the collection</typeparam>
    /// <param name="propertyName">The property name to check</param>
    /// <param name="values">The collection of values to check against</param>
    /// <returns>A predicate that checks if the property value is in the collection</returns>
    public static Expression<Func<T, bool>> BuildInPredicate<T, TValue>(string propertyName, IEnumerable<TValue> values)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyName);
        var constant = Expression.Constant(values);

        var method = typeof(Enumerable).GetMethods()
            .Where(m => m.Name == "Contains" && m.GetParameters().Length == 2)
            .Single()
            .MakeGenericMethod(typeof(TValue));

        var body = Expression.Call(method, constant, property);
        return Expression.Lambda<Func<T, bool>>(body, parameter);
    }

    /// <summary>
    /// Combines two predicate expressions with a logical AND operation.
    /// </summary>
    public static Expression<Func<T, bool>> And<T>(Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var leftBody = ReplaceParameter(left.Body, left.Parameters[0], parameter);
        var rightBody = ReplaceParameter(right.Body, right.Parameters[0], parameter);
        var body = Expression.AndAlso(leftBody, rightBody);

        return Expression.Lambda<Func<T, bool>>(body, parameter);
    }

    /// <summary>
    /// Combines two predicate expressions with a logical OR operation.
    /// </summary>
    public static Expression<Func<T, bool>> Or<T>(Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var leftBody = ReplaceParameter(left.Body, left.Parameters[0], parameter);
        var rightBody = ReplaceParameter(right.Body, right.Parameters[0], parameter);
        var body = Expression.OrElse(leftBody, rightBody);

        return Expression.Lambda<Func<T, bool>>(body, parameter);
    }

    /// <summary>
    /// Builds an ordering expression for the specified property.
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    /// <param name="propertyName">The property name to order by</param>
    /// <returns>An expression that extracts the property value for ordering</returns>
    public static Expression<Func<T, object>> BuildOrderExpression<T>(string propertyName)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, propertyName);
        var converted = Expression.Convert(property, typeof(object));

        return Expression.Lambda<Func<T, object>>(converted, parameter);
    }

    /// <summary>
    /// Determines if a type is a nullable value type (e.g., int?, DateTime?).
    /// </summary>
    private static bool IsNullableType(Type type)
    {
        return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>);
    }

    /// <summary>
    /// Replaces parameter references in an expression tree.
    /// Used when combining expressions that have different parameter instances.
    /// </summary>
    private static Expression ReplaceParameter(Expression expression, ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        return new ParameterReplacer(oldParameter, newParameter).Visit(expression);
    }

    /// <summary>
    /// Expression visitor that replaces parameter references.
    /// </summary>
    private class ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter) : ExpressionVisitor
    {
        private readonly ParameterExpression _oldParameter = oldParameter;
        private readonly ParameterExpression _newParameter = newParameter;

        protected override Expression VisitParameter(ParameterExpression node)
        {
            return node == _oldParameter ? _newParameter : base.VisitParameter(node);
        }
    }

    /// <summary>
    /// Attempts to parse a string value to common database query types.
    /// Tries parsing as int, double, DateTime, bool, and falls back to string.
    /// </summary>
    /// <param name="objString">The string value to parse</param>
    /// <returns>The parsed value in its appropriate type, or the original string if no parsing succeeded</returns>
    public static dynamic TryParseToDbQueryType(string objString)
    {
        ArgumentNullException.ThrowIfNull(objString);

        if (int.TryParse(objString, out int returnInt))
            return returnInt;

        if (double.TryParse(objString, out double returnDouble))
            return returnDouble;
          
        if (DateTime.TryParse(objString, out DateTime returnDateTime))
            return returnDateTime;

        if (bool.TryParse(objString, out bool returnBool))
            return returnBool;

        return objString;
    }
}

/// <summary>
/// Provides a fluent interface for building complex predicate expressions.
/// Allows chaining multiple conditions with AND/OR operations.
/// </summary>
/// <typeparam name="T">The entity type to build predicates for</typeparam>
/// <example>
/// var predicate = new FluentExpressionBuilder<User>()
///     .Where("Age", ExpressionType.GreaterThan, "18")
///     .WhereContains("Name", "John")
///     .Build();
/// </example>
public class FluentExpressionBuilder<T>
{
    private Expression<Func<T, bool>> _expression;

    public FluentExpressionBuilder()
    {
        _expression = x => true; // Start with a predicate that matches all records
    }

    /// <summary>
    /// Adds a condition using the specified property, operation, and value.
    /// </summary>
    public FluentExpressionBuilder<T> Where(string propertyName, ExpressionType operation, string value)
    {
        var predicate = ExpressionBuilder.BuildPredicate<T>(propertyName, operation, value);
        _expression = ExpressionBuilder.And(_expression, predicate);
        return this;
    }

    /// <summary>
    /// Adds a string contains condition.
    /// </summary>
    public FluentExpressionBuilder<T> WhereContains(string propertyName, string value)
    {
        var predicate = ExpressionBuilder.BuildContainsPredicate<T>(propertyName, value);
        _expression = ExpressionBuilder.And(_expression, predicate);
        return this;
    }

    /// <summary>
    /// Adds an "in list" condition.
    /// </summary>
    public FluentExpressionBuilder<T> WhereIn<TValue>(string propertyName, IEnumerable<TValue> values)
    {
        var predicate = ExpressionBuilder.BuildInPredicate<T, TValue>(propertyName, values);
        _expression = ExpressionBuilder.And(_expression, predicate);
        return this;
    }

    /// <summary>
    /// Adds an OR condition with the specified expression.
    /// </summary>
    public FluentExpressionBuilder<T> Or(Expression<Func<T, bool>> orExpression)
    {
        _expression = ExpressionBuilder.Or(_expression, orExpression);
        return this;
    }

    /// <summary>
    /// Builds the final predicate expression.
    /// </summary>
    public Expression<Func<T, bool>> Build()
    {
        return _expression;
    }

    /// <summary>
    /// Implicit conversion to allow direct use as an expression.
    /// </summary>
    public static implicit operator Expression<Func<T, bool>>(FluentExpressionBuilder<T> builder)
    {
        return builder.Build();
    }
}
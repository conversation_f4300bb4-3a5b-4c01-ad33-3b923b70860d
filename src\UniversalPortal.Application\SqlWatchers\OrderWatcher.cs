﻿using AtlasCommon.Infrastructure.Abstractions;
using AtlasCommon.Infrastructure.Attributes;
using AtlasCommon.Infrastructure.Enums;

using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

using System;
using System.Threading.Tasks;

using UniversalPortal.DTOs;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Hubs;
using UniversalPortal.Models.DirectModels;

using Volo.Abp.Application.Services;

namespace UniversalPortal.SqlWatchers;

[SqlWatcher(ConnectionName = "UniversalPortalDb", NotificationType = SqlWatcherNotificationType.Insert | SqlWatcherNotificationType.Update, SchemaName = "ControlTower", TableName = "Orders")]
public class OrderWatcher(ILogger<OrderWatcher> logger,IHubContext<LiveUpdateHub, IHubClient> hubContext) : ApplicationService, ISqlWatcherHandler<Order>
{
    public async Task HandleAsync(Order? current, Order? previous)
    {
        try
        {
            if(current is null)
                return;

            ChangeDto<OrderDto> message = new(
                previous is null ? null : ObjectMapper.Map<Order, OrderDto>(previous),
                ObjectMapper.Map<Order, OrderDto>(current));

            await hubContext.Clients.Group(nameof(Order)).Publish(message, nameof(Order));
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Failed to publish order update to the consumers. \r\n\r\n{currentOrder}", current);
            throw;
        }
    }
}
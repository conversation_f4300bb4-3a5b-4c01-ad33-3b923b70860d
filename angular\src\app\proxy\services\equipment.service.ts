import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { EquipmentDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class EquipmentService {
  apiName = 'Default';
  

  create = (input: EquipmentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EquipmentDto>({
      method: 'POST',
      url: '/api/app/equipment',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/equipment/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EquipmentDto>({
      method: 'GET',
      url: `/api/app/equipment/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<EquipmentDto>>({
      method: 'GET',
      url: '/api/app/equipment',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: EquipmentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EquipmentDto>({
      method: 'PUT',
      url: `/api/app/equipment/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

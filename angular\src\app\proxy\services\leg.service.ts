import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { LegDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class LegService {
  apiName = 'Default';
  

  create = (input: LegDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, LegDto>({
      method: 'POST',
      url: '/api/app/leg',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/leg/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, LegDto>({
      method: 'GET',
      url: `/api/app/leg/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<LegDto>>({
      method: 'GET',
      url: '/api/app/leg',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: LegDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, LegDto>({
      method: 'PUT',
      url: `/api/app/leg/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { DriverDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class DriverService {
  apiName = 'Default';
  

  create = (input: DriverDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, DriverDto>({
      method: 'POST',
      url: '/api/app/driver',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/driver/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, DriverDto>({
      method: 'GET',
      url: `/api/app/driver/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<DriverDto>>({
      method: 'GET',
      url: '/api/app/driver',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: DriverDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, DriverDto>({
      method: 'PUT',
      url: `/api/app/driver/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

import { Component, ViewChild, AfterViewInit, inject } from '@angular/core';
import { animate, sequence, state, style, transition, trigger } from '@angular/animations';
import { environment } from '../../../environments/environment';
import { StopTableComponent } from '../../UI/stop-table/stop-table.component';
import { OrderCardComponent } from '../../UI/order-card/order-card.component';
import { GeoMapComponent } from '../../UI/geo-map/geo-map.component';
import { StopRecord } from '../../data/records/stop.record';
import { OrderRecord } from '../../data/records/order.record';

@Component({
  selector: 'app-root',
  styleUrl: 'dashboard.component.scss',
  templateUrl: 'dashboard.component.html',
  standalone: false,
  animations: [
    trigger('highlightChanges', [
      transition(':increment', [
        animate('100ms ease-out', style({ backgroundColor: 'yellow' })),
        animate('1200ms ease-in', style({ backgroundColor: '*' }))
      ]),
    ]),
    trigger('highlightNew', [
      transition('void => true', [
        animate('100ms ease-out', style({ backgroundColor: 'lime' })),
        animate('2400ms ease-in', style({ backgroundColor: '*' }))
      ]),
    ]),
    trigger('gridFade', [
      transition("* => *", [
        style({ opacity: 0 }),
        animate('150ms ease-in', style({ opacity: 1 }))
      ])
    ])
  ],
})
export class DashboardComponent implements AfterViewInit {

  public readonly debugFlag: boolean = environment.debug;

  public inspectingOrder: boolean = false;

  public readonly debugColumns: string[] = [
    'Stop:stopId',
    'Order:orderId',
    'Record:importedTime',
    'Record:lastUpdated'
  ]

  public readonly stickyColumns: string[] = [
    'Order:orderNumber'
  ];

  private readonly key: (keyof StopRecord) = 'order';

  public readonly displayedColumns: string[] = [

    'Order:startDate',
    'Order:route',
    'Order:miles',

    'Order:companyId',
    'Order:companyName',

    'Stop:stopNumber',
    'Stop:type',
    'Stop:sequence',
    'Stop:mileage',

    'Stop:scheduledEarliest',
    'Stop:arrival',
    'Stop:arrivalStatus',
    'Stop:arrivalDisparityMinutes',
    'Stop:lateArrivalReason',
    'Stop:universalLateArrival',

    'Stop:scheduledLatest',
    'Stop:departure',
    'Stop:departureStatus',
    'Stop:departureDisparityMinutes',
    'Stop:lateDepartureReason',
    'Stop:universalLateDeparture',

    'Stop:minutesWaiting',
    'Stop:windowTimeMinutes',
    'Stop:minutesOverTimeWindow',
    'Order:weekEnding'
  ];

  public readonly subStickyColumns: string[] = [
    'Stop:stopNumber'
  ];

  public readonly subDisplayedColumns: string[] = [
    'Stop:type',
    'Stop:sequence',
    'Stop:mileage',

    'Stop:scheduledEarliest',
    'Stop:arrival',
    'Stop:arrivalStatus',
    'Stop:arrivalDisparityMinutes',
    'Stop:lateArrivalReason',
    'Stop:universalLateArrival',

    'Stop:scheduledLatest',
    'Stop:departure',
    'Stop:departureStatus',
    'Stop:departureDisparityMinutes',
    'Stop:lateDepartureReason',
    'Stop:universalLateDeparture',

    'Stop:minutesWaiting',
    'Stop:windowTimeMinutes',
    'Stop:minutesOverTimeWindow',
  ];

  public allColumns: string[];

  @ViewChild('stopsTable') stopsTable: StopTableComponent;
  @ViewChild('orderCard') orderCard: OrderCardComponent;
  @ViewChild('stopsByOrderTable') stopsByOrderTable: StopTableComponent;
  @ViewChild('mappedStopsByOrder') mappedStopsByOrder: GeoMapComponent;

  constructor() {
    if (!this.debugFlag) {
      this.debugColumns = [];
    }
  }

  ngAfterViewInit() {
    this.stopsTable.inspectedRecord$.subscribe((row: StopRecord) => {
      if (row != null) {
        this.inspectRecord(row);
      }
    })

    this.stopsByOrderTable.setFilter(x => '', 'N/A');
  }

  inspectRecord(stop: StopRecord) {

    this.orderCard.targetNewOrder(stop.order);
    this.inspectingOrder = true;

    this.stopsByOrderTable.setFilter(DashboardComponent.orderIdSelector, stop.order.getPrimaryId().toString());
    this.mappedStopsByOrder.changeDisplayedStops(this.stopsByOrderTable.getFilteredStops());
  }

  private static orderIdSelector: ((StopRecord) => string) = (rec: StopRecord) => rec.order.getPrimaryId().toString()

  clearInspection() {
    this.inspectingOrder = false;
    this.stopsByOrderTable.resetPov();
    //we could reset the filter, too, but it's invisible anyways
  }
}

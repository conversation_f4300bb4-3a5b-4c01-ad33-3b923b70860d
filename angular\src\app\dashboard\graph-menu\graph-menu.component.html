<div class="graph-menu">

  <section class="radio">
    <mat-label>{{ '::Chart:select' | abpLocalization }}</mat-label>
    <mat-radio-group class="radio-group"
                     [(ngModel)]="selectedStatistic"
                     layout="row"
                     (change)="onSelectedStatisticChanged()">
      @for (stat of statistics; track stat[0]) {
        <mat-radio-button class="radio-button" [value]="stat">{{stat.optionName}}</mat-radio-button>
      }
    </mat-radio-group>
  </section>

  <div>
    <mat-divider></mat-divider>
  </div>

  <section>
    <div class="controls">

      <mat-form-field class="singleControl"
                      [hidden]="!dateRangeEnabled">
        <mat-label>{{ '::Chart:dateRange' | abpLocalization }}</mat-label>
        <mat-date-range-input [formGroup]="dateRange"
                              [rangePicker]="dateRangePicker"
                              [max]="getCurrentDate()">
          <input matStartDate placeholder="Start date"
                 formControlName="start"
                 [disabled]="!dateRangeEnabled">
          <input matEndDate placeholder="End date"
                 formControlName="end"
                 [disabled]="!dateRangeEnabled">
        </mat-date-range-input>
        <mat-datepicker-toggle matIconSuffix [for]="dateRangePicker"></mat-datepicker-toggle>
        <mat-date-range-picker #dateRangePicker></mat-date-range-picker>
      </mat-form-field>

      <mat-form-field class="singleControl">
        <mat-label>{{ '::Chart:locations' | abpLocalization }}</mat-label>
        <mat-select [(value)]="selectedGeofences"
                    multiple>
          <mat-select-trigger>
            ({{selectedGeofences.length}} selected)
          </mat-select-trigger>
          @for (fence of geofences; track fence) {
          <mat-option [value]="fence.id" (click)="validateSelectionStatus()">{{fence.name}}</mat-option>
          }
        </mat-select>
      </mat-form-field>

      <div class="singleControl">
      <button class="mat-button"
              mat-raised-button
              (click)="selectAllOrUnselectAll()">{{stringSelectAllButton}}</button>
      </div>
    </div>
  </section>

  <section>
    <button class="mat-button"
            mat-raised-button
            [disabled]="!chartGenerationEnabled()"
            (click)="generateChart()">{{ '::Chart:generate' | abpLocalization }}</button>
  </section>

  <div [hidden]="buttonPressed == false">
    <mat-divider></mat-divider>
  </div>
  <section [hidden]="buttonPressed == false"
           class="vertical-layout">
    <div>
      <mat-spinner [hidden]="!loadingChart"></mat-spinner>
    </div>
    <div class="canvas"
          [@unhidden]="!loadingChart">
      <canvas #canvas
              [hidden]="loadingChart"></canvas>
    </div>
  </section>
</div>

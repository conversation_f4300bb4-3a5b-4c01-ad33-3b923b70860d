﻿using System;
using System.Collections.Generic;
using UniversalPortal.DTOs.LinkedData;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class OrderDto : AuditedEntityDto
{
    public OrderDto()
    {
        OrderContacts = [];
        OrderNotes = [];
        Stops = [];
    }

    public int OrderId { get; set; }
    public int OrderNumber { get; set; }
    public int MoveNumber {  get; set; }
    public DateTime StartDate { get; set; }
    public string? Route {  get; set; }
    public int Miles { get; set; }
    public string? CompanyId { get; set; }
    public string? CompanyName { get; set; }

    public ICollection<OrderContactDto> OrderContacts { get; set; }
    public ICollection<OrderNoteDto> OrderNotes { get; set; }
    public ICollection<StopDto> Stops { get; set; }
}
//---------------------------------------------------
// Whole Page
//------------

.dashboard {
    display: flex;
    flex-direction: column;
}

stop-table {
    border: 2px solid teal;
}

.unhidden {
    border: 2px solid black;
}

.controls {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.mat-icon {
  font-size: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

//---------------------------------------------------
// Page Header Contents
//----------------------

/*.dash-header {
    display: flex;
    flex-direction: row;
}

button .header-button {
  //position: sticky;
  top: 0;
  background-color: #f9f9f9;
}*/

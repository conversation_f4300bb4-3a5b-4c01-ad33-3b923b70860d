import { OrderDto } from "../../proxy/dtos/direct-data";
import { LocalRecord } from "../local-record";

/**
 * Represents a locally-cached instance of a single Stop.
 */
export class OrderRecord extends LocalRecord<OrderDto> {
  constructor(
    dto: OrderDto,
    fromSignalRGroup: string
  ) {
    super(dto, fromSignalRGroup);
    this.calculateDerivedFields(dto);
  }

  //Derived fields
  public weekEnding: Date;

  public subsumes(otherDto: OrderDto): boolean {
    return this.localData.orderId == otherDto.orderId;
  }

  public getPrimaryId(): number {
    return this.localData.orderId;
  }

  protected override calculateDerivedFields(dto: OrderDto) {
    this.setDerivedField('weekEnding', OrderRecord.determineWeekEnding(new Date(dto.startDate)));
  }

  private static determineWeekEnding(date: Date): Date {
    let day = date.getDay();
    let saturday = new Date(date);
    // Calculate the difference to Saturday
    saturday.setDate(date.getDate() + (6 - day) % 7);
    return saturday;
  }

}

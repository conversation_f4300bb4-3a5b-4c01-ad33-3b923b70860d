
import { Component, ViewChild, Inject, AfterViewInit } from '@angular/core';
//import { BehaviorSubject } from 'rxjs';
import { MatCard } from '@angular/material/card';
import { MatChip } from '@angular/material/chips';
import { animate, sequence, state, style, transition, trigger } from '@angular/animations';
import { OrderCache } from '../../data/caches/order.cache';
import { OrderRecord } from '../../data/records/order.record';

@Component({
  selector: 'app-order-card',
  styleUrl: 'order-card.component.scss',
  templateUrl: 'order-card.component.html',
  standalone: false,
  animations: [
    trigger('highlightChanges', [
      transition(':increment', [
        animate('100ms ease-out', style({ backgroundColor: 'yellow' })),
        animate('1200ms ease-in', style({ backgroundColor: '*' }))
      ]),
    ]),
  ],
})
export class OrderCardComponent /*implements AfterViewInit*/ {

  public displayOrder: OrderRecord = null;

  @ViewChild('card') card: MatCard;

  constructor(
    @Inject(OrderCache) public orderCache: OrderCache
  ) { }

  public targetNewOrder(order: OrderRecord): void {
    this.displayOrder = order;
  }

  public formatOrderNumber(): string {
    return this.displayOrder?.localData.orderNumber.toString() ?? "None";
  }
}

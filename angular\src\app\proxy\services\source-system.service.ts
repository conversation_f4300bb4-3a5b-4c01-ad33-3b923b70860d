import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { SourceSystemDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class SourceSystemService {
  apiName = 'Default';
  

  create = (input: SourceSystemDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, SourceSystemDto>({
      method: 'POST',
      url: '/api/app/source-system',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/source-system/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, SourceSystemDto>({
      method: 'GET',
      url: `/api/app/source-system/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<SourceSystemDto>>({
      method: 'GET',
      url: '/api/app/source-system',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: SourceSystemDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, SourceSystemDto>({
      method: 'PUT',
      url: `/api/app/source-system/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

# scripts/manage-iis.ps1
# Advanced IIS management script for YMS deployment

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("stop", "start", "restart", "status")]
    [string]$Action,
    
    [string[]]$AppPools = @("Yms", "Ymsapi"),
    [string[]]$Sites = @("Yms", "Ymsapi"),
    [int]$TimeoutSeconds = 30
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

function Get-AppPoolStatus {
    param([string]$Name)
    try {
        $status = C:\Windows\System32\inetsrv\appcmd.exe list apppool $Name /text:state 2>$null
        return $status
    } catch {
        return $null
    }
}

function Get-SiteStatus {
    param([string]$Name)
    try {
        $status = C:\Windows\System32\inetsrv\appcmd.exe list site $Name /text:state 2>$null
        return $status
    } catch {
        return $null
    }
}

function Ensure-AppPool {
    param([string]$Name)
    $status = Get-AppPoolStatus $Name
    if (-not $status) {
        Write-Log "Creating app pool: $Name"
        C:\Windows\System32\inetsrv\appcmd.exe add apppool /name:$Name
        # Set .NET version and other properties
        C:\Windows\System32\inetsrv\appcmd.exe set apppool $Name /managedRuntimeVersion:"v4.0"
        C:\Windows\System32\inetsrv\appcmd.exe set apppool $Name /processModel.identityType:ApplicationPoolIdentity
    } else {
        Write-Log "App pool $Name already exists with status: $status"
    }
}

function Stop-AppPoolSafely {
    param([string]$Name)
    $status = Get-AppPoolStatus $Name
    if ($status -eq "Started") {
        Write-Log "Stopping app pool: $Name"
        C:\Windows\System32\inetsrv\appcmd.exe stop apppool $Name
        
        # Wait for it to stop with timeout
        $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
        do {
            Start-Sleep -Seconds 1
            $status = Get-AppPoolStatus $Name
            if ((Get-Date) -gt $timeout) {
                Write-Log "Timeout waiting for app pool $Name to stop" "WARNING"
                break
            }
        } while ($status -eq "Stopping")
        
        Write-Log "App pool $Name final status: $status"
    } else {
        Write-Log "App pool $Name is already stopped or doesn't exist (status: $status)"
    }
}

function Start-AppPoolSafely {
    param([string]$Name)
    $status = Get-AppPoolStatus $Name
    if ($status -ne "Started") {
        Write-Log "Starting app pool: $Name"
        C:\Windows\System32\inetsrv\appcmd.exe start apppool $Name
        
        # Wait for it to start with timeout
        $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
        do {
            Start-Sleep -Seconds 1
            $status = Get-AppPoolStatus $Name
            if ((Get-Date) -gt $timeout) {
                Write-Log "Timeout waiting for app pool $Name to start" "WARNING"
                break
            }
        } while ($status -eq "Starting")
        
        Write-Log "App pool $Name final status: $status"
    } else {
        Write-Log "App pool $Name is already started"
    }
}

function Stop-SiteSafely {
    param([string]$Name)
    $status = Get-SiteStatus $Name
    if ($status -eq "Started") {
        Write-Log "Stopping site: $Name"
        C:\Windows\System32\inetsrv\appcmd.exe stop site $Name
        Write-Log "Site $Name stopped"
    } else {
        Write-Log "Site $Name is already stopped or doesn't exist (status: $status)"
    }
}

function Start-SiteSafely {
    param([string]$Name)
    $status = Get-SiteStatus $Name
    if ($status -ne "Started") {
        Write-Log "Starting site: $Name"
        C:\Windows\System32\inetsrv\appcmd.exe start site $Name
        Write-Log "Site $Name started"
    } else {
        Write-Log "Site $Name is already started"
    }
}

function Show-Status {
    Write-Log "=== IIS Status Report ==="
    foreach ($pool in $AppPools) {
        $status = Get-AppPoolStatus $pool
        Write-Log "App Pool '$pool': $status"
    }
    foreach ($site in $Sites) {
        $status = Get-SiteStatus $site
        Write-Log "Site '$site': $status"
    }
}

# Main execution
Write-Log "Starting IIS management action: $Action"

switch ($Action) {
    "stop" {
        # Ensure app pools exist first
        foreach ($pool in $AppPools) {
            Ensure-AppPool $pool
        }
        
        # Stop sites first, then app pools
        foreach ($site in $Sites) {
            Stop-SiteSafely $site
        }
        foreach ($pool in $AppPools) {
            Stop-AppPoolSafely $pool
        }
    }
    "start" {
        # Ensure app pools exist first
        foreach ($pool in $AppPools) {
            Ensure-AppPool $pool
        }
        
        # Start app pools first, then sites
        foreach ($pool in $AppPools) {
            Start-AppPoolSafely $pool
        }
        foreach ($site in $Sites) {
            Start-SiteSafely $site
        }
    }
    "restart" {
        # Stop everything first
        foreach ($site in $Sites) {
            Stop-SiteSafely $site
        }
        foreach ($pool in $AppPools) {
            Stop-AppPoolSafely $pool
        }
        
        # Brief pause
        Start-Sleep -Seconds 2
        
        # Start everything
        foreach ($pool in $AppPools) {
            Start-AppPoolSafely $pool
        }
        foreach ($site in $Sites) {
            Start-SiteSafely $site
        }
    }
    "status" {
        Show-Status
    }
}

Write-Log "IIS management action completed: $Action"

﻿using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class StopService(IRepository<Stop> repository) : ApplicationService, IStopService
{
    public async Task<StopDto> CreateAsync(StopDto input)
    {
        var driver = ObjectMapper.Map<StopDto, Stop>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Stop, StopDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.StopId == id);
    }

    public async Task<StopDto> GetAsync(int id)
    {
        var queryable = await repository.GetQueryableAsync();
        var stop = await queryable.Where(x => x.StopId == id)
                                  .Include(x => x.StopEquipment)
                                  .FirstOrDefaultAsync();

        return stop is null ? throw new EntityNotFoundException() :  ObjectMapper.Map<Stop, StopDto>(stop);
    }

    public async Task<PagedResultDto<StopDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Include(x => x.StopEquipment)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .AsSingleQuery();

        var orders = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<StopDto>(
            totalCount,
            ObjectMapper.Map<List<Stop>, List<StopDto>>(orders)
        );
    }

    public Task<StopDto> UpdateAsync(int id, StopDto input)
    {
        throw new NotImplementedException();
    }
}
﻿using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using UniversalPortal.Data;
using Volo.Abp.DependencyInjection;

namespace UniversalPortal.EntityFrameworkCore;

public class EntityFrameworkCoreUniversalPortalDbSchemaMigrator(IServiceProvider serviceProvider)
        : IUniversalPortalDbSchemaMigrator, ITransientDependency
{
    public async Task MigrateAsync()
    {
        /* We intentionally resolving the UniversalPortalDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await serviceProvider
            .GetRequiredService<UniversalPortalDbContext>()
            .Database
            .MigrateAsync();
    }
}

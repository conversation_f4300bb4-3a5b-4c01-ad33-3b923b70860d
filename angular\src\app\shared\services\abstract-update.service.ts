import { AuditedEntityDto } from '@abp/ng.core';
import { Injectable, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { UpdateDto } from '../dtos/update.model';
import { LocalRecord } from '../../data/local-record';

@Injectable({
  providedIn: 'root'
})
export abstract class AbstractUpdateService<D extends AuditedEntityDto<string>> {

  public abstract entityUpdate$: Subject<UpdateDto<D>>;
}

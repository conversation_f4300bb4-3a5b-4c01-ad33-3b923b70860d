{"App": {"SelfUrl": "https://controltowerapi.ulhdev.com", "AngularUrl": "https://controltower.ulhdev.com", "CorsOrigins": "https://controltower.ulhdev.com,https://controltowerapi.ulhdev.com", "RedirectAllowedUrls": "https://controltower.ulhdev.com", "DisablePII": true, "HealthCheckUrl": "/health-status"}, "ConnectionStrings": {"Default": "Server=dv-atlassql;Database=ControlTower;Trusted_Connection=True;TrustServerCertificate=true", "UniversalPortalDb": "Server=dv-atlassql;Database=ControlTower;Trusted_Connection=True;Encrypt=False;"}, "AuthServer": {"Authority": "https://controltowerapi.ulhdev.com", "RequireHttpsMetadata": true, "SwaggerClientId": "UniversalPortal_Swagger", "CertificatePassPhrase": "b80e8beb-ab94-4986-a55e-70c8b0f6095f"}, "StringEncryption": {"DefaultPassPhrase": "WxSVqetBp4V1T5OJ"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}}
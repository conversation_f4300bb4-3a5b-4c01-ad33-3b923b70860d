﻿using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class EquipmentService(IRepository<Equipment> repository) : ApplicationService, IEquipmentService
{
    public async Task<EquipmentDto> CreateAsync(EquipmentDto input)
    {
        var driver = ObjectMapper.Map<EquipmentDto, Equipment>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Equipment, EquipmentDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.EquipmentId == id);
    }

    public async Task<EquipmentDto> GetAsync(int id)
    {
        var queryable = await repository.GetQueryableAsync();
        var equipment = await queryable.Where(x => x.EquipmentId == id)
                                       .Include(x => x.StopEquipment)
                                       .Include(x => x.EquipmentGeolocations)
                                       .FirstOrDefaultAsync();

        return equipment is null ? throw new EntityNotFoundException() : ObjectMapper.Map<Equipment, EquipmentDto>(equipment);
    }

    public async Task<PagedResultDto<EquipmentDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Include(x => x.StopEquipment)
            .Include(x => x.EquipmentGeolocations)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .AsSingleQuery();

        var equipment = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<EquipmentDto>(
            totalCount,
            ObjectMapper.Map<List<Equipment>, List<EquipmentDto>>(equipment)
        );
    }

    public Task<EquipmentDto> UpdateAsync(int id, EquipmentDto input)
    {
        throw new NotImplementedException();
    }
}
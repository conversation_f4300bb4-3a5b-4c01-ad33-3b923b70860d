﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class AddFileNameToAttachments : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FileName",
                schema: "ControlTower",
                table: "Attachments",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>N<PERSON>",
                schema: "ControlTower",
                table: "Attachments");
        }
    }
}

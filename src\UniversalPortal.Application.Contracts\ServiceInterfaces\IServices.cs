﻿using System.Collections.Generic;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.DTOs.Queries;
using UniversalPortal.DTOs.SemanticData;

using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace UniversalPortal.ServiceInterfaces;

public interface IOrderService :
    ICrudAppService<OrderDto, int, PagedAndSortedResultRequestDto, OrderDto>
{
}

public interface IDriverService :
    ICrudAppService<DriverDto, int, PagedAndSortedResultRequestDto, DriverDto>
{
}

public interface IEquipmentService :
    ICrudAppService<EquipmentDto, int, PagedAndSortedResultRequestDto, EquipmentDto>
{
}

public interface IEquipmentTypeService :
    ICrudAppService<EquipmentTypeDto, int, PagedAndSortedResultRequestDto, EquipmentTypeDto>
{
}

public interface IGeolocationService :
    ICrudAppService<GeolocationDto, int, PagedAndSortedResultRequestDto, GeolocationDto>
{
}

public interface ILegService :
    ICrudAppService<LegDto, int, PagedAndSortedResultRequestDto, LegDto>
{
}

public interface INoteService :
    ICrudAppService<NoteDto, int, PagedAndSortedResultRequestDto, NoteDto>
{
    Task<PagedResultDto<NoteDto>> GetFilteredListAsync(FilteredPagedAndSortedResultRequestDto input);
}

public interface IAttachmentService :
    ICrudAppService<AttachmentDto, int, PagedAndSortedResultRequestDto, AttachmentDto>
{
}

public interface IPowerUnitService :
    ICrudAppService<PowerUnitDto, int, PagedAndSortedResultRequestDto, PowerUnitDto>
{
}

public interface IStopService :
    ICrudAppService<StopDto, int, PagedAndSortedResultRequestDto, StopDto>
{
}

public interface IAddressService :
    ICrudAppService<AddressDto, int, PagedAndSortedResultRequestDto, AddressDto>
{
}

public interface IContactService :
    ICrudAppService<ContactDto, int, PagedAndSortedResultRequestDto, ContactDto>
{
}

public interface IGeofenceService :
    ICrudAppService<GeofenceDto, int, PagedAndSortedResultRequestDto, GeofenceDto>
{
}

public interface ISourceSystemService :
    ICrudAppService<SourceSystemDto, int, PagedAndSortedResultRequestDto, SourceSystemDto>
{
}

public interface IReportingService : IApplicationService, IRemoteService
{
    public Task<ICollection<BinnedQuantityDto>> GetAverageDwellTimeByLocationAsync(LocationReportQueryDto input);
    public Task<ICollection<BinnedQuantityDto>> GetTrailerCountByLocationAsync(LocationReportQueryDto input);
    public Task<ICollection<BinnedQuantityDto>> GetInactiveAssetsByLocationAsync(LocationReportQueryDto input, int inactiveHours);
    public Task<ICollection<BinnedQuantityDto>> GetAverageHoursSinceMoveByLocationAsync(LocationReportQueryDto input);
}

public interface IMapService : IApplicationService, IRemoteService
{
    public Task<MapFeaturesDto> GetMapFeaturesByOrder(int id);
}
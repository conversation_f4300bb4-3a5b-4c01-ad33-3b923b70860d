﻿using System;

namespace UniversalPortal.DTOs.Queries
{
    /// <summary>
    /// Encapsulates parameters used for querying data for a location report.
    /// </summary>
    [Serializable]
    public sealed class LocationReportQueryDto
    {
        public DateTime RangeBegin { get; set; } = default;
        public DateTime RangeEnd { get; set; } = default;

        public int[] LocationIds { get; set; } = [];
    }
}

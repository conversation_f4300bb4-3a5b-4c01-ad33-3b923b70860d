﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class EquipmentPowerUnitTableUpdates : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "ExpirationDate",
                schema: "ControlTower",
                table: "PowerUnits",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "PowerUnitCode",
                schema: "ControlTower",
                table: "PowerUnits",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "ExpirationDate",
                schema: "ControlTower",
                table: "Equipment",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "<PERSON>",
                schema: "ControlTower",
                table: "Equipment",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExpirationDate",
                schema: "ControlTower",
                table: "PowerUnits");

            migrationBuilder.DropColumn(
                name: "PowerUnitCode",
                schema: "ControlTower",
                table: "PowerUnits");

            migrationBuilder.DropColumn(
                name: "ExpirationDate",
                schema: "ControlTower",
                table: "Equipment");

            migrationBuilder.DropColumn(
                name: "Vin",
                schema: "ControlTower",
                table: "Equipment");
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Order : TenantAuditedEntity
{
    public Order()
    {
        OrderContacts = [];
        OrderNotes = [];
        Stops = [];
    }

    [Key]
    public int OrderId { get; set; }
    public int OrderNumber { get; set; }
    public int MoveNumber { get; set; }
    public DateTime StartDate { get; set; }
    public string? Route {  get; set; }
    public int Miles { get; set; }
    public string? CompanyId { get; set; }
    public string? CompanyName { get; set; }
    [InverseProperty(nameof(OrderContact.Order))]
    public ICollection<OrderContact> OrderContacts { get; set; }
    [InverseProperty(nameof(OrderNote.Order))]
    public ICollection<OrderNote> OrderNotes { get; set; }
    [InverseProperty(nameof(Stop.Order))]
    public ICollection<Stop> Stops { get; set; }

    public override object[] GetKeys()
    {
        return [OrderId];
    }
}
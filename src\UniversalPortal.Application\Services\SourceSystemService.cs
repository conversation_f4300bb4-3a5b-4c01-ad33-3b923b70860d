﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class SourceSystemService(IRepository<SourceSystem> repository) : ApplicationService, ISourceSystemService
{
    public async Task<SourceSystemDto> CreateAsync(SourceSystemDto input)
    {
        var driver = ObjectMapper.Map<SourceSystemDto, SourceSystem>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<SourceSystem, SourceSystemDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.SourceSystemId == id);
    }

    public async Task<SourceSystemDto> GetAsync(int id)
    {
        var driver = await repository.GetAsync(x => x.SourceSystemId == id);
        return ObjectMapper.Map<SourceSystem, SourceSystemDto>(driver);
    }

    public async Task<PagedResultDto<SourceSystemDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var driver = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<SourceSystemDto>(
            totalCount,
            ObjectMapper.Map<List<SourceSystem>, List<SourceSystemDto>>(driver)
        );
    }

    public Task<SourceSystemDto> UpdateAsync(int id, SourceSystemDto input)
    {
        throw new NotImplementedException();
    }
}
﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.DirectModels;
using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

public class StopEquipment : AuditedEntity
{
    [Key, Column(Order = 0)]
    public int StopId { get; set; }
    [Key, Column(Order = 1)]
    public int EquipmentId { get; set; }
    [InverseProperty(nameof(DirectModels.Stop.StopEquipment))]
    public Stop? Stop { get; set; }
    [InverseProperty(nameof(DirectModels.Equipment.StopEquipment))]
    public Equipment? Equipment { get; set; }

    public override object[] GetKeys()
    {
        return [StopId, EquipmentId];
    }
}
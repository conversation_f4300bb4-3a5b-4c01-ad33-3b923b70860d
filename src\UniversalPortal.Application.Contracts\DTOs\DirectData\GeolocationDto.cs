﻿using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class GeolocationDto : AuditedEntityDto
{
    public GeolocationDto()
    {
        EquipmentIds = [];
    }

    public int GeolocationId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime GeolocationDate { get; set; }
    public ICollection<int> EquipmentIds { get; set; }
}
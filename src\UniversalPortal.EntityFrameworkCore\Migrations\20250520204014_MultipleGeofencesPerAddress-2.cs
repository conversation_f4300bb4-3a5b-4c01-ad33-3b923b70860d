﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class MultipleGeofencesPerAddress2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Stops",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "PowerUnits",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Orders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Notes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Legs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Geolocations",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Geofences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "EquipmentTypes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Equipment",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Drivers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Contacts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Addresses",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Addresses_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Addresses",
                principalColumn: "AddressId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Contacts_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Contacts",
                principalColumn: "ContactId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Drivers_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Drivers",
                principalColumn: "DriverId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_EquipmentTypes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "EquipmentTypes",
                principalColumn: "EquipmentTypeId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Equipment_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Equipment",
                principalColumn: "EquipmentId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Geofences_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Geofences",
                principalColumn: "GeofenceId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Geolocations_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Legs_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Legs",
                principalColumn: "LegId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Notes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Notes",
                principalColumn: "NoteId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Orders_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Orders",
                principalColumn: "OrderId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_PowerUnits_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "PowerUnits",
                principalColumn: "PowerUnitId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Stops_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Stops",
                principalColumn: "StopId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}

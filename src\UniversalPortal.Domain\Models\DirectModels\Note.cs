﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Note : SoftDeleteEntity
{
    public Note()
    {
        OrderNotes = [];
        StopNotes = [];
        Attachments = [];
    }

    [Key]
    public int NoteId { get; set; }
    public string? Content {  get; set; }
    [InverseProperty(nameof(OrderNote.Note))]
    public ICollection<OrderNote> OrderNotes { get; set; }
    [InverseProperty(nameof(StopNote.Note))]
    public ICollection<StopNote> StopNotes { get; set; }
    [InverseProperty(nameof(Attachment.Note))]
    public ICollection<Attachment> Attachments { get; set; }

    public override object[] GetKeys()
    {
        return [NoteId];
    }
}
﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Attachment : SoftDeleteEntity
{
    public Attachment()
    {
        Content = [];
        Note = new ();
    }

    [Key]
    public int AttachmentId { get; set; }
    public int NoteId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public byte[] Content {  get; set; }
    [InverseProperty(nameof(DirectModels.Note.Attachments))]
    public Note Note { get; set; }

    public override object[] GetKeys()
    {
        return [AttachmentId];
    }
}
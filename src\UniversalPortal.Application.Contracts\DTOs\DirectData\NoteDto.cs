﻿using System.Collections.Generic;
using UniversalPortal.DTOs.LinkedData;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class NoteDto : AuditedEntityDto
{
    public NoteDto()
    {
        OrderNotes = [];
        StopNotes = [];
        Attachments = [];
    }

    public int NoteId { get; set; }
    public string? Content {  get; set; }

    public ICollection<OrderNoteDto> OrderNotes { get; set; }
    public ICollection<StopNoteDto> StopNotes { get; set; }
    public ICollection<AttachmentDto> Attachments { get; set; }

}
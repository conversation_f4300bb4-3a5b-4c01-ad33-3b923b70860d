﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class AddMoveNumberReferences : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "MoveId",
                schema: "ControlTower",
                table: "Legs",
                newName: "MoveNumber");

            migrationBuilder.AddColumn<int>(
                name: "MoveNumber",
                schema: "ControlTower",
                table: "Orders",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MoveNumber",
                schema: "ControlTower",
                table: "Orders");

            migrationBuilder.RenameColumn(
                name: "MoveNumber",
                schema: "ControlTower",
                table: "Legs",
                newName: "MoveId");
        }
    }
}

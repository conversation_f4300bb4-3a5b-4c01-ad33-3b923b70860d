﻿using System.Collections.Generic;
using UniversalPortal.DTOs.LinkedData;
namespace UniversalPortal.DTOs.DirectData;

public class AddressDto
{
    public AddressDto()
    {
        AddressContacts = [];
        Geofences = [];
        GeofenceIds = [];
    }

    public int AddressId { get; set; }
    public string AddressLine1 { get; set; } = string.Empty;
    public string AddressLine2 { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string TimeZone { get; set; } = string.Empty;
    public string Zip { get; set; } = string.Empty;
    public ICollection<GeofenceDto> Geofences { get; set; }
    public ICollection<AddressContactDto> AddressContacts { get; set; }
    public ICollection<int> GeofenceIds { get; set; }
}
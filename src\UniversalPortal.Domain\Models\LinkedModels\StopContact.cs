﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.DirectModels;
using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

public class StopContact : AuditedEntity
{
    [Key, Column(Order = 0)]
    public int StopId { get; set; }
    [Key, Column(Order = 1)]
    public int ContactId { get; set; }
    [InverseProperty(nameof(Stop.StopContacts))]
    public Stop? Stop { get; set; }
    [InverseProperty(nameof(Contact.StopContacts))]
    public Contact? Contact { get; set; }

    public override object[] GetKeys()
    {
        return [StopId, ContactId];
    }
}
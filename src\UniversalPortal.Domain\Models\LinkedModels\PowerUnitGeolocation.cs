﻿using Microsoft.EntityFrameworkCore;

using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.DirectModels;
using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

[PrimaryKey(nameof(PowerUnitId),nameof(GeolocationId))]
public class PowerUnitGeolocation: AuditedEntity
{
    public int PowerUnitId { get; set; }
    public int GeolocationId { get; set; }
    [InverseProperty(nameof(DirectModels.PowerUnit.PowerUnitGeolocations))]
    public PowerUnit? PowerUnit { get; set; }
    [InverseProperty(nameof(Geolocation.PowerUnitGeolocations))]
    public Geolocation? Geolocations { get; set; }

    public override object[] GetKeys()
    {
        return [PowerUnitId, GeolocationId];
    }
}
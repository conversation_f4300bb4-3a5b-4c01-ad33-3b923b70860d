﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class AddGeofenceAndSourceSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AddressId",
                schema: "ControlTower",
                table: "Stops",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Stops",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "PowerUnits",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Orders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Notes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Legs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Geolocations",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "EquipmentTypes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystem",
                schema: "ControlTower",
                table: "Equipment",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Equipment",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Drivers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Contacts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "Geofence",
                schema: "dbo",
                columns: table => new
                {
                    GeofenceId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GeofenceCoordinates = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Centroid = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    SourceSystemId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Geofence", x => x.GeofenceId);
                });

            migrationBuilder.CreateTable(
                name: "Addresses",
                schema: "ControlTower",
                columns: table => new
                {
                    AddressId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AddressLine1 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AddressLine2 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    GeofenceId = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    SourceSystemId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Addresses", x => x.AddressId);
                    table.ForeignKey(
                        name: "FK_Addresses_Geofence_GeofenceId",
                        column: x => x.GeofenceId,
                        principalSchema: "dbo",
                        principalTable: "Geofence",
                        principalColumn: "GeofenceId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SourceSystems",
                schema: "ControlTower",
                columns: table => new
                {
                    SourceSystemId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SourceSystems", x => x.SourceSystemId);
                    table.ForeignKey(
                        name: "FK_SourceSystems_Addresses_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Addresses",
                        principalColumn: "AddressId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Contacts_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Contacts",
                        principalColumn: "ContactId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Drivers_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Drivers",
                        principalColumn: "DriverId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_EquipmentTypes_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "EquipmentTypes",
                        principalColumn: "EquipmentTypeId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Equipment_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Equipment",
                        principalColumn: "EquipmentId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Geofence_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "dbo",
                        principalTable: "Geofence",
                        principalColumn: "GeofenceId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Geolocations_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Geolocations",
                        principalColumn: "GeolocationId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Legs_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Legs",
                        principalColumn: "LegId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Notes_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Notes",
                        principalColumn: "NoteId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Orders_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Orders",
                        principalColumn: "OrderId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_PowerUnits_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "PowerUnits",
                        principalColumn: "PowerUnitId");
                    table.ForeignKey(
                        name: "FK_SourceSystems_Stops_SourceSystemId",
                        column: x => x.SourceSystemId,
                        principalSchema: "ControlTower",
                        principalTable: "Stops",
                        principalColumn: "StopId");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Stops_AddressId",
                schema: "ControlTower",
                table: "Stops",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Addresses_GeofenceId",
                schema: "ControlTower",
                table: "Addresses",
                column: "GeofenceId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Stops_Addresses_AddressId",
                schema: "ControlTower",
                table: "Stops",
                column: "AddressId",
                principalSchema: "ControlTower",
                principalTable: "Addresses",
                principalColumn: "AddressId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Stops_Addresses_AddressId",
                schema: "ControlTower",
                table: "Stops");

            migrationBuilder.DropTable(
                name: "SourceSystems",
                schema: "ControlTower");

            migrationBuilder.DropTable(
                name: "Addresses",
                schema: "ControlTower");

            migrationBuilder.DropTable(
                name: "Geofence",
                schema: "dbo");

            migrationBuilder.DropIndex(
                name: "IX_Stops_AddressId",
                schema: "ControlTower",
                table: "Stops");

            migrationBuilder.DropColumn(
                name: "AddressId",
                schema: "ControlTower",
                table: "Stops");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Stops");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "PowerUnits");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Notes");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "EquipmentTypes");

            migrationBuilder.DropColumn(
                name: "SourceSystem",
                schema: "ControlTower",
                table: "Equipment");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Equipment");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Drivers");

            migrationBuilder.DropColumn(
                name: "SourceSystemId",
                schema: "ControlTower",
                table: "Contacts");
        }
    }
}

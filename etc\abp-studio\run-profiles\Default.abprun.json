﻿{
  "applications": {
    "UniversalPortal.HttpApi.Host": {
      "type": "dotnet-project",
      "launchUrl": "https://localhost:44369",
      "path": "../../../src/UniversalPortal.HttpApi.Host/UniversalPortal.HttpApi.Host.csproj",
      "kubernetesService": ".*-httpapihost$",
      "execution": {
        "order": 4
      }
    },
    "UniversalPortal.Angular": {
      "type": "cli",
      "workingDirectory": "../../../angular",
      "startCommand": "./start.ps1",
      "launchUrl": "http://localhost:4200",
      "kubernetesService": ".*-angular$",
      "execution": {
        "order": 2
      }    
    }
  }
}
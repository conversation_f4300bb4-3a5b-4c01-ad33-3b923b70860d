﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Address : TenantAuditedEntity
{
    public Address()
    {
        Stops = [];
        Geofences = [];
        AddressContacts = [];
    }

    [Key]
    public int AddressId { get; set; }
    public string AddressLine1 { get; set; } = string.Empty;
    public string AddressLine2 { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string TimeZone { get; set; } = string.Empty;
    public string Zip { get; set; } = string.Empty;
    [InverseProperty(nameof(Geofence.Address))]
    public ICollection<Geofence> Geofences { get; set; }
    [InverseProperty(nameof(Stop.Address))]
    public ICollection<Stop> Stops { get; set; }
    [InverseProperty(nameof(AddressContact.Address))]
    public ICollection<AddressContact> AddressContacts { get; set; }

    public override object[] GetKeys()
    {
        return [AddressId];
    }
}
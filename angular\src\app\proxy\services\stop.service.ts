import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { StopDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class StopService {
  apiName = 'Default';
  

  create = (input: StopDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StopDto>({
      method: 'POST',
      url: '/api/app/stop',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/stop/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StopDto>({
      method: 'GET',
      url: `/api/app/stop/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<StopDto>>({
      method: 'GET',
      url: '/api/app/stop',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: StopDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StopDto>({
      method: 'PUT',
      url: `/api/app/stop/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

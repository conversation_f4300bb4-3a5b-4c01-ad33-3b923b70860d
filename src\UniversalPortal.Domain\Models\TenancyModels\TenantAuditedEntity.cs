﻿using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.TenancyModels;

public abstract class TenantAuditedEntity : AuditedEntity
{
    // will come back to this ... causing issues and sourcesystem is unused currently.
    //public int SourceSystemId { get; set; }
    //[ForeignKey(nameof(SourceSystemId))]
    //public ICollection<SourceSystem> SourceSystems { get; set; } = new HashSet<SourceSystem>();
}
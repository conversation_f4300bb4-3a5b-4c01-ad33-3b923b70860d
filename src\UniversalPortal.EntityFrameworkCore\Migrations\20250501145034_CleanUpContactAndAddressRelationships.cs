﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class CleanUpContactAndAddressRelationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Centroid",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.AddColumn<int>(
                name: "AddressId",
                schema: "ControlTower",
                table: "Geofences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "GeolocationId",
                schema: "ControlTower",
                table: "Geofences",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "AddressId",
                schema: "ControlTower",
                table: "Contacts",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Couh<PERSON>",
                schema: "ControlTower",
                table: "Addresses",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DisplayName",
                schema: "ControlTower",
                table: "Addresses",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "State",
                schema: "ControlTower",
                table: "Addresses",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "TimeZone",
                schema: "ControlTower",
                table: "Addresses",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Zip",
                schema: "ControlTower",
                table: "Addresses",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_Geofences_GeolocationId",
                schema: "ControlTower",
                table: "Geofences",
                column: "GeolocationId");

            migrationBuilder.CreateIndex(
                name: "IX_Contacts_AddressId",
                schema: "ControlTower",
                table: "Contacts",
                column: "AddressId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contacts_Addresses_AddressId",
                schema: "ControlTower",
                table: "Contacts",
                column: "AddressId",
                principalSchema: "ControlTower",
                principalTable: "Addresses",
                principalColumn: "AddressId");

            migrationBuilder.AddForeignKey(
                name: "FK_Geofences_Geolocations_GeolocationId",
                schema: "ControlTower",
                table: "Geofences",
                column: "GeolocationId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contacts_Addresses_AddressId",
                schema: "ControlTower",
                table: "Contacts");

            migrationBuilder.DropForeignKey(
                name: "FK_Geofences_Geolocations_GeolocationId",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.DropIndex(
                name: "IX_Geofences_GeolocationId",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.DropIndex(
                name: "IX_Contacts_AddressId",
                schema: "ControlTower",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "AddressId",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.DropColumn(
                name: "GeolocationId",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.DropColumn(
                name: "AddressId",
                schema: "ControlTower",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "Couhtry",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.DropColumn(
                name: "DisplayName",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.DropColumn(
                name: "State",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.DropColumn(
                name: "TimeZone",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.DropColumn(
                name: "Zip",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.AddColumn<string>(
                name: "Centroid",
                schema: "ControlTower",
                table: "Geofences",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}

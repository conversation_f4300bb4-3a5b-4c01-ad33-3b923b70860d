{"id": "091f4f0f-fc15-4456-84d0-b5c8dd8707ef", "template": "app", "versions": {"AbpFramework": "9.0.4", "AbpStudio": "0.9.23", "TargetDotnetFramework": "net9.0"}, "modules": {"UniversalPortal": {"path": "UniversalPortal.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/abp-studio/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": ["^/metrics$"]}}, "creatingStudioConfiguration": {"template": "app", "createdAbpStudioVersion": "0.9.23", "tiered": "false", "runInstallLibs": "true", "useLocalReferences": "false", "multiTenancy": "true", "includeTests": "true", "kubernetesConfiguration": "false", "uiFramework": "angular", "mobileFramework": "none", "distributedEventBus": "none", "databaseProvider": "ef", "runDbMigrator": "true", "databaseManagementSystem": "sqlserver", "separateTenantSchema": "false", "createInitialMigration": "true", "theme": "leptonx-lite", "themeStyle": "", "progressiveWebApp": "false", "runProgressiveWebAppSupport": "false", "publicWebsite": "false", "socialLogin": ""}}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class PowerUnitService(IRepository<PowerUnit> repository) : ApplicationService, IPowerUnitService
{
    public async Task<PowerUnitDto> CreateAsync(PowerUnitDto input)
    {
        var driver = ObjectMapper.Map<PowerUnitDto, PowerUnit>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<PowerUnit, PowerUnitDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.PowerUnitId == id);
    }

    public async Task<PowerUnitDto> GetAsync(int id)
    {
        var powerUnit = await repository.GetAsync(x => x.PowerUnitId == id);
        return ObjectMapper.Map<PowerUnit, PowerUnitDto>(powerUnit);
    }

    public async Task<PagedResultDto<PowerUnitDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var powerUnits = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<PowerUnitDto>(
            totalCount,
            ObjectMapper.Map<List<PowerUnit>, List<PowerUnitDto>>(powerUnits)
        );
    }

    public Task<PowerUnitDto> UpdateAsync(int id, PowerUnitDto input)
    {
        throw new NotImplementedException();
    }
}
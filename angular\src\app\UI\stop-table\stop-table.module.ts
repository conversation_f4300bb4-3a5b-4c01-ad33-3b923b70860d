import { NgModule } from '@angular/core';
import { PageModule } from '@abp/ng.components/page';
import { SharedModule } from '../../shared/shared.module';
import { MatPaginatorIntl, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { ThemeLeptonXModule } from '@abp/ng.theme.lepton-x';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { StopTableComponent } from './stop-table.component';
import { IntlPaginatorComponent } from '../intl-paginator/intl-paginator.component';

@NgModule({
  declarations: [
    StopTableComponent
  ],
  exports: [
    StopTableComponent
  ],
  imports: [
    SharedModule,
    PageModule,
    MatPaginatorModule,
    MatTableModule,
    MatSortModule,
    ThemeLeptonXModule.forRoot(),
    MatIconModule,
    MatButtonModule,
    MatDividerModule
  ],
  providers: [
    {
      provide: MatPaginatorIntl,
      useClass: IntlPaginatorComponent
    }
  ]
})
export class StopTableModule { }

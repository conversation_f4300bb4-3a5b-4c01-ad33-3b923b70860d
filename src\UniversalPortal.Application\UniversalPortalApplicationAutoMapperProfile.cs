using System.Linq;
using AutoMapper;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.DTOs.LinkedData;
using UniversalPortal.DTOs.SemanticData;
using UniversalPortal.Models;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.SemanticModels;

using Volo.Abp.AutoMapper;

namespace UniversalPortal;

public class UniversalPortalApplicationAutoMapperProfile : Profile
{
    public UniversalPortalApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */

        CreateMap<AddressContact, AddressContactDto>();
        CreateMap<Address, AddressDto>();
        CreateMap<Contact, ContactDto>();
        CreateMap<Driver, DriverDto>();
        CreateMap<Equipment, EquipmentDto>()
            .ForMember(x => x.StopIds,
                              opt => opt.MapFrom(src => src.StopEquipment.Select(x => x.StopId)))
            .ForMember(x => x.GeolocationIds,
                              opt => opt.MapFrom(src => src.EquipmentGeolocations.Select(x => x.GeolocationId)))
            .Ignore(x => x.EquipmentGeolocations);

        CreateMap<EquipmentGeolocation, EquipmentGeolocationDto>();
        CreateMap<EquipmentType, EquipmentTypeDto>();
        CreateMap<Geofence, GeofenceDto>()
            .ForMember(x => x.Center,
                              opt => opt.MapFrom(src => new GeolocationCoordinate()
                              {
                                  Latitude = src.Geolocation!.Latitude,
                                  Longitude = src.Geolocation.Longitude
                              }));
        CreateMap<Geolocation, GeolocationDto>()
            .ForMember(x => x.EquipmentIds,
                              opt => opt.MapFrom(src => src.EquipmentGeolocations.Select(x => x.EquipmentId)));

        CreateMap<Leg, LegDto>();
        CreateMap<Note, NoteDto>();
        CreateMap<NoteDto, Note>();
        CreateMap<Attachment, AttachmentDto>()
            .Ignore(x => x.Note); // stop serilization errors because of circular references.

        CreateMap<AttachmentDto, Attachment>();
        CreateMap<Order, OrderDto>();
        CreateMap<OrderContact, OrderContactDto>();
        CreateMap<OrderNote, OrderNoteDto>()
            .Ignore(x => x.Note); // stop serilization errors because of circular references.

        CreateMap<OrderNoteDto, OrderNote>();
        CreateMap<PowerUnit, PowerUnitDto>();
        CreateMap<PowerUnitGeolocation, PowerUnitGeolocationDto>();
        CreateMap<SourceSystem, SourceSystemDto>();
        CreateMap<Stop, StopDto>()
            .ForMember(x => x.EquipmentIds,
                              opt => opt.MapFrom(src => src.StopEquipment.Select(x => x.EquipmentId)))
            .Ignore(x => x.StopEquipment);

        CreateMap<StopContact, StopContactDto>();
        CreateMap<StopEquipment, StopEquipmentDto>();
        CreateMap<StopNote, StopNoteDto>();
        CreateMap<StopNoteDto, StopNote>()
            .Ignore(x => x.Note); // stop serilization errors because of circular references.

        CreateMap<BinnedQuantity, BinnedQuantityDto>();
    }
}

﻿using Microsoft.EntityFrameworkCore;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class AttachmentService(IRepository<Attachment> repository) : ApplicationService, IAttachmentService
{
    public async Task<AttachmentDto> CreateAsync(AttachmentDto input)
    {
        var attachment = ObjectMapper.Map<AttachmentDto, Attachment>(input);
        var insertedEntity = await repository.InsertAsync(attachment);
        return ObjectMapper.Map<Attachment, AttachmentDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        var note = await repository.GetAsync(x => x.NoteId == id && x.IsDeleted == false);
        note.IsDeleted = true;
        _ = await repository.UpdateAsync(note);
    }

    public async Task<AttachmentDto> GetAsync(int id)
    {
        var note = await repository.GetAsync(x => x.NoteId == id);
        return ObjectMapper.Map<Attachment, AttachmentDto>(note);
    }

    public async Task<PagedResultDto<AttachmentDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Where(x => x.IsDeleted == false)
            .AsSingleQuery();

        var notes = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<AttachmentDto>(
            totalCount,
            ObjectMapper.Map<List<Attachment>, List<AttachmentDto>>(notes)
        );
    }

    public async Task<PagedResultDto<AttachmentDto>> GetFilteredListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Where(x => x.IsDeleted == false)
            .AsSingleQuery();

        var notes = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<AttachmentDto>(
            totalCount,
            ObjectMapper.Map<List<Attachment>, List<AttachmentDto>>(notes)
        );
    }

    public async Task<AttachmentDto> UpdateAsync(int id, AttachmentDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var originalAttachment = await repository.GetAsync(x => x.NoteId == id);
        originalAttachment.IsDeleted = true;
        originalAttachment.LastModificationTime = input.LastModificationTime;
        originalAttachment.LastModifierId = input.LastModifierId;
        // soft delete the original attachment
        var updatedResult = await repository.UpdateAsync(originalAttachment);

        // create new attachment
        var newAttachment = ObjectMapper.Map<AttachmentDto, Attachment>(input);
        var newDbAttachment = await repository.InsertAsync(newAttachment);
        return ObjectMapper.Map<Attachment, AttachmentDto>(newDbAttachment);
    }
}
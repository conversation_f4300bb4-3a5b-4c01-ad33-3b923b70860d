﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Geolocation : TenantAuditedEntity
{
    public Geolocation()
    {
        EquipmentGeolocations = [];
        PowerUnitGeolocations = [];
        Geofences = [];
    }

    public Geolocation(double lat, double lng)
    {
        EquipmentGeolocations = [];
        PowerUnitGeolocations = [];
        Geofences = [];
        Latitude = lat;
        Longitude = lng;
    }

    [Key]
    public int GeolocationId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime GeolocationDate { get; set; }
    [ForeignKey(nameof(GeolocationId))]
    [InverseProperty(nameof(EquipmentGeolocation.Geolocation))]
    public ICollection<EquipmentGeolocation> EquipmentGeolocations { get; set; }
    [ForeignKey(nameof(GeolocationId))]
    [InverseProperty(nameof(PowerUnitGeolocation.Geolocations))]
    public ICollection<PowerUnitGeolocation> PowerUnitGeolocations { get; set; }
    [InverseProperty(nameof(Geofence.Geolocation))]
    public ICollection<Geofence> Geofences { get; set; }

    [InverseProperty(nameof(Dwell.EntryGeolocation))]
    public Dwell? EntryDwell { get; set; }

    [InverseProperty(nameof(Dwell.DepartureGeolocation))]
    public Dwell? DepartureDwell { get; set; }


    public override object[] GetKeys()
    {
        return [GeolocationId];
    }
}
import { Environment } from '@abp/ng.core';

const baseUrl = 'http://localhost:4200';

const oAuthConfig = {
  issuer: 'https://localhost:44369/',
  redirectUri: baseUrl,
  clientId: 'UniversalPortal_App',
  responseType: 'code',
  scope: 'offline_access UniversalPortal',
  requireHttps: true,
};

export const environment = {
  production: true,
  debug: false,
  application: {
    baseUrl,
    name: 'UniversalPortal',
  },
  oAuthConfig,
  apis: {
    default: {
      url: 'https://localhost:44369',
      rootNamespace: 'UniversalPortal',
    },
    AbpAccountPublic: {
      url: oAuthConfig.issuer,
      rootNamespace: 'AbpAccountPublic',
    },
  },
  remoteEnv: {
    url: '/getEnvConfig',
    mergeStrategy: 'deepmerge'
  }
} as Environment;

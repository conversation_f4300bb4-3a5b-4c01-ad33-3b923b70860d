﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class ChangeEntityRoots : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Stops");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Stops");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "StopNotes");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "StopNotes");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "StopEquipment");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "StopEquipment");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "StopContacts");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "StopContacts");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "PowerUnits");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "PowerUnits");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "PowerUnitGeolocations");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "PowerUnitGeolocations");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "OrderNotes");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "OrderNotes");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "OrderContacts");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "OrderContacts");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Notes");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Notes");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Moves");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Moves");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "EquipmentTypes");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "EquipmentTypes");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "EquipmentGeolocations");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "EquipmentGeolocations");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Equipment");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Equipment");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Drivers");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Drivers");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Contacts");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Stops",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Stops",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "StopNotes",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "StopNotes",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "StopEquipment",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "StopEquipment",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "StopContacts",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "StopContacts",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "PowerUnits",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "PowerUnits",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Orders",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Orders",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "OrderNotes",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "OrderNotes",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "OrderContacts",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "OrderContacts",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Notes",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Notes",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Moves",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Moves",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Legs",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Legs",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Geolocations",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Geolocations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "EquipmentTypes",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "EquipmentTypes",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "EquipmentGeolocations",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "EquipmentGeolocations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Equipment",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Equipment",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Drivers",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Drivers",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                schema: "ControlTower",
                table: "Contacts",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExtraProperties",
                schema: "ControlTower",
                table: "Contacts",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}

﻿using System;
using System.Threading.Tasks;

using AtlasCommon.Infrastructure.Abstractions;
using AtlasCommon.Infrastructure.Attributes;
using AtlasCommon.Infrastructure.Enums;

using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

using UniversalPortal.DTOs;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Hubs;
using UniversalPortal.Models.DirectModels;

using Volo.Abp.Application.Services;

namespace UniversalPortal.SqlWatchers;

[SqlWatcher(ConnectionName = "UniversalPortalDb", NotificationType = SqlWatcherNotificationType.Insert | SqlWatcherNotificationType.Update, SchemaName = "ControlTower", TableName = "Stops")]
public class StopWatcher(ILogger<StopWatcher> logger, IHubContext<LiveUpdateHub, IHubClient> hubContext) : ApplicationService, ISqlWatcherHandler<Stop>
{
    public async Task HandleAsync(Stop? current, Stop? previous)
    {
        try
        {
            if(current is null)
                return;

            ChangeDto<StopDto> message = new(
                previous is null ? null : ObjectMapper.Map<Stop, StopDto>(previous),
                ObjectMapper.Map<Stop, StopDto>(current));

            await hubContext.Clients.Group(nameof(Stop)).Publish(message, nameof(Stop));
        }
        catch(Exception ex)
        {
            logger.LogError(ex, "Failed to publish stop update to the consumers. \r\n\r\n{currentStop}", current);
            throw;
        }
    }
}
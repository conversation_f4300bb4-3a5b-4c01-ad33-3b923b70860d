﻿using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.DTOs.Queries;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class NoteService(IRepository<Note> repository) : ApplicationService, INoteService
{
    public async Task<NoteDto> CreateAsync(NoteDto input)
    {
        var note = ObjectMapper.Map<NoteDto, Note>(input);
        var insertedEntity = await repository.InsertAsync(note);
        return ObjectMapper.Map<Note, NoteDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        var note = await repository.GetAsync(x => x.NoteId == id && x.IsDeleted == false);
        note.IsDeleted = true;
        _ = await repository.UpdateAsync(note);
    }

    public async Task<NoteDto> GetAsync(int id)
    {
        var note = await repository.GetAsync(x => x.NoteId == id);
        return ObjectMapper.Map<Note, NoteDto>(note);
    }

    public async Task<PagedResultDto<NoteDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Include(x => x.Attachments)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Where(x => x.IsDeleted == false)
            .AsSingleQuery();

        var notes = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<NoteDto>(
            totalCount,
            ObjectMapper.Map<List<Note>, List<NoteDto>>(notes)
        );
    }

    public async Task<PagedResultDto<NoteDto>> GetFilteredListAsync(FilteredPagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Include(x => x.Attachments)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Where(x => x.IsDeleted == false)
            .AsSingleQuery();

        Expression<Func<Note, bool>>? predicate = null;

        if(input.PredicateField is not null && input.PredicateValue is not null)
            predicate = ExpressionBuilder.BuildPredicate<Note>(input.PredicateField!, input.PredicateOperator, input.PredicateValue!);

        var notes = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<NoteDto>(
            totalCount,
            ObjectMapper.Map<List<Note>, List<NoteDto>>(notes)
        );
    }

    public async Task<NoteDto> UpdateAsync(int id, NoteDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var note = await repository.GetAsync(x => x.NoteId == id);
        note.Content = input.Content;
        note.LastModificationTime = input.LastModificationTime;
        note.LastModifierId = input.LastModifierId;
        var updatedResult = await repository.UpdateAsync(note);
        return ObjectMapper.Map<Note, NoteDto>(updatedResult);
    }
}
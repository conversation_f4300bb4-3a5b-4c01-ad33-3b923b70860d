﻿using System;
using System.Collections.Generic;
using UniversalPortal.DTOs.LinkedData;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class PowerUnitDto : AuditedEntityDto
{
    public PowerUnitDto()
    {
        PowerUnitGeolocations = [];
    }

    public int PowerUnitId { get; set; }
    public string? Vin {  get; set; }
    public string? PowerUnitCode { get; set; }
    public DateTime ExpirationDate { get; set; }

    public ICollection<PowerUnitGeolocationDto> PowerUnitGeolocations { get; set; }
}
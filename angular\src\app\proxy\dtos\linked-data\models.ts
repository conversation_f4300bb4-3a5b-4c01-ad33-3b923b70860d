import type { ContactDto, EquipmentDto, GeolocationDto, NoteDto, OrderDto } from '../direct-data/models';
import type { AuditedEntityDto } from '@abp/ng.core';

export interface AddressContactDto {
  addressId: number;
  contactId: number;
  contact: ContactDto;
}

export interface EquipmentGeolocationDto extends AuditedEntityDto {
  equipmentId: number;
  geolocationId: number;
  geolocation: GeolocationDto[];
}

export interface OrderContactDto extends AuditedEntityDto {
  orderId: number;
  contactId: number;
  order: OrderDto;
  contact: ContactDto;
}

export interface OrderNoteDto extends AuditedEntityDto {
  orderId: number;
  noteId: number;
  note: NoteDto;
}

export interface PowerUnitGeolocationDto extends AuditedEntityDto {
  powerUnitId: number;
  geolocationId: number;
  geolocation: GeolocationDto;
}

export interface StopContactDto extends AuditedEntityDto {
  stopId: number;
  contactId: number;
  contact: ContactDto;
}

export interface StopEquipmentDto extends AuditedEntityDto {
  stopId: number;
  equipmentId: number;
  equipment: EquipmentDto;
}

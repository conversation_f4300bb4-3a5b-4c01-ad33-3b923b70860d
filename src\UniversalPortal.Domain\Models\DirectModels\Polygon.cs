﻿using System.Collections.Generic;
using System.Linq;

namespace UniversalPortal.Models.DirectModels;

public interface IPolygon
{
    bool Contains(Geolocation location);
    bool Contains(double latitude, double longitude);
}

//Credit to https://stackoverflow.com/questions/46144205/point-in-polygon-using-winding-number
public class Polygon : IPolygon
{
    private readonly List<Geolocation> _points = [];

    public int Count => _points.Count;

    public Polygon(List<Geolocation> points)
    {
        _points = points;
    }

    public Polygon(IEnumerable<Geolocation> stopGeofenceAddresses)
    {
        _points = stopGeofenceAddresses?.Select(p => new Geolocation(p?.Latitude ?? 0.0, p?.Longitude ?? 0.0))
                                        .ToList() ?? [];
    }

    public bool Contains(double latitude, double longitude)
    {
        return Contains(new Geolocation(latitude, longitude));
    }

    public bool Contains(Geolocation location)
    {
        var polygonPointsWithClosure = PolygonPointsWithClosure();

        var windingNumber = 0;

        for (var pointIndex = 0; pointIndex < polygonPointsWithClosure.Length - 1; pointIndex++)
        {
            Edge edge = new(polygonPointsWithClosure[pointIndex], polygonPointsWithClosure[pointIndex + 1]);
            windingNumber += AscendingIntersection(location, edge);
            windingNumber -= DescendingIntersection(location, edge);
        }

        return windingNumber != 0;
    }

    private Geolocation[] PolygonPointsWithClosure()
    {
        return [.. _points, new(_points.FirstOrDefault()?.Latitude ?? 0.0, _points.FirstOrDefault()?.Longitude ?? 0.0)];
    }

    private static int AscendingIntersection(Geolocation location, Edge edge)
    {
        if (!edge.AscendingRelativeTo(location))
            return 0;

        if (!edge.LocationInRange(location, Orientation.Ascending))
            return 0;

        return Wind(location, edge, Position.Left);
    }

    private static int DescendingIntersection(Geolocation location, Edge edge)
    {
        if (edge.AscendingRelativeTo(location))
            return 0;

        if (!edge.LocationInRange(location, Orientation.Descending))
            return 0;

        return Wind(location, edge, Position.Right);
    }

    private static int Wind(Geolocation location, Edge edge, Position position)
    {
        return edge.RelativePositionOf(location) != position ? 0 : 1;
    }

    private class Edge(Geolocation startPoint, Geolocation endPoint)
    {
        public Position RelativePositionOf(Geolocation location)
        {
            var positionCalculation =
                (endPoint.Longitude - startPoint.Longitude) * (location.Latitude - startPoint.Latitude) -
                (location.Longitude - startPoint.Longitude) * (endPoint.Latitude - startPoint.Latitude);

            return positionCalculation switch
            {
                > 0 => Position.Left,
                < 0 => Position.Right,
                _ => Position.Center
            };
        }

        public bool AscendingRelativeTo(Geolocation location)
        {
            return startPoint.Latitude <= location.Latitude;
        }

        public bool LocationInRange(Geolocation location, Orientation orientation)
        {
            if (orientation == Orientation.Ascending)
                return endPoint.Latitude > location.Latitude;

            return endPoint.Latitude <= location.Latitude;
        }
    }

    private enum Position
    {
        Left,
        Right,
        Center
    }

    private enum Orientation
    {
        Ascending,
        Descending
    }
}
import { AuditedEntityDto } from '@abp/ng.core';
import { Component, Directive, OnInit } from '@angular/core';
import { LocalCache } from './local-cache';
import { Observable } from 'rxjs';

/**
 * Represents a locally-cached instance of a single entity.
 */
export abstract class LocalRecord<T extends AuditedEntityDto> {

  public readonly localData: T;

  public readonly recordType: string;

  /** The last time at which this record was updated with new information from the database. */
  public lastUpdated: Date;
  /** The time at which this record was migrated from TMW to the Control Tower database. */
  public importedTime: Date;
  /** The last time, for each individual field, at which the field was updated with new information from the database. */
  public lastUpdatedByField: Map<string, Date>;

  constructor(dto: T, recordType: string) {
    this.localData = dto;
    this.recordType = recordType;
    this.lastUpdated = new Date();
    this.importedTime = LocalRecord.UtcToLocalTime(this.localData.creationTime);
    this.lastUpdatedByField = new Map<string, Date>();
  }

  /**
   * Determine if a Dto of the same encapsulated type refers to the same original data.
   * Usually this means they have matching primary IDs, but ymmv.
   */
  public abstract subsumes(otherDto: T): boolean;

  /**
   * Determine if another local record of the same type refers to the SAME record.
   * Usually this means their respective localData Dtos have matching primary IDs, but ymmv.
   */
  public matches(otherRecord: LocalRecord<T>): boolean {
    return this.subsumes(otherRecord.localData);
  }

  public abstract getPrimaryId(): number;

  /**
   * Attempt to get a field of the given name from the record. Prefers fields of the inner dto over derived fields.
   * If given a field accessor, will attempt to access subrecords with member names equivalent to their type.
   */
  public getField(key: string): any {
    if (key.includes(":")) {
      var [recordType, field] = key.split(":");
      recordType = recordType.toLowerCase();

      if (this[recordType] != undefined && this[recordType]["getField"] != undefined) {
        return this[recordType]["getField"](key);
      }
      else if (this.localData[field] != undefined) {
        return this.localData[field];
      }
      else if (this[field] != undefined) {
        return this[field];
      }
    }
    else {

      if (this.localData[key] != undefined) {
        return this.localData[key];
      }
      else if (this[key] != undefined) {
        return this[key];
      }
    }

    return undefined;
  }
  /**
   * Attempt to get a field of the given name from the record in numeric form, or as a string if it's not numeric.
   */
  public getSortableField(key: string): number | string {
    if (this.getField(key) === undefined) {
      return undefined;
    }
    else if (this.getField(key) as number != undefined) {
      return this.getField(key) as number;
    }
    return this.getField(key).toString();
  }

  /**
   * Update the record to reflect the information provided in the dto. This WILL mark the entire record as updated,
   * even if all of the provided information is identical.
   * @param dto The new information from the database to reflect in the record.
   */
  public updateFields(dto: T): void {
    if (dto.lastModificationTime <= this.localData.lastModificationTime) {
      return;
    }

    console.log(`Updating (extant) ${this.recordType} ${this.getPrimaryId()}`);

    // Update values of properties inherited from the original dto
    let key: keyof T;
    for (key in this.localData) {
      if (this.localData[key] == undefined || this.localData[key] != dto[key]) {
        this.localData[key] = dto[key];
        this.lastUpdatedByField.set(key, new Date());
      }
    }

    this.calculateDerivedFields(dto);

    this.lastUpdated = new Date();
  }

  /**
   * A needlessly contrived system for automating bulk queries to tertiary data
   */
  protected queryTertiaryRecord<R extends LocalRecord<D>, D extends AuditedEntityDto<string>>(id: number, cache: LocalCache<R, D>, load: (records: R) => void): void {
    if (!id) {
      console.log(`Tried querying null ${cache.cacheType} id for ${this.recordType} ${this.getPrimaryId()}}`);
      return;
    }
    cache.requestById(id).subscribe((requestedRecord: R) => {
      console.log(`Resolved ${cache.cacheType} ${requestedRecord.getPrimaryId()} for ${this.recordType} ${this.getPrimaryId()}`);
      load(requestedRecord);
    })
  }

  protected queryTertiaryRecords<R extends LocalRecord<D>, D extends AuditedEntityDto<string>>(ids: number[], cache: LocalCache<R, D>, load: (records: R[]) => void): void {
    console.log(`Querying ${ids.length} ${cache.cacheType} id${ids.length == 1 ? '' : 's'} for ${this.recordType} ${this.getPrimaryId()}`);
    cache.getSubset(ids).subscribe((requestedRecords: R[]) => {
      console.log(`Resolved ${requestedRecords.length} ${cache.cacheType} record${requestedRecords.length == 1 ? '' : 's'} for ${this.recordType} ${this.getPrimaryId()}`);
      load(requestedRecords);
    })
  }

  // Override this to implement updates for additional (i.e. not from the original dto)
  // properties in derived classes.
  protected calculateDerivedFields(dto: T): void { }

  // Use this function to perform the derived updates so that their last update time is recorded.
  protected setDerivedField<K extends keyof this>(fieldName: K, newValue: this[K]) {
    //console.log(`Set field '${fieldName as string}' to ${newValue}`)
    if (!this.lastUpdatedByField.has(fieldName as string) || this[fieldName] != newValue) {
      this[fieldName] = newValue;
      this.lastUpdatedByField.set(fieldName as string, new Date());
    }
  }

  /**
   * Convert a UTC date (from the database) to local time using the local time zone.
   */
  private static UtcToLocalTime(date: string | Date): Date {
    return new Date(new Date(date).getTime() - new Date().getTimezoneOffset() * 60000);
  }

}

import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { Stop } from '../../models/direct-models/models';

@Injectable({
  providedIn: 'root',
})
export class StopWatcherService {
  apiName = 'Default';
  

  handle = (current: Stop, previous: Stop, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/stop-watcher/handle',
      body: previous,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

import { Component, ViewChild, AfterViewInit, Input, ElementRef, OnDestroy, LOCALE_ID, inject } from '@angular/core';
import { Map, MapGeoJSONFeature, NavigationControl, Popup } from 'maplibre-gl';
import { environment } from '../../../environments/environment';
import { StopRecord } from '../../data/records/stop.record';
import { MapService } from '../../proxy/services';
import { OrderRecord } from '../../data/records/order.record';
import { MapFeaturesDto } from '../../proxy/dtos/semantic-data';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-geo-map',
  styleUrl: 'geo-map.component.scss',
  templateUrl: 'geo-map.component.html',
  standalone: false,
  animations: [
    trigger('unhidden', [
      transition('false => true', [
        style({ opacity: '0' }),
        animate('500ms ease-in', style({ opacity: '1' }))
      ]),
    ])
  ]
})
export class GeoMapComponent implements AfterViewInit, OnDestroy {

  private locale: string = inject(LOCALE_ID);

  @Input() public displayedOrder: OrderRecord;
  @Input() public displayedStops: StopRecord[] = [];

  public map: Map | undefined;
  @ViewChild('map') mapContainer: ElementRef<HTMLElement>;

  public noDataAvailable: boolean = false;

  private focusedFeature: MapGeoJSONFeature = undefined;

  public mapIsLoading: boolean = true;

  private popup: Popup = new Popup({
    closeButton: false,
    closeOnClick: false
  });

  constructor(
    private readonly mapData: MapService
  ) { }

  public ngAfterViewInit(): void {
    if (this.displayedStops.length > 0) {
      this.changeDisplayedStops(this.displayedStops);
    }
  }

  ngOnDestroy(): void {
    this.map?.remove();
  }

  public changeDisplayedStops(stops: StopRecord[]): void {
    this.displayedStops = stops;

    if (stops.length == 0) {
      this.displayedOrder = undefined;
      this.map?.remove();
    }
    else {
      this.mapIsLoading = true;

      this.displayedOrder = this.displayedStops[0].order;
      console.log(`Initializing geo-map with ${stops.length} stops from order #${this.displayedOrder.localData.orderNumber}`, stops);

      this.mapData.getMapFeaturesByOrderById(this.displayedOrder.localData.orderId).subscribe(features => {
        this.refreshMapDisplay(features);
      });
    }
  }

  private refreshMapDisplay(data: MapFeaturesDto): void {
    if (data.boundingBoxMinLongitude == data.boundingBoxMaxLongitude
      || data.boundingBoxMinLatitude == data.boundingBoxMaxLatitude) {
      this.noDataAvailable = true;
      return;
    }
    else {
      this.noDataAvailable = false;
    }

    var centerCoords = this.getGeographicCenter(data);
    var initZoom = this.getInitialZoom(data);

    this.map = new Map({
      container: this.mapContainer.nativeElement,
      style: environment.mapTiles.styleUrl,
      zoom: initZoom,
      center: centerCoords,
      maplibreLogo: false,
      attributionControl: {
        compact: true
      },
      locale: this.locale // this only affects the controls on the map, not the map content itself
    });

    this.map.addControl(new NavigationControl({
      visualizePitch: false,
      visualizeRoll: false,
      showZoom: true,
      showCompass: false
    }));

    this.map.on('load', () => {
      this.addGeofencePolygons(data.geofencePolygons);
      this.addEquipmentRoutes(data.equipmentRoutes);
      this.addGeofenceMarkers(data.geofencePoints);
      this.map.resize();
      this.map.dragRotate.disable();
      this.map.keyboard.disable();
      this.map.touchZoomRotate.disable();
      this.map.setMinZoom(2);
      GeoMapComponent.changeMapLabelLanguage(this.map, this.locale);
      this.logMapView(centerCoords, initZoom);

      this.mapIsLoading = false;
    });
  }

  private addEquipmentRoutes(layerData): void {
    console.log("Mapping equipment routes", layerData);
    let dataName = 'routes';

    this.map.addSource(dataName, ({
      type: 'geojson',
      data: layerData
    }));

    this.map.addLayer({
      id: dataName,
      type: 'line',
      source: dataName,
      layout: {
        "line-join": 'round',
        "line-cap": 'round'
      },
      paint: {
        "line-width": 6,
        "line-color": '#cc0000',
        "line-dasharray": [0.5, 2]
      }
    })

    this.map.on('mousemove', dataName, (e) => {
      let feature = e.features[0];
      if (this.focusedFeature !== feature) {
        this.focusedFeature = feature;
        this.map.getCanvas().style.cursor = 'pointer';
        let description = `<strong>VIN</strong>:<p>${feature.properties.vin}</p>`;
        this.popup.setLngLat(e.lngLat).setHTML(description).addTo(this.map);
      }
    });

    this.map.on('mouseleave', dataName, () => {
      this.focusedFeature = undefined;
      this.map.getCanvas().style.cursor = '';
      this.popup.remove();
    });
  }

  private addGeofenceMarkers(layerData): void {
    console.log("Mapping geofence points", layerData);
    let dataName = 'fence-points';

    this.map.addSource(dataName, ({
      type: 'geojson',
      data: layerData
    }));

    this.map.addLayer({
      id: dataName,
      type: 'circle',
      source: dataName,
      paint: {
        "circle-radius": 12,
        "circle-color": '#0066ff'
      }
    })

    this.map.on('mousemove', dataName, (e) => {
      let feature = e.features[0];
      if (this.focusedFeature !== feature) {
        this.focusedFeature = feature;
        this.map.getCanvas().style.cursor = 'pointer';
        let description = `<strong>Location</strong>:<p>${feature.properties.displayName}</p>`;
        this.popup.setLngLat(e.lngLat).setHTML(description).addTo(this.map);
      }
    });

    this.map.on('mouseleave', dataName, () => {
      this.focusedFeature = undefined;
      this.map.getCanvas().style.cursor = '';
      this.popup.remove();
    });
  }

  private addGeofencePolygons(layerData): void {
    console.log("Mapping geofence polygons", layerData);
    let dataName = 'fence-polygons';

    this.map.addSource(dataName, ({
      type: 'geojson',
      data: layerData
    }));

    this.map.addLayer({
      id: dataName,
      type: 'fill',
      source: dataName,
      layout: {},
      paint: {
        'fill-color': '#009900',
        'fill-opacity': 0.4
      }
    })

    this.map.on('mousemove', dataName, (e) => {
      let feature = e.features[0];
      if (this.focusedFeature !== feature) {
        this.focusedFeature = feature;
        this.map.getCanvas().style.cursor = 'pointer';
        let description = `<strong>Geofence</strong>: "${feature.properties.name}"`;
        this.popup.setLngLat(e.lngLat).setHTML(description).addTo(this.map);
      }
    });

    this.map.on('mouseleave', dataName, () => {
      this.focusedFeature = undefined;
      this.map.getCanvas().style.cursor = '';
      this.popup.remove();
    });
  }

  private logMapView(center: [number, number], zoom: number) {
    console.log("Refreshed map view:\n\tZoom @ %s\n\tCentered @ (%s, %s)\n",
      zoom.toFixed(2),
      center[0].toFixed(4),
      center[1].toFixed(4),
      this.map);
  }

  /**
   * Return the geographic center of the bounding box defined in the dto
   */
  private getGeographicCenter(data: MapFeaturesDto): [number, number] {
    var boundWidth = data.boundingBoxMaxLongitude - data.boundingBoxMinLongitude;
    var boundHeight = data.boundingBoxMaxLatitude - data.boundingBoxMinLatitude;

    return [
      data.boundingBoxMinLongitude + boundWidth / 2,
      data.boundingBoxMinLatitude + boundHeight / 2
    ];
  }

  /**
   * Calculate a zoom level that neatly encompasses the bounding box defined in the dto
   */
  private getInitialZoom(data: MapFeaturesDto): number {
    /**
     * Zoom level 0 corresponds to a view of the entire globe, and incrementing the zoom level by 1 halves the displayed area.
     *  So zoom level 0 corresponds to 180 degrees of vertical display.
     *  (90' at N pole, 0 at equator, -90' at S pole)
     * 
     * In showing the map, we're restricted more by vertical range than horizontal range.
     *  The map can wrap horizontally, but it's vertically locked to the N/S poles.
     *  Also, we're probably displaying the map in a landscape format.
     *
     * Since we already have the bounding box, we simply need to figure out
     *  its height, plus a little extra to create a margin, then determine
     *  what zoom level shrinks the map enough to perfectly fit everything.
     */

    var desiredRange = Math.abs(data.boundingBoxMaxLatitude - data.boundingBoxMinLatitude);

    // How many times must we divide 180 by 2 until it encompasses (115% of) our desired range?
    return Math.log2(180 / (1.15 * desiredRange));
  }

  /**
   * Modify the map object to use the given display language.
   */
  private static changeMapLabelLanguage(map: Map, lang: string) {
    // No pretty way of doing this
    // We essentially have to traverse the entire tree of map objects and change its properties
    map.getStyle().layers.forEach((layer) => {
      if (layer.id.startsWith('label_')) {
        map.setLayoutProperty(layer.id, 'text-field', [
          "case",
          ["has", "name:nonlatin"],
          [
            "concat",
            ["get", "name:latin"],
            "\n",
            ["get", "name:nonlatin"]
          ],
          [
            'coalesce',
            ['get', `name:${lang}`],
            ['get', 'name'],
          ]
        ]);
      }
    });
  }
}

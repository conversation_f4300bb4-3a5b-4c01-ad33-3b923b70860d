import { Injectable, inject } from '@angular/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { LocalizationService } from '@abp/ng.core'
import { Subject } from 'rxjs';

import '@angular/localize/init';

@Injectable()
export class IntlPaginatorComponent implements MatPaginatorIntl {

  private localizationService: LocalizationService = inject(LocalizationService);

  changes = new Subject<void>(); // mandated by the inheritance from MatPaginatorIntl

  constructor() {
    console.log('Initializing international paginator with lang:', this.localizationService.currentLang);
    this.initializeLabels();

    this.localizationService.languageChange$.subscribe(() => {
      console.log('Updated current language:', this.localizationService.currentLang);
      this.initializeLabels();
      this.changes.next();
    });
  }

  private initializeLabels() {

    this.firstPageLabel = this.localizationService.instant({
      key: '::Paginator:firstPage',
      defaultValue: '{firstPage}'
    });
    this.lastPageLabel = this.localizationService.instant({
      key: '::Paginator:lastPage',
      defaultValue: '{lastPage}'
    });
    this.previousPageLabel = this.localizationService.instant({
      key: '::Paginator:previousPage',
      defaultValue: '{previousPage}'
    });
    this.nextPageLabel = this.localizationService.instant({
      key: '::Paginator:nextPage',
      defaultValue: '{nextPage}'
    });

    this.itemsPerPageLabel = this.localizationService.instant({
      key: '::Paginator:itemsPerPage',
      defaultValue: '{itemsPerPage}'
    });
  }

  firstPageLabel: string;
  lastPageLabel: string;
  previousPageLabel: string;
  nextPageLabel: string;

  itemsPerPageLabel: string;

  getRangeLabel(page: number, pageSize: number, length: number) {
    let firstRecordShown = page * pageSize + 1;
    let lastRecordShown = Math.min(length, (page + 1) * pageSize);

    return this.localizationService.instant('::Paginator:displayedRange', firstRecordShown.toString(), lastRecordShown.toString(), length.toString());
  }
}

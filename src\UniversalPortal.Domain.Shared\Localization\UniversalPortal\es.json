{
  "culture": "es",
  "texts": {
    "AppName": "Control Tower",
    "Menu:home": "Hogar",
    "Page:dashboard": "Panel de control",

    "Table:sortBy": "ordenar por",
    "Table:inspect": "examinar",

    "Paginator:itemsPerPage": "registros por página",
    "Paginator:displayedRange": "{0}–{1} de {2}",
    "Paginator:firstPage": "primera página",
    "Paginator:lastPage": "última página",
    "Paginator:previousPage": "página anterior",
    "Paginator:nextPage": "página siguiente",

    // These are used for debugging, no translation needed
    //"Record:lastUpdated": "Last Updated",
    //"Record:importedTime": "Imported",

    "Order": "pedido",
    "Order:orderId": "'id' de pedido",
    "Order:orderNumber": "pedido #",
    "Order:startDate": "fecha de inicio",
    "Order:route": "ruta",
    "Order:miles": "millas",
    "Order:companyName": "proveedor",
    "Order:companyId": "'id' del proveedor",
    "Order:weekEnding": "última fecha de la semana",

    "Stop": "parada",
    "Stop:stopId": "'id' de parada",
    "Stop:stopNumber": "parada #",
    "Stop:type": "tipo",
    "Stop:sequence": "orden",
    "Stop:mileage": "distancia (millas)",
    "Stop:scheduledEarliest": "programado más temprano",
    "Stop:arrival": "llegada",
    "Stop:arrivalStatus": "estado de llegada",
    "Stop:lateArrivalReason": "razón de la llegada tardía",
    "Stop:universalLateArrival": "¿Llegada tardía por culpa de Universal?",
    "Stop:scheduledLatest": "última programada",
    "Stop:departure": "salida",
    "Stop:departureStatus": "estado de salida",
    "Stop:lateDepartureReason": "razón de la salida tardía",
    "Stop:universalLateDeparture": "¿Llegada salida por culpa de Universal?",
    "Stop:minutesWaiting": "minutos de espera",
    "Stop:arrivalDisparityMinutes": "disparidad de llegadas (min)",
    "Stop:departureDisparityMinutes": "disparidad de salidas (min)",
    "Stop:supplierDelayWarningNeeded": "¿Advertir sobre retraso del proveedor?",
    "Stop:windowTimeMinutes": "ventana de tiempo (min)",
    "Stop:minutesOverTimeWindow": "tiempo más allá de la ventana (min)",

    "Contacts": "contactos",
    "Notes": "notas",

    "Status": "estado",
    "Status:code0": "temprano",
    "Status:code1": "tarde",
    "Status:code2": "a tiempo",

    "Boolean:true": "cierto",
    "Boolean:false": "falso",
    
    "Chart:select": "Tipo de gráfico",
    "Chart:locations": "ubicaciones",
    "Chart:dateRange": "rango de fechas",
    "Chart:generate": "renderizar el gráfico",
    "Chart:allLocations": "seleccionar todo",
    "Chart:noLocations": "deseleccionar todo",
    
    "Chart:hours": "horas",
    "Chart:assets": "activos",
    "Chart:location": "ubicación",
    
    // infixed to chart subtitles, e.g. "[Date Range] (as of [Time of Day])"
    "Chart:asOf": "a las",
    
    "Stat:avgDwellTime:Option": "Duración media de la estancia",
    "Stat:avgDwellTime:Title": "Duración media de la estancia por ubicación",
    "Stat:trailerCount:Option": "Recuento de activos",
    "Stat:trailerCount:Title": "Recuento de activos por ubicación",
    "Stat:avgTimeSinceMove:Option": "Tiempo promedio transcurrido desde el movimiento",
    "Stat:avgTimeSinceMove:Title": "Tiempo promedio transcurrido desde el movimiento por ubicación",
    "Stat:24hInactive:Option": "Recuento de activos inactivos 24h",
    "Stat:24hInactive:Title": "Recuento de activos inactivos de 24 horas por ubicación"
  }
}

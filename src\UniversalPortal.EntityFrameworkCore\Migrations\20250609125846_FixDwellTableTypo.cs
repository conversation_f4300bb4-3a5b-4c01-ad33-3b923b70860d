﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class FixDwellTableTypo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Dwell_Geolocations_GeolocationEnteyId",
                schema: "ControlTower",
                table: "Dwell");

            migrationBuilder.RenameColumn(
                name: "GeolocationEnteyId",
                schema: "ControlTower",
                table: "Dwell",
                newName: "GeolocationEntryId");

            migrationBuilder.RenameIndex(
                name: "IX_Dwell_GeolocationEnteyId",
                schema: "ControlTower",
                table: "Dwell",
                newName: "IX_Dwell_GeolocationEntryId");

            migrationBuilder.AddForeignKey(
                name: "FK_Dwell_Geolocations_GeolocationEntryId",
                schema: "ControlTower",
                table: "Dwell",
                column: "GeolocationEntryId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Dwell_Geolocations_GeolocationEntryId",
                schema: "ControlTower",
                table: "Dwell");

            migrationBuilder.RenameColumn(
                name: "GeolocationEntryId",
                schema: "ControlTower",
                table: "Dwell",
                newName: "GeolocationEnteyId");

            migrationBuilder.RenameIndex(
                name: "IX_Dwell_GeolocationEntryId",
                schema: "ControlTower",
                table: "Dwell",
                newName: "IX_Dwell_GeolocationEnteyId");

            migrationBuilder.AddForeignKey(
                name: "FK_Dwell_Geolocations_GeolocationEnteyId",
                schema: "ControlTower",
                table: "Dwell",
                column: "GeolocationEnteyId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}

import { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import { BehaviorSubject, Observable, concatMap, filter, forkJoin, map, take, toArray } from 'rxjs';
import { SignalRService } from '../shared/services/signalr-update.service';
import { LocalRecord } from './local-record';
import { GenericEndpoint } from '../shared/services/generic-endpoint.service';
import { Injectable } from '@angular/core';
import { UpdateDto } from '../shared/dtos/update.model';

@Injectable({
  providedIn: 'root'
})
export abstract class LocalCache<R extends LocalRecord<D>, D extends AuditedEntityDto<string>> {

  public records$: BehaviorSubject<R[]>;
  public loading$: BehaviorSubject<boolean>;
  public length: number;

  public cacheType: string;

  private cachedRecords: R[];
  private cachedRequests: Map<number, Observable<R>>;

  public constructor(
    signalRGroupName: string,
    makeInitialRequest: boolean,
    private dataEndpoint: GenericEndpoint<D>,
    private dataBroker: SignalRService<D>
  ) {
    this.cacheType = signalRGroupName;

    this.records$ = new BehaviorSubject<R[]>([]);
    this.loading$ = new BehaviorSubject<boolean>(false);
    this.length = 0;

    this.cachedRecords = [];
    this.cachedRequests = new Map<number, Observable<R>>();

    this.dataBroker.initialize(signalRGroupName);
    this.dataBroker.entityUpdate$.subscribe((update: UpdateDto<D>) => {
      this.processSingleUpdate(update.after);
      this.updateFeed();
    });

    if (makeInitialRequest) {
      this.reload(LocalCache.produceInitialRequest());
    }
  }

  // Create the request dto that will be used to query and load the initial cache contents.
  private static produceInitialRequest(): PagedAndSortedResultRequestDto {
    var request = new PagedAndSortedResultRequestDto();
    request.maxResultCount = 1000;
    return request;
  }

  // Calls the constructor for the record type, R, parameterized by the dto type, D.
  // This can't be dynamically derived from the generic type, so it must be concretely implemented by the child class.
  public abstract processDto(dto: D): R;

  /**
   * Request a feed from the cache that contains only a subset of its records.
   * @param ids The primary-key IDs of the records that should inhabit the feed.
   */
  public getSubset(ids: number[]): Observable<R[]> {
    var pendingResult = forkJoin(ids.filter(x => x).map(x => this.requestById(x)));

    //pendingResult.subscribe(() => this.updateFeed());

    return pendingResult;
  }

  /**
   * Direct the cache to clear its contents and submit the given request to the backend database, repopulating itself with the results.
   * @param request The parameters of the request to be made to the backend database, dictating the new contents of the cache.
   */
  public reload(request: PagedAndSortedResultRequestDto): void {
    this.cachedRecords = [];
    this.updateFeed();
    this.loading$.next(true);

    this.dataEndpoint.getList(request).subscribe((response) => {
      this.processMultipleUpdates(response.items);
    });
  }

  /**
   * Request that the cache begin monitoring the entity with the given primary-key id.
   * @param id The primary key of the target entity within the database.
   * @returns An observable that will, upon subscription, prompt the cache to query the target entity from the backend database.
   */
  public requestById(id: number): Observable<R> {
    var cachedResult = this.cachedRecords.find(x => x.getPrimaryId() == id);
    //console.log(`Requesting '${this.dataBroker.groupName}' #${id}...`);

    if (cachedResult != undefined) {
      console.log(`Request for ${this.cacheType} ${id} matches cached record`)
      return new Observable((subscriber) => subscriber.next(cachedResult));
    }
    else if (this.cachedRequests.has(id)) {
      console.log(`Request for ${this.cacheType} ${id} pending...`)
      return this.cachedRequests.get(id);
    }
    else {
      console.log(`Request for ${this.cacheType} ${id} prompted new query`)
      var newRequest = this.dataEndpoint.get(id).pipe(map((dto) => this.processSingleUpdate(dto)));

      this.cachedRequests.set(id, newRequest);
      newRequest.subscribe((_) => this.cachedRequests.delete(id));
      return newRequest;
    }
  }

  /**
   * Process multiple Dtos in sequence
   */
  private processMultipleUpdates(updateDtos: D[]): void {
    console.log(`Aggregate ${this.cacheType} update (${updateDtos.length} records)`, updateDtos);
    updateDtos.forEach(dto => this.processSingleUpdate(dto));
    this.updateFeed();
    this.loading$.next(false);
  }

  /**
   * Process a single Dto corresponding to the entity type this cache keeps track of, reifying it into a local record.
   */
  private processSingleUpdate(updateDto: D): R {
    var target = this.cachedRecords.find(x => x.subsumes(updateDto));

    if (target == undefined) {
      var newRecord = this.processDto(updateDto);
      console.log(`Created record of ${this.cacheType} ${newRecord.getPrimaryId()}`);
      this.cachedRecords.push(newRecord);
      return newRecord;
    }
    else {
      target.updateFields(updateDto);
      return target;
    }
  }

  /**
   * Update the public feed member with the current contents of the cache.
   */
  private updateFeed(): void {
    this.records$.next(Array.from(this.cachedRecords));
    this.length = this.cachedRecords.length;
  }
}

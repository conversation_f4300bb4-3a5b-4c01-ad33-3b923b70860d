﻿using System;
using System.Collections.Generic;
using UniversalPortal.DTOs.LinkedData;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class EquipmentDto : AuditedEntityDto
{
    public EquipmentDto()
    {
        EquipmentGeolocations = [];
        GeolocationIds = [];
        StopIds = [];
    }

    public int EquipmentId { get; set; }
    public string? EquipmentNumber { get; set; }
    public string? Vin { get; set; }
    public DateTime ExpirationDate { get; set; }
    public int EquipmentTypeId { get; set; }
    public string? Description { get; set; }
    public EquipmentTypeDto? EquipmentType { get; set; }
    public ICollection<EquipmentGeolocationDto> EquipmentGeolocations { get; set; }
    public ICollection<int> GeolocationIds { get; set; }
    public ICollection<int> StopIds { get; set; }

}
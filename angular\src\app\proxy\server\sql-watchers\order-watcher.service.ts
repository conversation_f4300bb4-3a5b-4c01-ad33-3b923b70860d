import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { Order } from '../../models/direct-models/models';

@Injectable({
  providedIn: 'root',
})
export class OrderWatcherService {
  apiName = 'Default';
  

  handle = (current: Order, previous: Order, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/order-watcher/handle',
      body: previous,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

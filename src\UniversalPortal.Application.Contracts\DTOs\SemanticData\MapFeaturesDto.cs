﻿using System;
using NetTopologySuite.Features;

namespace UniversalPortal.DTOs.SemanticData
{
    /// <summary>
    /// Represents several layers to be mapped on the frontend
    /// </summary>
    [Serializable]
    public sealed class MapFeaturesDto(
        FeatureCollection equipmentRoutes,
        FeatureCollection geofencePoints,
        FeatureCollection geofencePolygons,
        double minBoundX,
        double minBoundY,
        double maxBoundX,
        double maxBoundY)
    {

        // NOTE:
        // These members are INTENTIONALLY untyped objects!
        // The NetTopologySuite classes serialize directly into the GeoJSON objects needed by the front end.
        // We DON'T want ABP to generate DTO classes for the entire type hierarchy.
        public object EquipmentRoutes { get; set; } = equipmentRoutes;
        public object GeofencePoints { get; set; } = geofencePoints;
        public object GeofencePolygons { get; set; } = geofencePolygons;

        public double BoundingBoxMinLatitude { get; set; } = minBoundY;
        public double BoundingBoxMinLongitude { get; set; } = minBoundX;

        public double BoundingBoxMaxLatitude { get; set; } = maxBoundY;
        public double BoundingBoxMaxLongitude { get; set; } = maxBoundX;
    }
}

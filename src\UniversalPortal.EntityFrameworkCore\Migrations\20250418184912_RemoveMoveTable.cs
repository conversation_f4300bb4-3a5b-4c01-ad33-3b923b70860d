﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class RemoveMoveTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Legs_Moves_MoveId",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropForeignKey(
                name: "FK_Orders_Moves_MoveId",
                schema: "ControlTower",
                table: "Orders");

            migrationBuilder.DropTable(
                name: "Moves",
                schema: "ControlTower");

            migrationBuilder.DropIndex(
                name: "IX_Orders_MoveId",
                schema: "ControlTower",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_Legs_MoveId",
                schema: "ControlTower",
                table: "Legs");

            migrationBuilder.DropColumn(
                name: "MoveId",
                schema: "ControlTower",
                table: "Orders");

            migrationBuilder.AddColumn<int>(
                name: "MoveNumber",
                schema: "ControlTower",
                table: "Stops",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MoveNumber",
                schema: "ControlTower",
                table: "Stops");

            migrationBuilder.AddColumn<int>(
                name: "MoveId",
                schema: "ControlTower",
                table: "Orders",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Moves",
                schema: "ControlTower",
                columns: table => new
                {
                    MoveId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    MoveNumber = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Moves", x => x.MoveId);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Orders_MoveId",
                schema: "ControlTower",
                table: "Orders",
                column: "MoveId");

            migrationBuilder.CreateIndex(
                name: "IX_Legs_MoveId",
                schema: "ControlTower",
                table: "Legs",
                column: "MoveId");

            migrationBuilder.AddForeignKey(
                name: "FK_Legs_Moves_MoveId",
                schema: "ControlTower",
                table: "Legs",
                column: "MoveId",
                principalSchema: "ControlTower",
                principalTable: "Moves",
                principalColumn: "MoveId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Orders_Moves_MoveId",
                schema: "ControlTower",
                table: "Orders",
                column: "MoveId",
                principalSchema: "ControlTower",
                principalTable: "Moves",
                principalColumn: "MoveId");
        }
    }
}

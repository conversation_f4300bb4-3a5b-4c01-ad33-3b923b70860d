﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Dwell : TenantAuditedEntity
{
    public Dwell()
    {
    }

    [Key]
    public int DwellId { get; set; }
    public int GeofenceId { get; set; }
    public int EquipmentId { get; set; }
    public int GeolocationEntryId { get; set; }
    public int? GeolocationDepartureId { get; set; }
    // this is intentionally hiding the inherited CreationTime, since we do not want a system generated time for the entity.
    // This should be derived from the times of the geolocation pings and without hiding it, we are unable to manually enter a value into this field via EF.
    public new DateTime CreationTime { get; set; }

    [ForeignKey(nameof(GeofenceId))]
    [InverseProperty(nameof(DirectModels.Geofence.Dwells))]
    public required Geofence Geofence { get; set; }

    [ForeignKey(nameof(EquipmentId))]
    [InverseProperty(nameof(DirectModels.Equipment.Dwells))]
    public Equipment? Equipment { get; set; }

    [ForeignKey(nameof(GeolocationEntryId))]
    [InverseProperty(nameof(Geolocation.EntryDwell))]
    public required Geolocation EntryGeolocation { get; set; }

    [ForeignKey(nameof(GeolocationDepartureId))]
    [InverseProperty(nameof(Geolocation.DepartureDwell))]
    public Geolocation? DepartureGeolocation { get; set; }

    public override object[] GetKeys()
    {
        return [DwellId];
    }
}
image: atlassian/default-image:3

definitions:
  caches:
    dotnetcore:
      path: $USERPROFILE/.nuget/packages
    node-temp:
      path: $USERPROFILE/node-v20.19.0
    node-cache:
      path: $LOCALAPPDATA\Yarn\Cache
    angular-node-modules:
      path: angular/node_modules

steps:
  - step: &build-dotnet
      name: 'Build & Test .NET Backend'
      runs-on:
        - self.hosted
        - windows
      caches:
        - dotnetcore
      script:
        - dotnet --version
        - dotnet restore UniversalPortal-CICD.sln
        - dotnet build UniversalPortal-CICD.sln --configuration Release
        - dotnet test UniversalPortal-CICD.sln --configuration Release --no-build --logger trx
        - dotnet publish src/UniversalPortal.HttpApi.Host/UniversalPortal.HttpApi.Host.csproj --configuration Release --output publish/apihost
      artifacts:
        - publish/apihost/**

  - step: &run-migrations
      name: 'Run Database Migrations'
      runs-on:
        - self.hosted
        - windows
      script:
        - $env:ConnectionStrings__Default = "Server=$env:DB_SERVER;Database=$env:DB_NAME;Trusted_Connection=True;Encrypt=False;"
        - cd src/UniversalPortal.DbMigrator
        - dotnet run --configuration Release
      env:
        DB_SERVER: ${{ secrets.DB_SERVER }}
        DB_NAME: ${{ secrets.DB_NAME }}

  - step: &build-angular
      name: 'Build Angular Frontend'
      runs-on:
        - self.hosted
        - windows
      caches:
        - node-temp
        - node-cache
        - angular-node-modules
      script:
        - powershell -ExecutionPolicy Bypass -File scripts/install-node-yarn-temp.ps1
        - if (Test-Path "C:\certs\umbrella-root.cer") { $env:NODE_EXTRA_CA_CERTS="C:\certs\umbrella-root.cer" }
      artifacts:
        - publish/angular/**

  - step: &deploy
      name: 'Deploy to Windows Server'
      runs-on:
        - self.hosted
        - windows
      script:
        # Base Path
        - $basePath = "E:\Websites\ControlTower"
        - $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

        # Stop the application pool
        - Import-Module WebAdministration
        - $website = Get-Website -Name "ControlTower"
        - $appPoolState = Get-WebAppPoolState -Name "ControlTower"

        - if ($website.State -eq "Started") { Stop-Website -Name "ControlTower" }
        - if ($appPoolState.Value -eq "Started") { Stop-WebAppPool -Name "ControlTower" }

        - $website = Get-Website -Name "ControlTowerApi"
        - $appPoolState = Get-WebAppPoolState -Name "ControlTowerApi"

        - if ($website.State -eq "Started") { Stop-Website -Name "ControlTowerApi" }
        - if ($appPoolState.Value -eq "Started") { Stop-WebAppPool -Name "ControlTowerApi" }


        # Deploy API
        # 2. Define deployment path
        - $rootPath = "$basePath\apihost"
        - $rootDeploymentPath = "$rootPath\Deployments"
        - $deploymentPath = "$rootDeploymentPath\$timestamp"

        # 3. Ensure base Deployments directory exists
        - if (!(Test-Path "$rootDeploymentPath")) {New-Item -ItemType Directory -Path $rootDeploymentPath | Out-Null }

        # 4. Create this deployment's folder  
        - New-Item -ItemType Directory -Path $deploymentPath | Out-Null

        # 5. Copy build output to deployment folder
        - Copy-Item -Path "publish/apihost/*" -Destination $deploymentPath -Recurse -Force

        # 6. Remove old Live symlink (if it exists)
        - cmd /c rmdir "$rootPath\Live"

        # 7. Create new Live symlink to point to the new deployment
        - cmd /c mklink /D "$rootPath\Live" "$deploymentPath"


        # Deploy Angular
        # 2. Define deployment path
        - $rootPathAngular = "$basePath\angular"
        - $rootDeploymentPathAngular = "$rootPathAngular\Deployments"
        - $deploymentPathAngular = "$rootDeploymentPathAngular\$timestamp"

        # 3. Ensure base Deployments directory exists
        - if (!(Test-Path "$rootDeploymentPathAngular")) {New-Item -ItemType Directory -Path $rootDeploymentPathAngular | Out-Null }

        # 4. Create this deployment's folder  
        - New-Item -ItemType Directory -Path $deploymentPathAngular | Out-Null

        # 5. Copy build output to deployment folder
        - Copy-Item -Path "publish/angular/*" -Destination $deploymentPathAngular -Recurse -Force

        # 6. Remove old Live symlink (if it exists)
        - cmd /c rmdir "$rootPathAngular\Live"

        # 7. Create new Live symlink to point to the new deployment
        - cmd /c mklink /D "$rootPathAngular\Live" "$deploymentPathAngular"

        # Set proper permissions
        - $acl = Get-Acl $basePath
        - $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
        - $acl.SetAccessRule($accessRule)
        - Set-Acl $basePath $acl
        
        # Start the application pool
        - Start-WebAppPool -Name "ControlTower"
        - Start-Website -Name "ControlTower"

        # Start the application pool
        - Start-WebAppPool -Name "ControlTowerApi"
        - Start-Website -Name "ControlTowerApi"

        # Clean up old backups (keep last 5)
        - Get-ChildItem -Path "$basePath\apihost\Deployments" -Filter "*" | Sort-Object CreationTime -Descending | Select-Object -Skip 5 | Remove-Item -Recurse -Force
        - Get-ChildItem -Path "$basePath\angular\Deployments" -Filter "*" | Sort-Object CreationTime -Descending | Select-Object -Skip 5 | Remove-Item -Recurse -Force

pipelines:
  branches:
    main:
      - step: *build-dotnet
      - step: 
          <<: *run-migrations
          deployment: staging     
      - step: *build-angular     
      - step: *deploy
        deployment: staging
      
    development:
      - step: *build-dotnet
      - step: 
          <<: *run-migrations
          deployment: development
      - step: *build-angular
      - step: *deploy
        deployment: development
  pull-requests:
    '**':
      - step: *build-dotnet
      - step: *build-angular
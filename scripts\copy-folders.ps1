# scripts/copy-folders.ps1

$srcPath = "aspnet-core/src"
$destPath = "E:\Websites\Yms"
$foldersToCopy = @(
  "ulh.yms.Application",
  "ulh.yms.Application.Contracts",
  "ulh.yms.DbMigrator",
  "ulh.yms.Domain",
  "ulh.yms.Domain.Shared",
  "ulh.yms.EntityFrameworkCore",
  "ulh.yms.HttpApi",
  "ulh.yms.HttpApi.Client"
)

foreach ($folder in $foldersToCopy) {
  $sourceFolder = Join-Path -Path $srcPath -ChildPath $folder
  $destinationFolder = Join-Path -Path $destPath -ChildPath $folder

  # Delete only if the destination is one of the known folders
  if (Test-Path $destinationFolder) {
    Write-Host "Removing existing folder: $destinationFolder"
    Remove-Item -Path $destinationFolder -Recurse -Force
  }

  if (Test-Path $sourceFolder) {
    Write-Host "Copying $folder from $sourceFolder to $destinationFolder"
    Copy-Item -Path $sourceFolder -Destination $destinationFolder -Recurse
  } else {
    Write-Host "$sourceFolder does not exist. Skipping."
  }
}

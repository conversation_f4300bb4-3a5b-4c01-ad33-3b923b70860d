/* You can add global styles to this file, and also import other style files */

@import '@angular/cdk/a11y-prebuilt.css';
@import "~@angular/material/prebuilt-themes/indigo-pink.css";
@import url("https://fonts.googleapis.com/icon?family=Material+Icons");
@import '~maplibre-gl/dist/maplibre-gl.css';

:root .lpx-brand-logo {
  --lpx-logo: url('/assets/images/logo/logo-light.svg');
  --lpx-logo-icon: url('/assets/images/logo/icon.svg');
}

:root {
  --lpx-theme-light-bg: url('/assets/images/login/login-bg-img-light.svg');
  --lpx-theme-dim-bg: url('/assets/images/login/login-bg-img-dim.svg');
  --lpx-theme-dark-bg: url('/assets/images/login/login-bg-img-dark.svg');

  .abp-logo {
    background-repeat: no-repeat;
  }

  .abp-support {
    width: 178px;
    height: 30px;
    background-image: url('/assets/images/getting-started/abp-support.svg');
  }

  .abp-community {
    width: 215px;
    height: 30px;
    background-image: url('/assets/images/getting-started/abp-community.svg');
  }

  .abp-blog {
    width: 142px;
    height: 30px;
    background-image: url('/assets/images/getting-started/abp-blog.svg');
  }

  .logo-x {
    width: 20px;
    height: 20px;
    background-image: url('/assets/images/getting-started/x.svg');
  }
}

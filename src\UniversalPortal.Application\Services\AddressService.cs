﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class AddressService(IRepository<Address> repository) : ApplicationService, IAddressService
{
    public async Task<AddressDto> CreateAsync(AddressDto input)
    {
        var driver = ObjectMapper.Map<AddressDto, Address>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Address, AddressDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.AddressId == id);
    }

    public async Task<AddressDto> GetAsync(int id)
    {
        var driver = await repository.GetAsync(x => x.AddressId == id);
        return ObjectMapper.Map<Address, AddressDto>(driver);
    }

    public async Task<PagedResultDto<AddressDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var driver = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<AddressDto>(
            totalCount,
            ObjectMapper.Map<List<Address>, List<AddressDto>>(driver)
        );
    }

    public Task<AddressDto> UpdateAsync(int id, AddressDto input)
    {
        throw new NotImplementedException();
    }
}
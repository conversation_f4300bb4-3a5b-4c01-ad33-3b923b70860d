{"App": {"SelfUrl": "http://localhost:44312", "AngularUrl": "http://localhost:4200", "CorsOrigins": "http://localhost:4200,http://localhost:44369", "RedirectAllowedUrls": "http://localhost:4200", "DisablePII": false, "HealthCheckUrl": "/health-status", "UsePhysicalFiles": true}, "ConnectionStrings": {"Default": "Server=localhost;Database=ControlTower_Dev;Trusted_Connection=True;TrustServerCertificate=true", "UniversalPortalDb": "Server=localhost;Database=ControlTower_Dev;Trusted_Connection=True;Encrypt=False;"}, "AuthServer": {"Authority": "http://localhost:44312", "RequireHttpsMetadata": false, "SwaggerClientId": "UniversalPortal_Swagger", "CertificatePassPhrase": "b80e8beb-ab94-4986-a55e-70c8b0f6095f"}, "StringEncryption": {"DefaultPassPhrase": "WxSVqetBp4V1T5OJ"}}
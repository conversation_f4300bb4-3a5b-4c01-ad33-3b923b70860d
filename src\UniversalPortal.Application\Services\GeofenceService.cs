﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class GeofenceService(IRepository<Geofence> repository) : ApplicationService, IGeofenceService
{
    public async Task<GeofenceDto> CreateAsync(GeofenceDto input)
    {
        var driver = ObjectMapper.Map<GeofenceDto, Geofence>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Geofence, GeofenceDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.GeofenceId == id);
    }

    public async Task<GeofenceDto> GetAsync(int id)
    {
        var driver = await repository.GetAsync(x => x.GeofenceId == id);
        return ObjectMapper.Map<Geofence, GeofenceDto>(driver);
    }

    public async Task<PagedResultDto<GeofenceDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var driver = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<GeofenceDto>(
            totalCount,
            ObjectMapper.Map<List<Geofence>, List<GeofenceDto>>(driver)
        );
    }

    public Task<GeofenceDto> UpdateAsync(int id, GeofenceDto input)
    {
        throw new NotImplementedException();
    }
}
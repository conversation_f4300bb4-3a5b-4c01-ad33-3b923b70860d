﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>UniversalPortal</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\UniversalPortal.Domain\UniversalPortal.Domain.csproj" />
    <ProjectReference Include="..\UniversalPortal.Application.Contracts\UniversalPortal.Application.Contracts.csproj" />
    <ProjectReference Include="..\UniversalPortal.EntityFrameworkCore\UniversalPortal.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="NetTopologySuite.IO.GeoJSON" Version="4.0.0" />
    <PackageReference Include="NetTopologySuite.IO.GeoJSON4STJ" Version="4.0.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application" Version="9.0.4" />
  </ItemGroup>

</Project>

﻿using UniversalPortal.DTOs.LinkedData;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class AttachmentDto : AuditedEntityDto
{
    public AttachmentDto()
    {
    }

    public int AttachmentId { get; set; }
    public int NoteId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public byte[] Content {  get; set; } = [];

    public OrderNoteDto? Note { get; set; }
}
﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class RemoveEquipmentEquipmentGeolocations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Geolocations_EquipmentGeolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropTable(
                name: "EquipmentEquipmentGeolocation",
                schema: "ControlTower");

            migrationBuilder.DropIndex(
                name: "IX_PowerUnitGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations");

            migrationBuilder.DropIndex(
                name: "IX_Geolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "EquipmentGeolocationsEquipmentId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.DropColumn(
                name: "EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations");

            migrationBuilder.AlterColumn<int>(
                name: "GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .OldAnnotation("Relational:ColumnOrder", 1);

            migrationBuilder.AlterColumn<int>(
                name: "PowerUnitId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .OldAnnotation("Relational:ColumnOrder", 0);

            migrationBuilder.CreateIndex(
                name: "IX_PowerUnitGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                column: "GeolocationId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "EquipmentGeolocations",
                column: "GeolocationId");

            migrationBuilder.AddForeignKey(
                name: "FK_EquipmentGeolocations_Equipment_EquipmentId",
                schema: "ControlTower",
                table: "EquipmentGeolocations",
                column: "EquipmentId",
                principalSchema: "ControlTower",
                principalTable: "Equipment",
                principalColumn: "EquipmentId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_EquipmentGeolocations_Geolocations_GeolocationId",
                schema: "ControlTower",
                table: "EquipmentGeolocations",
                column: "GeolocationId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EquipmentGeolocations_Equipment_EquipmentId",
                schema: "ControlTower",
                table: "EquipmentGeolocations");

            migrationBuilder.DropForeignKey(
                name: "FK_EquipmentGeolocations_Geolocations_GeolocationId",
                schema: "ControlTower",
                table: "EquipmentGeolocations");

            migrationBuilder.DropIndex(
                name: "IX_PowerUnitGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations");

            migrationBuilder.DropIndex(
                name: "IX_EquipmentGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "EquipmentGeolocations");

            migrationBuilder.AlterColumn<int>(
                name: "GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("Relational:ColumnOrder", 1);

            migrationBuilder.AlterColumn<int>(
                name: "PowerUnitId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("Relational:ColumnOrder", 0);

            migrationBuilder.AddColumn<int>(
                name: "EquipmentGeolocationsEquipmentId",
                schema: "ControlTower",
                table: "Geolocations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "EquipmentEquipmentGeolocation",
                schema: "ControlTower",
                columns: table => new
                {
                    EquipmentId = table.Column<int>(type: "int", nullable: false),
                    EquipmentGeolocationsEquipmentId = table.Column<int>(type: "int", nullable: false),
                    EquipmentGeolocationsGeolocationId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentEquipmentGeolocation", x => new { x.EquipmentId, x.EquipmentGeolocationsEquipmentId, x.EquipmentGeolocationsGeolocationId });
                    table.ForeignKey(
                        name: "FK_EquipmentEquipmentGeolocation_EquipmentGeolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                        columns: x => new { x.EquipmentGeolocationsEquipmentId, x.EquipmentGeolocationsGeolocationId },
                        principalSchema: "ControlTower",
                        principalTable: "EquipmentGeolocations",
                        principalColumns: ["EquipmentId", "GeolocationId"],
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EquipmentEquipmentGeolocation_Equipment_EquipmentId",
                        column: x => x.EquipmentId,
                        principalSchema: "ControlTower",
                        principalTable: "Equipment",
                        principalColumn: "EquipmentId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PowerUnitGeolocations_GeolocationId",
                schema: "ControlTower",
                table: "PowerUnitGeolocations",
                column: "GeolocationId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Geolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations",
                columns: ["EquipmentGeolocationsEquipmentId", "EquipmentGeolocationsGeolocationId"]);

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentEquipmentGeolocation_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "EquipmentEquipmentGeolocation",
                columns: ["EquipmentGeolocationsEquipmentId", "EquipmentGeolocationsGeolocationId"]);

            migrationBuilder.AddForeignKey(
                name: "FK_Geolocations_EquipmentGeolocations_EquipmentGeolocationsEquipmentId_EquipmentGeolocationsGeolocationId",
                schema: "ControlTower",
                table: "Geolocations",
                columns: ["EquipmentGeolocationsEquipmentId", "EquipmentGeolocationsGeolocationId"],
                principalSchema: "ControlTower",
                principalTable: "EquipmentGeolocations",
                principalColumns: ["EquipmentId", "GeolocationId"]);
        }
    }
}

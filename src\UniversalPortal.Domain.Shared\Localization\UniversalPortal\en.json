{
  "culture": "en",
  "texts": {
    "AppName": "Control Tower",
    "Menu:home": "Home",
    "Page:dashboard": "Dashboard",
    "Page:graphMenu": "Graph Menu",

    // These are used to form larger phrases, like "Sort by Start Date" or "Inspect Order #12345"
    "Table:sortBy": "Sort by",
    "Table:inspect": "Inspect",

    "Paginator:itemsPerPage": "Records per Page",
    "Paginator:displayedRange": "{0}–{1} of {2}",
    "Paginator:firstPage": "First Page",
    "Paginator:lastPage": "Last Page",
    "Paginator:previousPage": "Previous Page",
    "Paginator:nextPage": "Next Page",

    "Record:lastUpdated": "Debug: Last Updated",
    "Record:importedTime": "Debug: Imported",

    "Order": "Order",
    "Order:orderId": "Order ID",
    "Order:orderNumber": "Order #",
    "Order:startDate": "Start Date",
    "Order:route": "Route",
    "Order:miles": "Miles",
    "Order:companyName": "Supplier",
    "Order:companyId": "Supplier ID",
    // The last date in the current week -- this week's is May 3rd
    "Order:weekEnding": "Week Ending",

    "Stop": "Stop",
    "Stop:stopId": "Stop ID",
    "Stop:stopNumber": "Stop #",
    "Stop:type": "Type",
    "Stop:sequence": "Sequence",
    "Stop:mileage": "Mileage",
    "Stop:scheduledEarliest": "Scheduled Earliest",
    "Stop:arrival": "Arrival",
    "Stop:arrivalStatus": "Arrival Status",
    "Stop:lateArrivalReason": "Late Arrival Reason",
    "Stop:universalLateArrival": "Late Arrival due to Universal?",
    "Stop:scheduledLatest": "Scheduled Latest",
    "Stop:departure": "Departure",
    "Stop:departureStatus": "Departure Status",
    "Stop:lateDepartureReason": "Late Departure Reason",
    "Stop:universalLateDeparture": "Late Departure due to Univeral?",
    "Stop:minutesWaiting": "Minutes Waiting",

    // "how long between when you were supposed to arrive versus when you ACTUALLY arrived"
    "Stop:arrivalDisparityMinutes": "Arrival Disparity (min)",
    "Stop:departureDisparityMinutes": "Departure Disparity (min)",
    "Stop:supplierDelayWarningNeeded": "Supplier Delay Warning Needed?",

    // e.g. "you have 60 minutes to complete this task"
    "Stop:windowTimeMinutes": "Window (min)",
    // e.g. "you took 90 minutes to finish, so you went 30 minutes over-time"
    "Stop:minutesOverTimeWindow": "Over-Window (min)",

    "Contacts": "Contacts",
    "Notes": "Notes",

    "Status": "Status",
    "Status:code0": "Early",
    "Status:code1": "Late",
    "Status:code2": "On-Time",

    "Boolean:true": "True",
    "Boolean:false": "False",
    
    "Chart:select": "Select Graph Type",
    "Chart:locations": "Locations",
    "Chart:dateRange": "Date Range",
    "Chart:generate": "Generate Chart",
    "Chart:allLocations": "Select All",
    "Chart:noLocations": "Deselect All",
    
    "Chart:hours": "Hours",
    "Chart:assets": "Assets",
    "Chart:location": "Location",
    
    // infixed to chart subtitles, e.g. "[Date Range] (as of [Time of Day])"
    "Chart:asOf": "as of",
    
    "Stat:avgDwellTime:Option": "Avg Dwell Time",
    "Stat:avgDwellTime:Title": "Average Dwell Time by Location",
    "Stat:trailerCount:Option": "Trailer Count",
    "Stat:trailerCount:Title": "Current Trailer Count by Location",
    "Stat:avgTimeSinceMove:Option": "Avg Time Since Move",
    "Stat:avgTimeSinceMove:Title": "Average Time Since Move by Location",
    "Stat:24hInactive:Option": "24h Inactive Count",
    "Stat:24hInactive:Title": "Trailers Inactive 24h or More by Location"
  }
}

using UniversalPortal.Localization;

using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace UniversalPortal.Permissions;

public class UniversalPortalPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        context.AddGroup(UniversalPortalPermissions.GroupName);

        //Define your own permissions here. Example:
        //myGroup.AddPermission(UniversalPortalPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<CtpLocalizationResource>(name);
    }
}

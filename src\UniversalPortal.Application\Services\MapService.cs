﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Features;
using NetTopologySuite.Geometries;
using UniversalPortal.DTOs.SemanticData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.Models.LinkedModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class MapService(
    IRepository<Address> addressRepo,
    IRepository<Order> orderRepo,
    IRepository<Stop> stopRepo,
    IRepository<StopEquipment> stopEquipmentRepo,
    IRepository<Geolocation> geolocationRepo,
    IRepository<EquipmentGeolocation> equipmentGeolocationRepo,
    IRepository<Equipment> equipmentRepo,
    IRepository<Geofence> geofenceRepo) : ApplicationService, IMapService
{
    private async Task<FeatureCollection> GetEquipmentLinesByOrder(int id)
    {
        var stops = await stopRepo.GetQueryableAsync();
        var stopEquipment = await stopEquipmentRepo.GetQueryableAsync();
        var equipment = await equipmentRepo.GetQueryableAsync();
        var equipmentGeolocations = await equipmentGeolocationRepo.GetQueryableAsync();
        var geolocations = await geolocationRepo.GetQueryableAsync();

        var queryMinDate = from stp in stops
                           where stp.OrderId == id
                           group stp.Arrival
                               by 1 into grp
                           select grp.Min();

        var queryMaxDate = from stp in stops
                           where stp.OrderId == id
                           group stp.Departure
                               by 1 into grp
                           select grp.Max();

        var equipmentQuery = from eqp in equipment
                             join eqp2stp in stopEquipment
                                 on eqp.EquipmentId equals eqp2stp.EquipmentId
                             join stp in stops
                                 on eqp2stp.StopId equals stp.StopId
                             where stp.OrderId == id
                             select eqp;

        var query = from eqp in equipmentQuery.Distinct()
                    join eqp2geo in equipmentGeolocations
                        on eqp.EquipmentId equals eqp2geo.EquipmentId
                    join geo in geolocations
                        on eqp2geo.GeolocationId equals geo.GeolocationId
                    where geo.GeolocationDate >= queryMinDate.FirstOrDefault()
                    where geo.GeolocationDate <= queryMaxDate.FirstOrDefault()
                    group geo
                        by eqp into grp
                    select new Tuple<Equipment, Geolocation[]>(grp.Key, grp.ToArray());

        List<Tuple<Equipment, Geolocation[]>> results = await query.AsSingleQuery().ToListAsync();

        IEnumerable<Feature> lines = results.Select(grp =>
        {
            Equipment eqp = grp.Item1;
            Geolocation[] geos = grp.Item2;

            Geometry feat;

            if (geos.Length == 1)
            {
                feat = new Point(geos[0].Longitude, geos[0].Latitude);
            }
            else
            {
                feat = new LineString([.. geos.Select(x => new Coordinate(x.Longitude, x.Latitude))]);
            }

            AttributesTable properties = new()
            {
                { "equipmentId", eqp.EquipmentId },
                { "equipmentNumber", eqp.EquipmentNumber },
                { "vin", eqp.Vin }
            };
            return new Feature(feat, properties);
        });

        return [.. lines];
    }

    private static NetTopologySuite.Geometries.Polygon ParsePolygon(Geofence fence)
    {
        List<Coordinate> vertices = [.. fence.ParseCoordinates().Select(x => new Coordinate(x.Item2, x.Item1))];
        vertices.Add(vertices.First());
        LinearRing ring = new([.. vertices]);

        return new(ring);
    }

    private async Task<FeatureCollection> GetGeofencePolygonsByOrder(int id)
    {
        var orders = await orderRepo.GetQueryableAsync();
        var stops = await stopRepo.GetQueryableAsync();
        var geofences = await geofenceRepo.GetQueryableAsync();

        var query = from ord in orders
                    where ord.OrderId == id
                    join stp in stops
                        on ord.OrderId equals stp.OrderId
                    join fen in geofences
                        on stp.AddressId equals fen.AddressId
                    select fen;

        List<Geofence> results = await query.AsSingleQuery().ToListAsync();

        IEnumerable<Feature> polygons = results
            .DistinctBy(x => x.GeofenceId)
            .Where(x => !string.IsNullOrEmpty(x.GeofenceCoordinates))
            .Select(fence =>
            {
                AttributesTable properties = new()
                {
                    { "name", fence.GeofenceName },
                    { "geofenceId", fence.GeofenceId }
                };

                return new Feature(ParsePolygon(fence), properties);
            });

        return [.. polygons];
    }

    private async Task<FeatureCollection> GetGeofencePointsByOrder(int id)
    {
        var orders = await orderRepo.GetQueryableAsync();
        var stops = await stopRepo.GetQueryableAsync();
        var addresses = await addressRepo.GetQueryableAsync();
        var geofences = await geofenceRepo.GetQueryableAsync();

        var query = from ord in orders
                    where ord.OrderId == id
                    join stp in stops
                        on ord.OrderId equals stp.OrderId
                    join fen in geofences
                        on stp.AddressId equals fen.AddressId
                    join adr in addresses
                        on stp.AddressId equals adr.AddressId
                    group fen
                        by adr into grp
                    select new Tuple<Address, Geofence[]>(grp.Key, grp.ToArray());

        IEnumerable<Tuple<Address, Geofence[]>> results = await query.AsSingleQuery().ToListAsync();

        IEnumerable<Feature> points = results.Select(grp =>
        {
            Address address = grp.Item1;
            Geofence[] fences = grp.Item2;

            NetTopologySuite.Geometries.Polygon[] polys = [.. fences.Select(ParsePolygon)];
            MultiPolygon mp = new(polys);
            Point center = mp.Envelope.Centroid;

            AttributesTable properties = new()
            {
                { "displayName", address.DisplayName },
                { "addressId", address.AddressId }
            };

            return new Feature(center, properties);
        });

        return [.. points];
    }

    public async Task<MapFeaturesDto> GetMapFeaturesByOrder(int id)
    {
        // query these features directly
        var equipmentLines = await GetEquipmentLinesByOrder(id);
        var geofencePolygons = await GetGeofencePolygonsByOrder(id);
        var geofencePoints = await GetGeofencePointsByOrder(id);

        // get bounding boxes of all features and use them to construct a single bounding box for ALL features
        IEnumerable<Envelope> allBounds = equipmentLines
            .Concat(geofencePolygons)
            .Select(x => x.Geometry.EnvelopeInternal);

        if (!allBounds.Any())
        {
            return new MapFeaturesDto(equipmentLines, geofencePoints, geofencePolygons, 0, 0, 0, 0);
        }

        double minX = allBounds.Select(x => x.MinX).Min();
        double minY = allBounds.Select(x => x.MinY).Min();

        double maxX = allBounds.Select(x => x.MaxX).Max();
        double maxY = allBounds.Select(x => x.MaxY).Max();

        return new MapFeaturesDto(equipmentLines, geofencePoints, geofencePolygons, minX, minY, maxX, maxY);
    }
}

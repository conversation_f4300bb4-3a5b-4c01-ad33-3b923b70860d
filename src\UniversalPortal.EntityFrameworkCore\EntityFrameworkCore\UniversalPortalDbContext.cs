using Microsoft.EntityFrameworkCore;

using UniversalPortal.Models.DirectModels;
using UniversalPortal.Models.LinkedModels;

using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace UniversalPortal.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ReplaceDbContext(typeof(ITenantManagementDbContext))]
[ConnectionStringName("Default")]
public class UniversalPortalDbContext(DbContextOptions<UniversalPortalDbContext> options) :
    AbpDbContext<UniversalPortalDbContext>(options),
    ITenantManagementDbContext,
    IIdentityDbContext
{
    /* Add DbSet properties for your Aggregate Roots / Entities here. */


    #region Entities from the modules

    /* Notice: We only implemented IIdentityProDbContext and ISaasDbContext
     * and replaced them for this DbContext. This allows you to perform JOIN
     * queries for the entities of these modules over the repositories easily. You
     * typically don't need that for other modules. But, if you need, you can
     * implement the DbContext interface of the needed module and use ReplaceDbContext
     * attribute just like IIdentityProDbContext and ISaasDbContext.
     *
     * More info: Replacing a DbContext of a module ensures that the related module
     * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
     */

    // Identity
    public DbSet<IdentityUser> Users { get; set; }
    public DbSet<IdentityRole> Roles { get; set; }
    public DbSet<IdentityClaimType> ClaimTypes { get; set; }
    public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
    public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
    public DbSet<IdentityLinkUser> LinkUsers { get; set; }
    public DbSet<IdentityUserDelegation> UserDelegations { get; set; }
    public DbSet<IdentitySession> Sessions { get; set; }

    // Tenant Management
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }

    #endregion

    public DbSet<Address> Addresses { get; set; }
    public DbSet<Attachment> Attachments { get; set; }
    public DbSet<Contact> Contacts { get; set; }
    public DbSet<Driver> Drivers { get; set; }
    public DbSet<Equipment> Equipment { get; set; }
    public DbSet<EquipmentGeolocation> EquipmentGeolocations { get; set; }
    public DbSet<EquipmentType> EquipmentTypes { get; set; }
    public DbSet<Geofence> Geofences { get; set; }
    public DbSet<Geolocation> Geolocations { get; set; }
    public DbSet<Leg> Legs { get; set; }
    public DbSet<Note> Notes { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderContact> OrderContacts { get; set; }
    public DbSet<OrderNote> OrderNotes { get; set; }
    public DbSet<PowerUnit> PowerUnits { get; set; }
    public DbSet<PowerUnitGeolocation> PowerUnitGeolocations { get; set; }
    public DbSet<SourceSystem> SourceSystems { get; set; }
    public DbSet<Stop> Stops { get; set; }
    public DbSet<StopContact> StopContacts { get; set; }
    public DbSet<StopEquipment> StopEquipment { get; set; }
    public DbSet<StopNote> StopNotes { get; set; }
    public DbSet<Dwell> Dwell { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigurePermissionManagement();
        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigureIdentity();
        builder.ConfigureOpenIddict();
        builder.ConfigureTenantManagement();
        builder.ConfigureBlobStoring();

        builder.HasDefaultSchema(UniversalPortalConsts.DbSchema);

        builder.Entity<Order>(o =>   
        {
            o.ToTable("Orders", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Stop>(o =>
        {
            o.ToTable("Stops", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<OrderContact>(o =>
        {
            o.HasKey(ck => new {ck.OrderId, ck.ContactId});
            o.ToTable("OrderContacts", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<StopContact>(o =>
        {
            o.HasKey(ck => new { ck.StopId, ck.ContactId } );
            o.ToTable("StopContacts", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Contact>(o =>
        {
            o.ToTable("Contacts", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<OrderNote>(o =>
        {
            o.HasKey(ck => new { ck.OrderId, ck.NoteId } );
            o.ToTable("OrderNotes", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<StopNote>(o =>
        {
            o.HasKey(ck => new { ck.StopId, ck.NoteId } );
            o.ToTable("StopNotes", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Note>(o =>
        {
            o.ToTable("Notes", 
                UniversalPortalConsts.ControlTowerSchema, 
                b => b.IsTemporal(ttb => 
                {
                    ttb.UseHistoryTable("Audit_Notes", UniversalPortalConsts.ControlTowerSchema);
                    ttb.HasPeriodStart("ValidFrom")
                    .HasColumnName("ValidFrom");
                    ttb.HasPeriodEnd("ValidTo")
                    .HasColumnName("ValidTo");
                }));
            o.ConfigureByConvention();
        });

        builder.Entity<Attachment>(o =>
        {
            o.ToTable("Attachments", 
                UniversalPortalConsts.ControlTowerSchema, 
                b => b.IsTemporal(ttb => 
                {
                    ttb.UseHistoryTable("Audit_Attachments", UniversalPortalConsts.ControlTowerSchema);
                    ttb.HasPeriodStart("ValidFrom")
                    .HasColumnName("ValidFrom");
                    ttb.HasPeriodEnd("ValidTo")
                    .HasColumnName("ValidTo");
                }));
            o.ConfigureByConvention();
        });

        builder.Entity<Driver>(o =>
        {
            o.ToTable("Drivers", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Equipment>(o =>
        {
            o.ToTable("Equipment", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<EquipmentGeolocation>(o =>
        {
            o.HasKey(ck => new { ck.EquipmentId, ck.GeolocationId } );
            o.ToTable("EquipmentGeolocations", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<EquipmentType>(o =>
        {
            o.ToTable("EquipmentTypes", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Geolocation>(o =>
        {
            o.ToTable("Geolocations", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Leg>(o =>
        {
            o.ToTable("Legs", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<PowerUnit>(o =>
        {
            o.ToTable("PowerUnits", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<PowerUnitGeolocation>(o =>
        {
            o.HasKey(ck => new { ck.PowerUnitId, ck.GeolocationId } );
            o.ToTable("PowerUnitGeolocations", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<StopEquipment>(o =>
        {
            o.HasKey(ck => new { ck.StopId, ck.EquipmentId } );
            o.ToTable("StopEquipment", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Geofence>(o =>
        {
            o.ToTable("Geofences", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Address>(o =>
        {
            o.ToTable("Addresses", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<SourceSystem>(o =>
        {
            o.ToTable("SourceSystems", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });

        builder.Entity<Dwell>(o =>
        {
            o.ToTable("Dwell", UniversalPortalConsts.ControlTowerSchema);
            o.ConfigureByConvention();
        });
    }
}

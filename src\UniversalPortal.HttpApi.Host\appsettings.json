{"App": {"SelfUrl": "http://controltower.ulhdev.com", "AngularUrl": "http://localhost:4200", "CorsOrigins": "https://*.UniversalPortal.com,http://localhost:4200,http://localhost:44369/,https://*.goutsi.com,https://controltower.ulhdev.com,https://controltowerapi.ulhdev.com,http://controltower.ulhdev.com", "RedirectAllowedUrls": "http://localhost:4200", "DisablePII": false, "HealthCheckUrl": "/health-status", "UsePhysicalFiles": false}, "ConnectionStrings": {"Default": "Server=dv-atlassql;Database=ControlTower;Trusted_Connection=True;TrustServerCertificate=true", "UniversalPortalDb": "Server=dv-atlassql;Database=ControlTower;Trusted_Connection=True;Encrypt=False;"}, "AuthServer": {"Authority": "https://localhost:44369", "RequireHttpsMetadata": true, "SwaggerClientId": "UniversalPortal_Swagger", "CertificatePassPhrase": "b80e8beb-ab94-4986-a55e-70c8b0f6095f"}, "StringEncryption": {"DefaultPassPhrase": "WxSVqetBp4V1T5OJ"}}
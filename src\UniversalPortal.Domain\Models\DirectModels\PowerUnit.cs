﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class PowerUnit : TenantAuditedEntity
{
    public PowerUnit()
    {
        Legs = [];
        PowerUnitGeolocations = [];
    }

    [Key]
    public int PowerUnitId { get; set; }
    public string? Vin {  get; set; }
    public string? PowerUnitCode { get; set; }
    public DateTime ExpirationDate { get; set; }
    // I'm not sure what all what we'd want in this table.

    [InverseProperty(nameof(Leg.PowerUnit))]
    public ICollection<Leg> Legs { get; set; }
    [InverseProperty(nameof(PowerUnitGeolocation.PowerUnit))]
    public ICollection<PowerUnitGeolocation> PowerUnitGeolocations { get; set; }

    public override object[] GetKeys()
    {
        return [PowerUnitId];
    }
}
﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>UniversalPortal</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\UniversalPortal.Domain.Shared\UniversalPortal.Domain.Shared.csproj" />
    <ProjectReference Include="..\UniversalPortal.Domain\UniversalPortal.Domain.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="NetTopologySuite.IO.GeoJSON" Version="4.0.0" />
    <PackageReference Include="NetTopologySuite.IO.GeoJSON4STJ" Version="4.0.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="9.0.4" />
  </ItemGroup>

</Project>

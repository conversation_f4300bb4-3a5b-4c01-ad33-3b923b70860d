import { Injectable } from "@angular/core";
import { OrderDto } from "../../proxy/dtos/direct-data";
import { RecordFormatter } from "../record-formatter";
import { OrderRecord } from "../records/order.record";

@Injectable({
  providedIn: 'root'
})
export class OrderFormatter extends RecordFormatter<OrderRecord, OrderDto> {

  protected fieldFormatters = new Map<string, (rec: OrderRecord) => string>([

    ['Order:startDate', (rec) => this.formatCalendarDate(new Date(rec.localData.startDate))],
    ['weekEnding', (rec) => this.formatCalendarDate(rec.weekEnding)],

    ['lastUpdated', (rec) => this.formatPreciseDate(rec.lastUpdated)],
    ['importedTime', (rec) => this.formatPreciseDate(rec.importedTime)],
  ]);

  private formatPreciseDate(date?: Date): string {
    return this.formatLocalDate(date, 'MMM d\nHH:mm:ss.SSS');
  }

  private formatCalendarDate(date: Date): string {
    return this.formatLocalDate(date, 'MMM d, yyyy');
  }
}

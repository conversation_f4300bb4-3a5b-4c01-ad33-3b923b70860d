import { Injectable } from "@angular/core";
import { StopDto } from "../../proxy/dtos/direct-data";
import { RecordFormatter } from "../record-formatter";
import { StopRecord } from "../records/stop.record";

@Injectable({
  providedIn: 'root'
})
export class StopFormatter extends RecordFormatter<StopRecord, StopDto> {

  protected fieldFormatters = new Map<string, (rec: StopRecord) => string>([

    ['Order:orderNumber', (rec) => rec.order?.localData.orderNumber.toString()],
    ['Order:startDate', (rec) => rec.order ? this.formatCalendarDate(new Date(rec.order?.localData.startDate)) : RecordFormatter.noValue],
    ['Order:route', (rec) => rec.order?.localData.route],
    ['Order:miles', (rec) => rec.order?.localData.miles.toString()],
    ['Order:companyName', (rec) => rec.order?.localData.companyName],
    ['Order:companyId', (rec) => rec.order?.localData.companyId],
    ['Order:weekEnding', (rec) => this.formatCalendarDate(rec.order?.weekEnding)],

    ['scheduledEarliest', (rec) => this.formatDateTime(new Date(rec.localData.scheduledEarliest))],
    ['arrival', (rec) => this.formatDateTime(new Date(rec.localData.arrival))],
    ['arrivalStatus', (rec) => this.formatStatusCode(rec.localData.arrivalStatus)],
    ['universalLateArrival', (rec) => this.formatBinary(rec.localData.universalLateArrival != 0)],

    ['scheduledLatest', (rec) => this.formatDateTime(new Date(rec.localData.scheduledLatest))],
    ['departure', (rec) => this.formatDateTime(new Date(rec.localData.departure))],
    ['departureStatus', (rec) => this.formatStatusCode(rec.localData.departureStatus)],
    ['universalLateDeparture', (rec) => this.formatBinary(rec.localData.universalLateDeparture != 0)],

    ['supplierDelayWarningNeeded', (rec) => this.formatBinary(rec.supplierDelayWarningNeeded)],

    ['lastUpdated', (rec) => this.formatPreciseDate(rec.lastUpdated)],
    ['importedTime', (rec) => this.formatPreciseDate(rec.importedTime)],
  ]);

  private formatPreciseDate(date?: Date): string {
    return this.formatLocalDate(date, 'MMM d\nHH:mm:ss.SSS');
  }

  private formatCalendarDate(date: Date): string {
    return this.formatLocalDate(date, 'MMM d, yyyy');
  }

  private formatDateTime(date: Date): string {
    return this.formatLocalDate(date, 'MMM d, yyyy\n h:mm a');
  }

  private formatStatusCode(code: number) {
    return this.formatStaticValue(`Status:code${code}`);
  }

  private formatBinary(bool: boolean): string {
    return this.formatStaticValue(`Boolean:${bool ? 'true' : 'false'}`);
  }

  private statusCodes = new Map<number, string>([
    [0, "early"],
    [1, "late"],
    [2, "On-Time"]
  ])

}

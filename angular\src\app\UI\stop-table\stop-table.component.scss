//---------------------------------------------------
// Table
//------------

// Wraps the central table
.table-container {
  width: auto;
  height: auto;
  max-height: 80vh;

  overflow-y: scroll;
  overflow-x: scroll;

  border: 2px solid orangeRed;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;

  vertical-align: middle;
}

table th {
  color: yellow;
  background-color: teal;
  position: sticky;
  text-align: center;
  padding: 10px;

  align-content: center;

  font-weight: bold;

  border-top: 2px solid black;
  border-bottom: 2px solid black;
  border-right: 2px solid black;
}

table th:first-child {
  border-left: 2px solid black; //Paint left-border only onto left-most header cell
}

table tr {
    height: 100%;
}

table td {
  padding-left: 2px;
  padding-right: 2px;
  padding-top: 1px;
  padding-bottom: 1px;

  border-bottom: 2px solid #ddd;
  border-right: 2px solid #ddd;
}

table td:first-child {
  border-left: 2px solid #ddd; //Paint left-border only onto left-most cell in each row
}

.mat-mdc-cell {
  white-space: pre-line;
  justify-content: center;
}

.mat-mdc-header-row {
  font-weight: bold;
  font-size: large;
}

// Assymetrical weirdness here is to balance the headings against the Sorting Arrow
::ng-deep .mat-sort-header-container {
    padding-left: 18px;
    padding-right: 0px;
    justify-content: center;
}

:host {
  --mat-sort-arrow-color: orangeRed;
}

//---------------------------------------------------
// Per-Column Formatting
//-----------------------
td.mat-column-Stop-stopId, td.mat-column-Order-orderId,
td.mat-column-Record-lastUpdated, td.mat-column-Record-importedTime {
  background-color: olive;
  text-align: center;
  min-width: 115px;
  font-weight: bolder;
}

td.mat-column-Stop-stopNumber,
td.mat-column-Order-orderNumber {
  font-weight: bold;
  text-align: center;
}

button .inline-button {
  justify-items: center;
  justify-content: center;
  border: none;
}

td.mat-column-Order-startDate,
td.mat-column-Stop-scheduledEarliest, td.mat-column-Stop-arrival,
td.mat-column-Stop-scheduledLatest, td.mat-column-Stop-departure,
td.mat-column-Order-weekEnding {
  min-width: 115px;
  text-align: center;
  font-weight: lighter;
}

td.mat-column-Stop-arrivalDisparityMinutes, td.mat-column-Stop-departureDisparityMinutes,
td.mat-column-Stop-minutesWaiting, td.mat-column-Stop-minutesOverTimeWindow, td.mat-column-Stop-windowTimeMinutes {
  min-width: 145px;
  text-align: center;
}

td.mat-column-Order-route,
td.mat-column-Order-companyId, td.mat-column-Order-companyName,
td.mat-column-Stop-type,
td.mat-column-Stop-lateArrivalReason, td.mat-column-Stop-lateDepartureReason,
td.mat-column-Stop-supplierDelayWarningNeeded {
  font-size: 9pt;
  text-align: center;
  font-weight: 500;
}

 td.mat-column-Order-companyName {
  min-width: 175px;
}

td.mat-column-Order-miles, td.mat-column-Stop-sequence,
td.mat-column-Stop-mileage,
td.mat-column-Stop-arrivalStatus, td.mat-column-Stop-departureStatus,
td.mat-column-Stop-universalLateArrival, td.mat-column-Stop-universalLateDeparture,
td.mat-column-Stop-supplierDelayWarningNeeded {
  text-align: center;
  font-weight: lighter;
}

//---------------------------------------------------
// Alternating Row Colors
//------------------------

.mat-mdc-row {
    background-color: azure;
}

.mat-mdc-row:nth-child(2n+1) {
  background-color: bisque;
}

//---------------------------------------------------
// Row Hover Highlighting
//------------------------

.mat-mdc-row {
  //transition: background-color 0.15s ease-out;
  transition: background-color 0.15s ease-out;
}

.mat-mdc-row:hover {
  //background-color: lightblue;
  background-color: lightBlue;
  ;
}

//---------------------------------------------------
// Page Footer Contents
//----------------------

.dash-footer {
  display: flex;
  flex-direction: row-reverse;
}

.pagination {
  display: inline-block;
}

.pagination a {
  color: black;
  float: left;
  padding: 8px 16px;
  text-decoration: none;
}

.pagination a.active {
  background-color: #4CAF50;
  color: white;
}

.pagination a:hover:not(.active) {
  background-color: #ddd;
}

mat-paginator {
  //position: fixed;
  bottom: 0;
  right: 0;
  background-color: #f9f9f9;
}

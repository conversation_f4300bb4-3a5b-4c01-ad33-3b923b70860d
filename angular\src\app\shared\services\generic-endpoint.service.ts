import { Rest } from '@abp/ng.core';
import type { AuditedEntityDto, PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Observable } from 'rxjs';

export interface GenericEndpoint<T extends AuditedEntityDto<string>> {
  apiName;

  create: (input: T, config?: Partial<Rest.Config>) => Observable<T>;
  delete: (id: number, config?: Partial<Rest.Config>) => Observable<void>;
  get: (id: number, config?: Partial<Rest.Config>) => Observable<T>;
  getList: (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) => Observable<PagedResultDto<T>>;
  update: (id: number, input: T, config?: Partial<Rest.Config>) => Observable<T>;
}

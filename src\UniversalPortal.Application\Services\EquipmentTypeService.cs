﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class EquipmentTypeService(IRepository<EquipmentType> repository) : ApplicationService, IEquipmentTypeService
{
    public async Task<EquipmentTypeDto> CreateAsync(EquipmentTypeDto input)
    {
        var driver = ObjectMapper.Map<EquipmentTypeDto, EquipmentType>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<EquipmentType, EquipmentTypeDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.EquipmentTypeId == id);
    }

    public async Task<EquipmentTypeDto> GetAsync(int id)
    {
        var equipmentType = await repository.GetAsync(x => x.EquipmentTypeId == id);
        return ObjectMapper.Map<EquipmentType, EquipmentTypeDto>(equipmentType);
    }

    public async Task<PagedResultDto<EquipmentTypeDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var equipmentTypes = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<EquipmentTypeDto>(
            totalCount,
            ObjectMapper.Map<List<EquipmentType>, List<EquipmentTypeDto>>(equipmentTypes)
        );
    }

    public Task<EquipmentTypeDto> UpdateAsync(int id, EquipmentTypeDto input)
    {
        throw new NotImplementedException();
    }
}
section {
    display: flex;
    justify-content: space-around;
    width: 100%;
}

section.vertical-layout {
    flex-direction: column;
    align-items: center;
}

.graph-menu {
    display: flex;
    flex-direction: column;
}

.radio {
    display: flex;
    flex-direction: column;
}

.radio-group {
    display: flex;
    justify-content: space-around;
}

.controls {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-content: center;
}

mat-divider {
    margin-top: 20px;
    margin-bottom: 20px;
}

.singleControl {
  padding-left: 20px;
  padding-right: 20px;
}

.canvas {
    width: 90%;
    display: flex;
    justify-content: center;
}

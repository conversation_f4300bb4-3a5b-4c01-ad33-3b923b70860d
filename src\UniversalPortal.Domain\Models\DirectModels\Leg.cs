﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Leg : TenantAuditedEntity
{
    public Leg()
    {
        Stops = [];
    }

    [Key]
    public int LegId { get; set; }
    public int LegNumber { get;set; }
    public int MoveNumber { get; set; }
    public int? Driver1Id { get; set; }
    public int? Driver2Id { get; set; }
    public int PowerUnitId { get; set; }
    [InverseProperty(nameof(Stop.Leg))]
    public ICollection<Stop> Stops { get; set; }

    // I'm not exactly sure how to represent this.
    // the relationships don't seem quite right.
    // ...
    // it built we'll go with this for now, hopefully 
    // no issues with deletion cascades and
    // circular references.
    
    [InverseProperty(nameof(Driver.Driver1Legs))]
    public Driver? Driver1 { get; set; }
    [InverseProperty(nameof(Driver.Driver2Legs))]
    public Driver? Driver2 { get; set; }
    [InverseProperty(nameof(DirectModels.PowerUnit.Legs))]
    public PowerUnit? PowerUnit { get; set; }

    public override object[] GetKeys()
    {
        return [LegId];
    }
}
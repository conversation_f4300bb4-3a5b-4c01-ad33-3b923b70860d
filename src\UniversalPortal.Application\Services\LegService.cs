﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class LegService(IRepository<Leg> repository) : ApplicationService, ILegService
{
    public async Task<LegDto> CreateAsync(LegDto input)
    {
        var driver = ObjectMapper.Map<LegDto, Leg>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Leg, LegDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.LegId == id);
    }

    public async Task<LegDto> GetAsync(int id)
    {
        var leg = await repository.GetAsync(x => x.LegId == id);
        return ObjectMapper.Map<Leg, LegDto>(leg);
    }

    public async Task<PagedResultDto<LegDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var legs = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<LegDto>(
            totalCount,
            ObjectMapper.Map<List<Leg>, List<LegDto>>(legs)
        );
    }

    public Task<LegDto> UpdateAsync(int id, LegDto input)
    {
        throw new NotImplementedException();
    }
}
﻿using System;
using System.Collections.Generic;
using UniversalPortal.DTOs.LinkedData;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.DirectData;

public class StopDto : AuditedEntityDto
{
    public StopDto()
    {
        StopContacts = [];
        StopNotes = [];
        StopEquipment = [];
        EquipmentIds = [];
    }

    public int StopId { get; set; }
    public int StopNumber { get; set; }
    public int OrderId { get; set; }
    public int MoveNumber {  get; set; }
    public required string Type { get; set; }
    public int Sequence { get; set; }
    public int Mileage { get; set; }
    public DateTime ScheduledEarliest { get; set; }
    public DateTime Arrival { get; set; }
    public int ArrivalStatus { get; set; }
    public string? LateArrivalReason { get; set; }
    public int UniversalLateArrival {  get; set; }
    public DateTime ScheduledLatest { get; set; }
    public DateTime Departure { get; set; }
    public int DepartureStatus { get; set; }
    public string? LateDepartureReason { get; set; }
    public int UniversalLateDeparture { get; set; }

    public ICollection<StopContactDto> StopContacts { get; set; }
    public ICollection<StopNoteDto> StopNotes { get; set; }
    public ICollection<StopEquipmentDto> StopEquipment { get; set; }
    public ICollection<int> EquipmentIds { get; set; }

    public AddressDto? Address { get; set; }
}
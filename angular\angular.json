{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"UniversalPortal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@ngx-env/builder:browser", "options": {"outputPath": "dist/UniversalPortal", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["chart.js", "js-sha256"], "assets": ["src/favicon.ico", "src/assets", {"glob": "web.config", "input": ".", "output": "."}], "styles": [{"input": "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "inject": true, "bundleName": "fontawesome-all.min"}, {"input": "node_modules/@fortawesome/fontawesome-free/css/v4-shims.min.css", "inject": true, "bundleName": "fontawesome-v4-shims.min"}, {"input": "node_modules/@swimlane/ngx-datatable/index.css", "inject": true, "bundleName": "ngx-datatable-index"}, {"input": "node_modules/@swimlane/ngx-datatable/assets/icons.css", "inject": true, "bundleName": "ngx-datatable-icons"}, {"input": "node_modules/@swimlane/ngx-datatable/themes/material.css", "inject": true, "bundleName": "ngx-datatable-material"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/bootstrap-dim.css", "inject": false, "bundleName": "bootstrap-dim"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/ng-bundle.css", "inject": false, "bundleName": "ng-bundle"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/side-menu/layout-bundle.css", "inject": false, "bundleName": "layout-bundle"}, {"input": "node_modules/@abp/ng.theme.lepton-x/assets/css/abp-bundle.css", "inject": false, "bundleName": "abp-bundle"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/bootstrap-dim.rtl.css", "inject": false, "bundleName": "bootstrap-dim.rtl"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/font-bundle.css", "inject": false, "bundleName": "font-bundle"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/font-bundle.rtl.css", "inject": false, "bundleName": "font-bundle.rtl"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/ng-bundle.rtl.css", "inject": false, "bundleName": "ng-bundle.rtl"}, {"input": "node_modules/@volo/ngx-lepton-x.lite/assets/css/side-menu/layout-bundle.rtl.css", "inject": false, "bundleName": "layout-bundle.rtl"}, {"input": "node_modules/@abp/ng.theme.lepton-x/assets/css/abp-bundle.rtl.css", "inject": false, "bundleName": "abp-bundle.rtl"}, {"input": "node_modules/bootstrap-icons/font/bootstrap-icons.css", "inject": true, "bundleName": "bootstrap-icons"}, "src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "2.5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "100kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "dev": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "2.5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "100kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@ngx-env/builder:dev-server", "configurations": {"production": {"buildTarget": "UniversalPortal:build:production"}, "development": {"buildTarget": "UniversalPortal:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@ngx-env/builder:extract-i18n", "options": {"buildTarget": "UniversalPortal:build"}}, "test": {"builder": "@ngx-env/builder:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}
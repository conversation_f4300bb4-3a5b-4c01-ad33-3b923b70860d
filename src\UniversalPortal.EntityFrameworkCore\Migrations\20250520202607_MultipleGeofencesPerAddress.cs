﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class MultipleGeofencesPerAddress : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Addresses_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Contacts_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Drivers_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_EquipmentTypes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeign<PERSON>ey(
                name: "FK_SourceSystems_Equipment_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Geofences_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Geolocations_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Legs_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Notes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Orders_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_PowerUnits_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Stops_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropIndex(
                name: "IX_Addresses_GeofenceId",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.DropColumn(
                name: "GeofenceId",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.CreateIndex(
                name: "IX_Geofences_AddressId",
                schema: "ControlTower",
                table: "Geofences",
                column: "AddressId");

            migrationBuilder.AddForeignKey(
                name: "FK_Geofences_Addresses_AddressId",
                schema: "ControlTower",
                table: "Geofences",
                column: "AddressId",
                principalSchema: "ControlTower",
                principalTable: "Addresses",
                principalColumn: "AddressId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Addresses_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Addresses",
                principalColumn: "AddressId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Contacts_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Contacts",
                principalColumn: "ContactId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Drivers_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Drivers",
                principalColumn: "DriverId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_EquipmentTypes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "EquipmentTypes",
                principalColumn: "EquipmentTypeId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Geolocations_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Legs_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Legs",
                principalColumn: "LegId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Notes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Notes",
                principalColumn: "NoteId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Orders_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Orders",
                principalColumn: "OrderId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Geofences_Addresses_AddressId",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Addresses_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Contacts_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Drivers_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_EquipmentTypes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Equipment_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Geofences_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Geolocations_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Legs_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Notes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Orders_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_PowerUnits_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropIndex(
                name: "IX_Geofences_AddressId",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.AddColumn<int>(
                name: "GeofenceId",
                schema: "ControlTower",
                table: "Addresses",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_Addresses_GeofenceId",
                schema: "ControlTower",
                table: "Addresses",
                column: "GeofenceId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Addresses_Geofences_GeofenceId",
                schema: "ControlTower",
                table: "Addresses",
                column: "GeofenceId",
                principalSchema: "ControlTower",
                principalTable: "Geofences",
                principalColumn: "GeofenceId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Addresses_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Addresses",
                principalColumn: "AddressId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Contacts_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Contacts",
                principalColumn: "ContactId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Drivers_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Drivers",
                principalColumn: "DriverId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_EquipmentTypes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "EquipmentTypes",
                principalColumn: "EquipmentTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Equipment_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Equipment",
                principalColumn: "EquipmentId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Geofences_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Geofences",
                principalColumn: "GeofenceId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Geolocations_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Geolocations",
                principalColumn: "GeolocationId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Legs_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Legs",
                principalColumn: "LegId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Notes_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Notes",
                principalColumn: "NoteId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Orders_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Orders",
                principalColumn: "OrderId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_PowerUnits_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "PowerUnits",
                principalColumn: "PowerUnitId");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Stops_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Stops",
                principalColumn: "StopId");
        }
    }
}

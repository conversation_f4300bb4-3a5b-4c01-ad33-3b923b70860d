﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Driver : TenantAuditedEntity
{
    public Driver()
    {
        Driver1Legs = [];
        Driver2Legs = [];
    }

    [Key]
    public int DriverId { get; set; }
    public int DriverNumber { get; set; }
    public required string Name { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }

    [InverseProperty(nameof(Leg.Driver1))]
    public ICollection<Leg> Driver1Legs { get; set; }
    [InverseProperty(nameof(Leg.Driver2))]
    public ICollection<Leg> Driver2Legs { get; set; }

    public override object[] GetKeys()
    {
        return [DriverId];
    }
}
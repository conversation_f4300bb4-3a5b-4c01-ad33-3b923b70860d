import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { GeofenceDto } from '../dtos/direct-data/models';

@Injectable({
  providedIn: 'root',
})
export class GeofenceService {
  apiName = 'Default';
  

  create = (input: GeofenceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeofenceDto>({
      method: 'POST',
      url: '/api/app/geofence',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/geofence/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeofenceDto>({
      method: 'GET',
      url: `/api/app/geofence/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<GeofenceDto>>({
      method: 'GET',
      url: '/api/app/geofence',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: GeofenceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeofenceDto>({
      method: 'PUT',
      url: `/api/app/geofence/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

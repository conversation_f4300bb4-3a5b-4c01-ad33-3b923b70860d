import { StopDto } from "../../proxy/dtos/direct-data";
import { LocalRecord } from '../local-record';
import { OrderCache } from '../caches/order.cache';
import { OrderRecord } from './order.record';

/**
 * Represents a locally-cached instance of a single Stop.
 */
export class StopRecord extends LocalRecord<StopDto> {
  constructor(
    dto: StopDto,
    fromSignalRGroup: string,
    private readonly orderCache: OrderCache
  ) {
    super(dto, fromSignalRGroup);
    this.calculateDerivedFields(dto);
  }

  public order: OrderRecord;

  // Derived fields
  public arrivalDateTime: Date;
  public departureDateTime: Date;

  public minutesWaiting: number;
  public arrivalDisparityMinutes: number;
  public departureDisparityMinutes: number;
  public supplierDelayWarningNeeded: boolean;
  public windowTimeMinutes: number;
  public minutesOverTimeWindow: number;

  public subsumes(otherDto: StopDto): boolean {
    return this.localData.stopId == otherDto.stopId;
  }

  public getPrimaryId(): number {
    return this.localData.stopId;
  }

  protected override calculateDerivedFields(dto: StopDto) {

    this.queryTertiaryRecord(dto.orderId, this.orderCache, rec => this.order = rec);

    this.setDerivedField('arrivalDateTime', new Date(dto.arrival));
    this.setDerivedField('departureDateTime', new Date(dto.departure));
    this.setDerivedField('minutesWaiting', dto.arrivalStatus == 0
      ? (new Date(dto.scheduledEarliest).getMinutes() - new Date(dto.arrival).getMinutes() + 15)
      : dto.arrivalStatus == 1
        ? (new Date(dto.scheduledEarliest).getMinutes() - new Date(dto.arrival).getMinutes() - 15) * -1
        : 0);
    this.setDerivedField('arrivalDisparityMinutes', StopRecord.minutesBetween(new Date(dto.arrival), new Date(dto.scheduledEarliest)));
    this.setDerivedField('departureDisparityMinutes', StopRecord.minutesBetween(new Date(dto.departure), new Date(dto.scheduledLatest)));
    this.setDerivedField('supplierDelayWarningNeeded', this.departureDisparityMinutes > 15 && dto.lateDepartureReason == "Supplier");
    this.setDerivedField('windowTimeMinutes', StopRecord.minutesBetween(new Date(dto.scheduledLatest), new Date(dto.scheduledEarliest)));
    this.setDerivedField('minutesOverTimeWindow', this.arrivalDisparityMinutes > 1
      ? this.minutesWaiting - this.windowTimeMinutes
      : this.departureDisparityMinutes);

  }

  private static minutesBetween(endTime: Date, startTime: Date): number {
    return (endTime.valueOf() - startTime.valueOf()) / 1000 / 60;
  }
}

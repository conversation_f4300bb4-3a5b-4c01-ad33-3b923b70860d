<mat-card #card
          class="order-card"
          appearance="outlined"
          [@highlightChanges]="displayOrder?.lastUpdated.getTime() ?? false">
  <mat-card-header class="order-card-header">
    <mat-card-title class="order-card-title">{{ '::Order:orderNumber' | abpLocalization }}{{ formatOrderNumber() }}</mat-card-title>
  </mat-card-header>
  <mat-card-content class="order-card-content">
    <table class="order-field-table">
      <colgroup>
        <col class="title-col" />
        <col class="field-col" />
      </colgroup>
      <tr>
        <td>{{ '::Order:startDate' | abpLocalization }}:</td>
        <td>{{ displayOrder?.localData.startDate | date: 'MMM dd, yyyy' }}</td>
      </tr>
      <tr>
        <td>{{ '::Order:route' | abpLocalization }}:</td>
        <td>{{ displayOrder?.localData.route }}</td>
      </tr>
      <tr>
        <td>{{ '::Order:miles' | abpLocalization }}:</td>
        <td>{{ displayOrder?.localData.miles }}</td>
      </tr>
    </table>
  </mat-card-content>
  <mat-card-footer class="order-card-footer">
    <mat-chip-set aria-label="Addtional order information">
      <mat-chip>{{ '::Contacts' | abpLocalization }}</mat-chip>
      <mat-chip>{{ '::Notes' | abpLocalization }}</mat-chip>
    </mat-chip-set>
  </mat-card-footer>
</mat-card>

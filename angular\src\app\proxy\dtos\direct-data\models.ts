import type { AddressContactDto, EquipmentGeolocationDto, OrderContactDto, OrderNoteDto, PowerUnitGeolocationDto, StopContactDto, StopEquipmentDto } from '../linked-data/models';
import type { AuditedEntityDto } from '@abp/ng.core';
import type { GeolocationCoordinate } from '../../models/models';

export interface AddressDto {
  addressId: number;
  addressLine1?: string;
  addressLine2?: string;
  displayName?: string;
  country?: string;
  state?: string;
  timeZone?: string;
  zip?: string;
  geofences: GeofenceDto[];
  addressContacts: AddressContactDto[];
  geofenceIds: number[];
}

export interface ContactDto extends AuditedEntityDto {
  contactId: number;
  addressId?: number;
  contactNumber: number;
  name?: string;
  phoneNumber?: string;
  email?: string;
}

export interface DriverDto extends AuditedEntityDto {
  driverId: number;
  driverNumber: number;
  name?: string;
  phoneNumber?: string;
  email?: string;
}

export interface EquipmentDto extends AuditedEntityDto {
  equipmentId: number;
  equipmentNumber?: string;
  vin?: string;
  expirationDate?: string;
  equipmentTypeId: number;
  description?: string;
  equipmentType: EquipmentTypeDto;
  equipmentGeolocations: EquipmentGeolocationDto[];
  geolocationIds: number[];
  stopIds: number[];
}

export interface EquipmentTypeDto extends AuditedEntityDto {
  equipmentTypeId: number;
  equipmentTypeName?: string;
  description?: string;
}

export interface GeofenceDto {
  geofenceId: number;
  geolocationId: number;
  geofenceName?: string;
  addressId: number;
  geofenceCoordinates?: string;
  center: GeolocationCoordinate;
}

export interface GeolocationDto extends AuditedEntityDto {
  geolocationId: number;
  latitude: number;
  longitude: number;
  geolocationDate?: string;
  equipmentIds: number[];
}

export interface LegDto extends AuditedEntityDto {
  legId: number;
  legNumber: number;
  moveNumber: number;
  driver1Id?: number;
  driver2Id?: number;
  powerUnitId?: number;
  stops: StopDto[];
  driver1: DriverDto;
  driver2: DriverDto;
  powerUnit: PowerUnitDto;
}

export interface NoteDto extends AuditedEntityDto {
  noteId: number;
  content?: string;
  orderNotes: OrderNoteDto[];
  stopNotes: StopNoteDto[];
}

export interface OrderDto extends AuditedEntityDto {
  orderId: number;
  orderNumber: number;
  moveNumber: number;
  startDate?: string;
  route?: string;
  miles: number;
  companyId?: string;
  companyName?: string;
  orderContacts: OrderContactDto[];
  orderNotes: OrderNoteDto[];
  stops: StopDto[];
}

export interface PowerUnitDto extends AuditedEntityDto {
  powerUnitId: number;
  vin?: string;
  powerUnitCode?: string;
  expirationDate?: string;
  powerUnitGeolocations: PowerUnitGeolocationDto[];
}

export interface SourceSystemDto {
  sourceSystemId: number;
  name?: string;
  description?: string;
}

export interface StopDto extends AuditedEntityDto {
  stopId: number;
  stopNumber: number;
  orderId: number;
  moveNumber: number;
  type?: string;
  sequence: number;
  mileage: number;
  scheduledEarliest?: string;
  arrival?: string;
  arrivalStatus: number;
  lateArrivalReason?: string;
  universalLateArrival: number;
  scheduledLatest?: string;
  departure?: string;
  departureStatus: number;
  lateDepartureReason?: string;
  universalLateDeparture: number;
  stopContacts: StopContactDto[];
  stopNotes: StopNoteDto[];
  stopEquipment: StopEquipmentDto[];
  equipmentIds: number[];
  address: AddressDto;
}

export interface StopNoteDto extends AuditedEntityDto {
  stopId: number;
  noteId: number;
  note: NoteDto;
}

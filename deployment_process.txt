run "dotnet publish ./src/UniversalPortal.HttpApi.Host/UniversalPortal.HttpApi.Host.csproj -c Release -o ./publish/controltower"
from th econsole run ng build --configuration=<env>

Servers:
Dev: \\dvatlasops\WebsiteUploads\ControlTower\
Test: TBD
Staging: TBD
Production TBD

copy the generated files to the server

copy the following projects to \\dvatlasops\WebsiteUploads\ControlTower\ as-is (probably zip them up)
	UniversalPortal.HttpApi.Host
	UniversalPortal.HttpApi.Client
	UniversalPortal.HttpApi
	UniversalPortal.EntityFrameworkCore
	UniversalPortal.Domain.Shared
	UniversalPortal.Domain
	UniversalPortal.Application.Contracts
	UniversalPortal.Application

Shut down IIS application pools and applications.

clear out *\Website\ControlTower and then copy the files to the location. Verify the applications still point to the correct folders.

Start up the application pools, then start up the application and run smoke tests to verify the app is working as expected and on the correct version.
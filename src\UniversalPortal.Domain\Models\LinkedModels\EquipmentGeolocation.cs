﻿using Microsoft.EntityFrameworkCore;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.DirectModels;
using Volo.Abp.Domain.Entities.Auditing;

namespace UniversalPortal.Models.LinkedModels;

[PrimaryKey(nameof(EquipmentId), nameof(GeolocationId))]
public class EquipmentGeolocation : AuditedEntity
{
    [Key, Column(Order = 0)]
    public int EquipmentId { get; set; }
    [Key, Column(Order = 1)]
    public int GeolocationId { get; set; }
    [InverseProperty(nameof(DirectModels.Equipment.EquipmentGeolocations))]
    public Equipment? Equipment { get; set; }
    [InverseProperty(nameof(DirectModels.Geolocation.EquipmentGeolocations))]
    public Geolocation? Geolocation { get; set; }

    public override object[] GetKeys()
    {
        return [EquipmentId, GeolocationId];
    }
}
import { NgModule } from '@angular/core';
import { PageModule } from '@abp/ng.components/page';
import { SharedModule } from '../../shared/shared.module';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardComponent } from './dashboard.component';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { ThemeLeptonXModule } from '@abp/ng.theme.lepton-x';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { StopTableModule } from '../../UI/stop-table/stop-table.module';
import { OrderCardModule } from '../../UI/order-card/order-card.module';
import { GeoMapModule } from '../../UI/geo-map/geo-map.module';

@NgModule({
  declarations: [
    DashboardComponent
  ],
  imports: [
    SharedModule,
    DashboardRoutingModule,
    PageModule,
    MatPaginatorModule,
    MatTableModule,
    MatSortModule,
    ThemeLeptonXModule.forRoot(),
    MatIconModule,
    MatButtonModule,
    MatDividerModule,
    StopTableModule,
    OrderCardModule,
    GeoMapModule,
  ],
})
export class DashboardModule { }

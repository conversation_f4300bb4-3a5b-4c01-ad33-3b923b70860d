﻿using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using UniversalPortal.DTOs.DirectData;
using UniversalPortal.Models.DirectModels;
using UniversalPortal.ServiceInterfaces;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace UniversalPortal.Services;

public class GeolocationService(IRepository<Geolocation> repository) : ApplicationService, IGeolocationService
{
    public async Task<GeolocationDto> CreateAsync(GeolocationDto input)
    {
        var driver = ObjectMapper.Map<GeolocationDto, Geolocation>(input);
        var insertedEntity = await repository.InsertAsync(driver);
        return ObjectMapper.Map<Geolocation, GeolocationDto>(insertedEntity);
    }

    public async Task DeleteAsync(int id)
    {
        await repository.DeleteAsync(x => x.GeolocationId == id);
    }

    public async Task<GeolocationDto> GetAsync(int id)
    {
        var queryable = await repository.GetQueryableAsync();
        var geolocation = await queryable.Where(x => x.GeolocationId == id)
                                         .Include(x => x.EquipmentGeolocations)
                                         .FirstOrDefaultAsync();

        return geolocation is null ? throw new EntityNotFoundException() : ObjectMapper.Map<Geolocation, GeolocationDto>(geolocation);
    }

    public async Task<PagedResultDto<GeolocationDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await repository.GetQueryableAsync();
        var query = queryable
            .Include(x => x.EquipmentGeolocations)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .AsSingleQuery();

        var geolocations = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(query);

        return new PagedResultDto<GeolocationDto>(
            totalCount,
            ObjectMapper.Map<List<Geolocation>, List<GeolocationDto>>(geolocations)
        );
    }

    public Task<GeolocationDto> UpdateAsync(int id, GeolocationDto input)
    {
        throw new NotImplementedException();
    }
}
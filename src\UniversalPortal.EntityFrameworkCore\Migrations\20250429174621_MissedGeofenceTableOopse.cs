﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class MissedGeofenceTableOopse : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Addresses_Geofence_GeofenceId",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Geofence_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Geofence",
                schema: "dbo",
                table: "Geofence");

            migrationBuilder.RenameTable(
                name: "Geofence",
                schema: "dbo",
                newName: "Geofences",
                newSchema: "ControlTower");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Geofences",
                schema: "ControlTower",
                table: "Geofences",
                column: "GeofenceId");

            migrationBuilder.AddForeignKey(
                name: "FK_Addresses_Geofences_GeofenceId",
                schema: "ControlTower",
                table: "Addresses",
                column: "GeofenceId",
                principalSchema: "ControlTower",
                principalTable: "Geofences",
                principalColumn: "GeofenceId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Geofences_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "ControlTower",
                principalTable: "Geofences",
                principalColumn: "GeofenceId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Addresses_Geofences_GeofenceId",
                schema: "ControlTower",
                table: "Addresses");

            migrationBuilder.DropForeignKey(
                name: "FK_SourceSystems_Geofences_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Geofences",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.RenameTable(
                name: "Geofences",
                schema: "ControlTower",
                newName: "Geofence",
                newSchema: "dbo");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Geofence",
                schema: "dbo",
                table: "Geofence",
                column: "GeofenceId");

            migrationBuilder.AddForeignKey(
                name: "FK_Addresses_Geofence_GeofenceId",
                schema: "ControlTower",
                table: "Addresses",
                column: "GeofenceId",
                principalSchema: "dbo",
                principalTable: "Geofence",
                principalColumn: "GeofenceId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SourceSystems_Geofence_SourceSystemId",
                schema: "ControlTower",
                table: "SourceSystems",
                column: "SourceSystemId",
                principalSchema: "dbo",
                principalTable: "Geofence",
                principalColumn: "GeofenceId");
        }
    }
}

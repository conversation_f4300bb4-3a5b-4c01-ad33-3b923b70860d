﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class AddDwellTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Dwell",
                schema: "ControlTower",
                columns: table => new
                {
                    DwellId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GeofenceId = table.Column<int>(type: "int", nullable: false),
                    EquipmentId = table.Column<int>(type: "int", nullable: false),
                    GeolocationEnteyId = table.Column<int>(type: "int", nullable: false),
                    GeolocationDepartureId = table.Column<int>(type: "int", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Dwell", x => x.DwellId);
                    table.ForeignKey(
                        name: "FK_Dwell_Equipment_EquipmentId",
                        column: x => x.EquipmentId,
                        principalSchema: "ControlTower",
                        principalTable: "Equipment",
                        principalColumn: "EquipmentId",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Dwell_Geofences_GeofenceId",
                        column: x => x.GeofenceId,
                        principalSchema: "ControlTower",
                        principalTable: "Geofences",
                        principalColumn: "GeofenceId",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Dwell_Geolocations_GeolocationDepartureId",
                        column: x => x.GeolocationDepartureId,
                        principalSchema: "ControlTower",
                        principalTable: "Geolocations",
                        principalColumn: "GeolocationId");
                    table.ForeignKey(
                        name: "FK_Dwell_Geolocations_GeolocationEnteyId",
                        column: x => x.GeolocationEnteyId,
                        principalSchema: "ControlTower",
                        principalTable: "Geolocations",
                        principalColumn: "GeolocationId",
                        onDelete: ReferentialAction.NoAction);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Dwell_EquipmentId",
                schema: "ControlTower",
                table: "Dwell",
                column: "EquipmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Dwell_GeofenceId",
                schema: "ControlTower",
                table: "Dwell",
                column: "GeofenceId");

            migrationBuilder.CreateIndex(
                name: "IX_Dwell_GeolocationDepartureId",
                schema: "ControlTower",
                table: "Dwell",
                column: "GeolocationDepartureId",
                unique: true,
                filter: "[GeolocationDepartureId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Dwell_GeolocationEnteyId",
                schema: "ControlTower",
                table: "Dwell",
                column: "GeolocationEnteyId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Dwell",
                schema: "ControlTower");
        }
    }
}

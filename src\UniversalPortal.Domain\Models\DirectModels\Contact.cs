﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using UniversalPortal.Models.LinkedModels;
using UniversalPortal.Models.TenancyModels;

namespace UniversalPortal.Models.DirectModels;

public class Contact : TenantAuditedEntity
{
    public Contact()
    {
        OrderContacts = [];
        StopContacts = [];
        AddressContacts = [];
    }

    [Key]
    public int ContactId { get; set; }
    public int ContactNumber { get;set; }
    public required string Name { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }

    [InverseProperty(nameof(OrderContact.Contact))]
    public ICollection<OrderContact> OrderContacts { get; set; }
    [InverseProperty(nameof(StopContact.Contact))]
    public ICollection<StopContact> StopContacts { get; set; }
    [InverseProperty(nameof(AddressContact.Contact))]
    public ICollection<AddressContact> AddressContacts { get; set; }

    public override object[] GetKeys()
    {
        return [ContactId];
    }
}
﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UniversalPortal.Migrations
{
    /// <inheritdoc />
    public partial class AddGeofenceNameToTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            
            migrationBuilder.AddColumn<string>(
                name: "GeofenceName",
                schema: "ControlTower",
                table: "Geofences",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GeofenceName",
                schema: "ControlTower",
                table: "Geofences");

            migrationBuilder.AddColumn<int>(
                name: "SourceSystem",
                schema: "ControlTower",
                table: "Equipment",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}

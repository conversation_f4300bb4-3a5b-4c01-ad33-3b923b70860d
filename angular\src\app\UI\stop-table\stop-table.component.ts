import { trigger, transition, animate, style } from "@angular/animations";
import { Component, OnInit, AfterViewInit, ViewChild, Input } from "@angular/core";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort, Sort } from "@angular/material/sort";
import { MatTableDataSource, MatTable } from "@angular/material/table";
import { BehaviorSubject } from "rxjs";
import { StopCache } from "../../data/caches/stop.cache";
import { StopFormatter } from "../../data/formatters/stop.formatter";
import { StopRecord } from "../../data/records/stop.record";

@Component({
  selector: 'app-stop-table',
  styleUrl: 'stop-table.component.scss',
  templateUrl: 'stop-table.component.html',
  standalone: false,
  animations: [
    trigger('highlightChanges', [
      transition(':increment', [
        animate('100ms ease-out', style({ backgroundColor: 'yellow' })),
        animate('1200ms ease-in', style({ backgroundColor: '*' }))
      ]),
    ]),
    trigger('highlightNew', [
      transition('void => true', [
        animate('100ms ease-out', style({ backgroundColor: 'lime' })),
        animate('2400ms ease-in', style({ backgroundColor: '*' }))
      ]),
    ]),
    trigger('gridFade', [
      transition("* => *", [
        style({ opacity: 0 }),
        animate('150ms ease-in', style({ opacity: 1 }))
      ])
    ])
  ],
})
export class StopTableComponent implements OnInit, AfterViewInit {
  public inspectedRecord$: BehaviorSubject<StopRecord>;

  public lastRefresh: Date = null;

  private tableData = new MatTableDataSource<StopRecord>([]);

  @ViewChild('table') table: MatTable<StopRecord>
  @ViewChild('paginator') paginator: MatPaginator;
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  @ViewChild('tableContainerRef') tableContainerRef;

  @Input() public debugColumns: string[] = []; // Sticky columns to be shown first with garish debug formatting
  @Input() public stickyColumns: string[] = [];
  @Input() public regularColumns: string[] = [];
  public allColumns: string[] = [];

  // Enables an additional "inspect" sticky column containing buttons that emit their associated record through 'clickedRow$'
  @Input() public enableInspectButton: boolean = false;

  constructor(
    public dataCache: StopCache,
    public formatter: StopFormatter
  ) {
    this.inspectedRecord$ = new BehaviorSubject<StopRecord>(null);
  }

  ngOnInit() {
    this.dataCache.records$.subscribe((newRecords) => {
      this.tableData.data = newRecords;
      this.setRefreshTime("Data changed");
    })

    this.allColumns = this.debugColumns
      .concat(this.stickyColumns)
      .concat(this.enableInspectButton ? ['inspectButtonColumn'] : [])
      .concat(this.regularColumns);
  }

  ngAfterViewInit() {
    this.table.dataSource = this.tableData

    this.paginator.length = this.tableData.filteredData.length;
    this.paginator.pageSize = 10;

    this.tableData.paginator = this.paginator;
    this.tableData.sort = this.sort;
    this.tableData.sortingDataAccessor = (data: StopRecord, sortHeaderId: string) => data.getSortableField(sortHeaderId);
  }

  // ---

  /** Event handler for iterating the paginated data */
  public pageChange(event: PageEvent) {
    this.setRefreshTime("Page changed");
    console.log(`Page change: ${event.pageIndex}`);
  }

  /** Event handler for changing the sort order of a column */
  public sortChange(sort: Sort) {
    this.setRefreshTime("Sort changed");
    console.log(`Sorting by: ${sort.active} ${sort.direction}`);
  }

  /** Event handler for clicking the "inspect" button when displayed */
  public inspectRecord(rec: StopRecord): void {
    if (this.enableInspectButton) {
      this.inspectedRecord$.next(rec);
    }
  }

  /**
   * Event handler that calculates the age of the youngest record currently being displayed.
   * This value is saved and used as a threshold to determine whether received records are updates or inserts.
   */
  private setRefreshTime(why: string) {
    var time = new Date(Math.max.apply(Math, this.tableData.filteredData.map(x => x.lastUpdated)))
    if (time > this.lastRefresh) {
      this.lastRefresh = time;
      console.log(`Table refreshed (${why}): ${this.lastRefresh.toLocaleTimeString()}`);
    }
  }

  // ---

  /** Can be called externally to set a filter on the displayed records */
  public setFilter(selector: (datum: StopRecord) => string, filter: string): void {
    this.tableData.filterPredicate = (dat: StopRecord, filt: string) => selector(dat).includes(filt);
    this.tableData.filter = filter;
    this.setRefreshTime("Filter changed");
    console.log(`Filter change: ${filter}`);
  }

  public getFilteredStops(): StopRecord[] {
    return Array.from(this.tableData.filteredData);
  }

  /** Resets the table to an "inital" view: page 1, scrolled fully top/left */
  public resetPov(): void {
    this.tableData.paginator.firstPage();
    this.tableContainerRef.nativeElement.scroll(0, 0);
  }
}

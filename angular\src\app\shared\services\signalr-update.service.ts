import { Injectable } from '@angular/core';
import { HttpTransportType, HubConnection, HubConnectionBuilder, HubConnectionState, LogLevel } from "@microsoft/signalr";
import { Subject } from 'rxjs';
import { environment } from '../../../environments/environment'
import { AbstractUpdateService } from './abstract-update.service';
import { UpdateDto } from '../dtos/update.model';

@Injectable({
  providedIn: 'root'
})
export abstract class SignalRService<D> extends AbstractUpdateService<D> {

  public entityUpdate$ = new Subject<UpdateDto<D>>();
  public entityName: string;

  private static liveHubConnection: HubConnection = new HubConnectionBuilder()
    .configureLogging(LogLevel.Debug)
    .withUrl(`${environment.apis.default.url}/signalr-hubs/live-update`, {
      skipNegotiation: true,
      transport: HttpTransportType.WebSockets,
    })
    .withAutomaticReconnect()
    .build();

  private static pendingHubConnection: Promise<void> = SignalRService.liveHubConnection.start();

  constructor() {
    super();
  }

  public initialize(entityName: string) {
    this.entityName = entityName;
    SignalRService.pendingHubConnection.then(() => {
      this.initializeHubConnection(entityName);
    })
  }

  private initializeHubConnection(groupName: string) {
    SignalRService.pendingHubConnection.then(() => {
      SignalRService.liveHubConnection.invoke("JoinGroup", groupName).then(() => {
        console.log(`Successfully joined SignalR group '${groupName}'`);
      })

      SignalRService.liveHubConnection.onreconnected(() => {
        SignalRService.liveHubConnection.invoke("JoinGroup", groupName);
      });

      SignalRService.liveHubConnection.on("Publish", (msg: UpdateDto<D>, group: string) => {
        if (group == groupName && msg) {
          console.log(`Received updated ${groupName} record`, msg);
          this.entityUpdate$.next(msg);
          //(msg as UpdateDto<T>[]).forEach(x => this.entityUpdate$.next(x));
        }
      })
    })
  }
}

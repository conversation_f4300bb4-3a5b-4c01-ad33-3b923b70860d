import type { TenantAuditedEntity } from '../models';
import type { AddressContact, EquipmentGeolocation, OrderContact, OrderNote, PowerUnitGeolocation, StopContact, StopEquipment, StopNote } from '../linked-models/models';

export interface Address extends TenantAuditedEntity {
  addressId: number;
  addressLine1?: string;
  addressLine2?: string;
  displayName?: string;
  country?: string;
  state?: string;
  timeZone?: string;
  zip?: string;
  geofences: Geofence[];
  stops: Stop[];
  addressContacts: AddressContact[];
}

export interface Contact extends TenantAuditedEntity {
  contactId: number;
  contactNumber: number;
  name?: string;
  phoneNumber?: string;
  email?: string;
  orderContacts: OrderContact[];
  stopContacts: StopContact[];
  addressContacts: AddressContact[];
}

export interface Driver extends TenantAuditedEntity {
  driverId: number;
  driverNumber: number;
  name?: string;
  phoneNumber?: string;
  email?: string;
  driver1Legs: Leg[];
  driver2Legs: Leg[];
}

export interface Dwell extends TenantAuditedEntity {
  dwellId: number;
  geofenceId: number;
  equipmentId: number;
  geolocationEntryId: number;
  geolocationDepartureId?: number;
  creationTime?: string;
  geofence: Geofence;
  equipment: Equipment;
  entryGeolocation: Geolocation;
  departureGeolocation: Geolocation;
}

export interface Equipment extends TenantAuditedEntity {
  equipmentId: number;
  equipmentNumber?: string;
  equipmentTypeId: number;
  vin?: string;
  expirationDate?: string;
  description?: string;
  stopEquipment: StopEquipment[];
  equipmentType: EquipmentType;
  equipmentGeolocations: EquipmentGeolocation[];
  dwells: Dwell[];
}

export interface EquipmentType extends TenantAuditedEntity {
  equipmentTypeId: number;
  equipmentTypeName?: string;
  description?: string;
  equipment: Equipment[];
}

export interface Geofence extends TenantAuditedEntity {
  geofenceId: number;
  geolocationId: number;
  geofenceName?: string;
  addressId: number;
  geofenceCoordinates?: string;
  address: Address;
  geolocation: Geolocation;
  dwells: Dwell[];
}

export interface Geolocation extends TenantAuditedEntity {
  geolocationId: number;
  latitude: number;
  longitude: number;
  geolocationDate?: string;
  equipmentGeolocations: EquipmentGeolocation[];
  powerUnitGeolocations: PowerUnitGeolocation[];
  geofences: Geofence[];
  entryDwell: Dwell;
  departureDwell: Dwell;
}

export interface Leg extends TenantAuditedEntity {
  legId: number;
  legNumber: number;
  moveNumber: number;
  driver1Id?: number;
  driver2Id?: number;
  powerUnitId: number;
  stops: Stop[];
  driver1: Driver;
  driver2: Driver;
  powerUnit: PowerUnit;
}

export interface Note extends TenantAuditedEntity {
  noteId: number;
  content?: string;
  orderNotes: OrderNote[];
  stopNotes: StopNote[];
}

export interface Order extends TenantAuditedEntity {
  orderId: number;
  orderNumber: number;
  moveNumber: number;
  startDate?: string;
  route?: string;
  miles: number;
  companyId?: string;
  companyName?: string;
  orderContacts: OrderContact[];
  orderNotes: OrderNote[];
  stops: Stop[];
}

export interface PowerUnit extends TenantAuditedEntity {
  powerUnitId: number;
  vin?: string;
  powerUnitCode?: string;
  expirationDate?: string;
  legs: Leg[];
  powerUnitGeolocations: PowerUnitGeolocation[];
}

export interface Stop extends TenantAuditedEntity {
  stopId: number;
  addressId?: number;
  stopNumber: number;
  orderId: number;
  moveNumber: number;
  type?: string;
  sequence: number;
  mileage: number;
  scheduledEarliest?: string;
  arrival?: string;
  arrivalStatus: number;
  lateArrivalReason?: string;
  universalLateArrival: number;
  scheduledLatest?: string;
  departure?: string;
  departureStatus: number;
  lateDepartureReason?: string;
  universalLateDeparture: number;
  stopContacts: StopContact[];
  stopNotes: StopNote[];
  stopEquipment: StopEquipment[];
  order: Order;
  leg: Leg;
  address: Address;
}

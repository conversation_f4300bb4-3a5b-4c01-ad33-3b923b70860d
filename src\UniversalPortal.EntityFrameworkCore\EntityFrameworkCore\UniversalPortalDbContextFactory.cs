﻿using System.IO;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace UniversalPortal.EntityFrameworkCore;

/* This class is needed for EF Core console commands
 * (like Add-Migration and Update-Database commands) */
public class UniversalPortalDbContextFactory : IDesignTimeDbContextFactory<UniversalPortalDbContext>
{
    public UniversalPortalDbContext CreateDbContext(string[] args)
    {
        var configuration = BuildConfiguration();
        
        UniversalPortalEfCoreEntityExtensionMappings.Configure();

        var builder = new DbContextOptionsBuilder<UniversalPortalDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));
        
        return new UniversalPortalDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../UniversalPortal.DbMigrator/"))
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}

﻿using System.Collections.Generic;
using UniversalPortal.DTOs.DirectData;
using Volo.Abp.Application.Dtos;

namespace UniversalPortal.DTOs.LinkedData;

public class EquipmentGeolocationDto : AuditedEntityDto
{
    public EquipmentGeolocationDto()
    {
        Geolocation = [];
    }

    public int EquipmentId { get; set; }
    public int GeolocationId { get; set; }
    public ICollection<GeolocationDto> Geolocation { get; set; }
}
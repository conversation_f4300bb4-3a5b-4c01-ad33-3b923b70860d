﻿using Volo.Abp.Identity;

namespace UniversalPortal;

public static class UniversalPortalConsts
{
    public const string DbTablePrefix = "App";
    public const string? DbSchema = "dbo";
    public const string? ControlTowerSchema = "ControlTower";
    public const string AdminEmailDefaultValue = IdentityDataSeedContributor.AdminEmailDefaultValue;
    public const string AdminPasswordDefaultValue = IdentityDataSeedContributor.AdminPasswordDefaultValue;
}

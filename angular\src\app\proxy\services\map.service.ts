import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { MapFeaturesDto } from '../dtos/semantic-data/models';

@Injectable({
  providedIn: 'root',
})
export class MapService {
  apiName = 'Default';
  

  getMapFeaturesByOrderById = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MapFeaturesDto>({
      method: 'GET',
      url: `/api/app/map/${id}/map-features-by-order`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}

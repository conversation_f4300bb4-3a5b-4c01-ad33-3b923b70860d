﻿using Microsoft.AspNetCore.SignalR;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace UniversalPortal.Hubs;

public static class SignalRDebugger
{
    public static readonly HashSet<string> ConnectIDs = [];
    public static readonly ConcurrentDictionary<string, int> UserConnections = new();
}

public class LiveUpdateHub : Hub<IHubClient>
{
    public async Task PublishToGroup(dynamic message, string groupName)
    {
        await Clients.Group(groupName).Publish(message, groupName);
    }

    public async Task JoinGroup(string groupName)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
    }

    public async Task LeaveGroup(string groupName)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
    }

    public override async Task OnConnectedAsync()
    {
        SignalRDebugger.ConnectIDs.Add(Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        SignalRDebugger.ConnectIDs.Remove(Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }

    public void RegisterUser(int personnelAccessID)
    {
        SignalRDebugger.UserConnections.TryAdd(Context.ConnectionId, personnelAccessID);
    }
}

public interface IHubClient
{
    Task Publish(dynamic message, string groupName);
}
import { LocalizationService } from "@abp/ng.core";
import { formatDate } from "@angular/common";
import { Injectable, inject, LOCALE_ID } from "@angular/core";
import { LocalRecord } from "./local-record";

/**
 * Contains functions for formatting the fields of a record of type T.
 */
@Injectable({
  providedIn: 'root'
})
export abstract class RecordFormatter<R extends LocalRecord<D>, D> {

  protected static noValue: string = '';

  private locale: string = inject(LOCALE_ID);
  private localizationService: LocalizationService = inject(LocalizationService);

  public formatFieldName(fieldAccessor: string): string {
    return this.localizationService.instant({
      key: `::${fieldAccessor}`,
      defaultValue: `\{${fieldAccessor}\}`
    });
  }

  public formatField(fieldAccessor: string, record: R): string {
    if (this.fieldFormatters.has(fieldAccessor)) {
      return this.fieldFormatters.get(fieldAccessor)(record)?.toString() || RecordFormatter.noValue;
    }

    var [_, field] = fieldAccessor.split(':');

    if (this.fieldFormatters.has(field)) {
      return this.fieldFormatters.get(field)(record)?.toString() || RecordFormatter.noValue;
    }
    else if (record.localData[field] != undefined) {
      return record.localData[field].toString();
    }
    else if (record[field] != undefined) {
      return record[field].toString();
    }

    return RecordFormatter.noValue;
  }

  protected formatLocalDate(date: Date, format: string): string {
    if (!date) {
      return RecordFormatter.noValue;
    }
    else {
      return formatDate(date, format, this.locale);
    }
  }

  protected formatStaticValue(valueAccessor: string): string {
    return this.localizationService.instant({
      key: `::${valueAccessor as string}`,
      defaultValue: `\{${valueAccessor.toString()}\}`
    });
  }

  // Maps fields to functions that select a value from a T and format it into a string
  protected abstract fieldFormatters: Map<string, (rec: R) => string>;
}
